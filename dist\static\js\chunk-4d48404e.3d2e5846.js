(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4d48404e"],{"35e2":function(t,e,a){"use strict";a("3fa2")},"3fa2":function(t,e,a){},d884:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"商品ID："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品ID"},model:{value:t.tableFrom.proId,callback:function(e){t.$set(t.tableFrom,"proId",e)},expression:"tableFrom.proId"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:list"],expression:"['merchant:pay:component:product:list']"}],attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"商品名称："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称",clearable:""},model:{value:t.tableFrom.search,callback:function(e){t.$set(t.tableFrom,"search",e)},expression:"tableFrom.search"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["amerchant:pay:component:product:list"],expression:"['amerchant:pay:component:product:list']"}],attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"名称",prop:"title","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[a("div",{staticClass:"text_overflow",attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(e.row.title))]),t._v(" "),a("div",{staticClass:"pup_card"},[t._v(t._s(e.row.title))])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品图片","min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:JSON.parse(t.row.headImg)[0],"preview-src-list":JSON.parse(t.row.headImg)}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"componentProductId",label:"视频号商品ID","min-width":"150",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"类目","min-width":"150",prop:"thirdCatName",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"获得积分",prop:"giveIntegral","min-width":"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sales",label:"销量","min-width":"90",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"stock",label:"库存","min-width":"90",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sales",label:"状态","min-width":"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("videoStatusFilter")(e.row.status)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[11===e.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:puton"],expression:"['merchant:pay:component:product:puton']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.upChange(e.row.id)}}},[t._v("上架")]):t._e(),t._v(" "),5===e.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:putdown"],expression:"['merchant:pay:component:product:putdown']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.downChange(e.row.id)}}},[t._v("下架")]):t._e(),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:delete"],expression:"['merchant:pay:component:product:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleDelete(e.row.id,e.$index)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("div",{staticClass:"block mb20"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)],1)},l=[],i=a("b7be"),s={name:"videoList",data:function(){return{listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,proId:"",search:""}}},mounted:function(){this.getList()},methods:{upChange:function(t){var e=this;Object(i["o"])(t).then((function(){e.$message.success("上架成功"),e.getList()}))},downChange:function(t){var e=this;Object(i["m"])(t).then((function(){e.$message.success("下架成功"),e.getList()}))},handleDelete:function(t,e){var a=this;this.$modalSure("删除吗？此操作不可逆，请谨慎操作！").then((function(){Object(i["videoDelApi"])(t).then((function(){a.$message.success("删除成功"),a.tableData.data.splice(e,1)}))}))},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,Object(i["n"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()}}},r=s,o=(a("35e2"),a("2877")),c=Object(o["a"])(r,n,l,!1,null,"2f489c85",null);e["default"]=c.exports}}]);