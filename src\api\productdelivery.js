import request from '@/utils/request';

/**
 * 商品配送方式列表
 * @param pram
 */
export function getProductDeliveryList(params) {
  return request({
    url: '/admin/merchant/product/delivery/list',
    method: 'GET',
    params,
  });
}
/**
 * 新增商品配送方式
 * @param data
 */
export function addProductDelivery(data) {
  return request({
    url: '/admin/merchant/product/delivery/add',
    method: 'POST',
    data,
  });
}

/**
 * 根据ID删除商品配送方式
 * @param {number} id
 */
export function deleteProductDeliveryById(id) {
  return request({
    url: `/admin/merchant/product/delivery/delete/${id}`,
    method: 'POST',
  });
}

/**
 * 批量导入商品配送方式
 * @param {FormData} data - 包含文件和type
 */
export function batchImportProductDelivery(data) {
  return request({
    url: '/admin/merchant/product/delivery/import',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
/**
 * 导出商品配送方式（返回下载链接）
 * @param params 查询参数
 */
export function exportProductDelivery(params) {
  return request({
    url: '/admin/merchant/product/delivery/export',
    method: 'GET',
    params,
  });
}
