<template>
  <div class="category-carousel">
    <div class="header-btns">
      <el-button type="primary" size="small" @click="handleAdd">新增轮播图</el-button>
    </div>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px" size="small" v-loading="loading" class="mt20">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="imageUrl" label="轮播图">
        <template slot-scope="scope">
          <el-image
            style="width: 100px; height: 60px"
            :src="scope.row.imageUrl"
            :preview-src-list="[scope.row.imageUrl]"
          >
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="linkUrl" label="链接地址"></el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="block">
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="form" ref="form" :rules="rules" label-width="100px">
        <el-form-item label="轮播图" prop="file">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :before-upload="beforeUpload"
            :http-request="uploadFile"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="链接地址" prop="linkUrl">
          <el-input v-model="form.linkUrl" placeholder="请输入链接地址"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCategoryCarouselList,
  addCategoryCarousel,
  updateCategoryCarousel,
  deleteCategoryCarousel,
} from '@/api/categoryCarousel';
import { mapGetters } from 'vuex';

export default {
  name: 'CategoryCarouselList',
  data() {
    return {
      tableData: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      loading: false,
      dialogVisible: false,
      dialogTitle: '新增轮播图',
      imageUrl: '',
      form: {
        id: null,
        linkUrl: '',
        file: null,
      },
      rules: {
        linkUrl: [{ required: true, message: '请输入链接地址', trigger: 'blur' }],
      },
    };
  },
  created() {
    this.getList();
  },
  computed: {
    ...mapGetters(['merId']),
  },
  mounted() {
    console.log('当前登录用户：', this.merId);
    console.log('全局state:', this.$store.state);
  },
  methods: {
    // 获取列表
    getList() {
      this.loading = true;
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        merchantId: this.merId, // 新增，带上当前登录用户的merId
      };
      getCategoryCarouselList(params)
        .then((res) => {
          this.tableData = res.list;
          this.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 新增
    handleAdd() {
      this.dialogTitle = '新增轮播图';
      this.form = {
        id: null,
        linkUrl: '',
        file: null,
        merId: this.merId,
      };
      this.imageUrl = '';
      this.dialogVisible = true;
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑轮播图';
      this.form = {
        id: row.id,
        linkUrl: row.linkUrl,
        file: null,
        merId: row.merId,
      };
      this.imageUrl = row.imageUrl;
      this.dialogVisible = true;
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该轮播图?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          deleteCategoryCarousel(row.id).then(() => {
            this.$message.success('删除成功');
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 上传前校验
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    // 自定义上传
    uploadFile(params) {
      this.form.file = params.file;
      this.imageUrl = URL.createObjectURL(params.file);
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const formData = new FormData();
          if (this.form.id) {
            formData.append('id', this.form.id);
          }
          formData.append('linkUrl', this.form.linkUrl);
          if (this.form.file) {
            formData.append('file', this.form.file);
          }
          // 新增时增加merId参数
          if (!this.form.id) {
            formData.append('merId', this.merId);
          }

          const request = this.form.id ? updateCategoryCarousel : addCategoryCarousel;
          request(formData).then(() => {
            this.$message.success(this.form.id ? '编辑成功' : '新增成功');
            this.dialogVisible = false;
            this.getList();
          });
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.category-carousel {
  background: #fff;
  padding: 24px;

  .header-btns {
    margin-bottom: 20px;
  }

  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;

    &:hover {
      border-color: #409eff;
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .block {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
