(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0de0816e"],{"284e":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"divBox relative"},[t("el-card",{staticClass:"box-card"},[t("div",{ref:"tableheader",staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("el-tabs",{model:{value:e.loginType,callback:function(a){e.loginType=a},expression:"loginType"}},e._l(e.headeNum,(function(e,a){return t("el-tab-pane",{key:a,attrs:{label:e.name,name:e.type.toString()}})})),1),e._v(" "),"1"===e.loginType?t("div",{staticClass:"information"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"basic-information"},[t("div",[e._v("商户名称："+e._s(e.merData.name))]),e._v(" "),t("div",[e._v("商户登录帐号："+e._s(e.merData.phone))]),e._v(" "),t("div",[e._v("商户负责人姓名："+e._s(e.merData.realName))]),e._v(" "),t("div",[e._v("商户分类："+e._s(e.merData.merCategory))]),e._v(" "),t("div",[e._v("商户类别："+e._s(e._f("selfTypeFilter")(e.merData.isSelf)))]),e._v(" "),t("div",[e._v("店铺类型："+e._s(e.merData.merType))]),e._v(" "),t("div",[e._v("商户手续费："+e._s(e.merData.handlingFee)+"%")]),e._v(" "),t("div",[e._v("添加商品："+e._s(e.merData.productSwitch?"需平台审核":"平台免审核"))]),e._v(" "),t("div",[e._v("商户星级："),t("el-rate",{attrs:{disabled:"","text-color":"#ff9900"},model:{value:e.merData.starLevel,callback:function(a){e.$set(e.merData,"starLevel",a)},expression:"merData.starLevel"}})],1),e._v(" "),t("div",[e._v("商户入驻时间："+e._s(e.merData.createTime))]),e._v(" "),t("div",[e._v("\n            商户二维码：\n            "),t("div",{staticClass:"acea-row"},[t("el-image",{staticClass:"mp-qr-code",attrs:{src:e.mpQRCode,"preview-src-list":[e.mpQRCode]}})],1)]),e._v(" "),e.merData.qualificationPicture?t("div",[e._v("\n            商户资质：\n            "),t("div",{staticClass:"acea-row"},e._l(JSON.parse(e.merData.qualificationPicture),(function(e,a){return t("div",{key:a,staticClass:"pictrue"},[t("el-image",{attrs:{src:e,"preview-src-list":[e]}})],1)})),0)]):e._e(),e._v(" "),e.checkPermi(["merchant:switch:update"])?t("div",[e._v("\n            开启商户：\n            "),t("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},on:{change:e.changeSwitch},model:{value:e.merData.isSwitch,callback:function(a){e.$set(e.merData,"isSwitch",a)},expression:"merData.isSwitch"}})],1):e._e()])]):e._e(),e._v(" "),"2"===e.loginType?t("div",{staticClass:"business-msg"},[t("div",{staticClass:"form-data"},[t("el-form",{ref:"merInfoForm",staticClass:"demo-ruleForm",attrs:{model:e.merInfoForm,rules:e.rules,"label-width":"140px"}},[t("el-form-item",{attrs:{label:"商户主头像：",prop:"avatar"}},[t("div",{staticClass:"upLoadPicBox acea-row",on:{click:function(a){return e.modalPicTap("1","avatar")}}},[e.merInfoForm.avatar?t("div",{staticClass:"pictrue"},[t("img",{attrs:{src:e.merInfoForm.avatar}})]):t("div",{staticClass:"upLoad"},[t("i",{staticClass:"el-icon-camera cameraIconfont"})]),e._v(" "),t("span",[e._v("请上传小于500kb的图片（90*90 px）")])])]),e._v(" "),t("el-form-item",{attrs:{label:"H5商户背景图：",prop:"backImage"}},[t("div",{staticClass:"upLoadPicBox acea-row",on:{click:function(a){return e.modalPicTap("1","backImage")}}},[e.merInfoForm.backImage?t("div",{staticClass:"pictrue"},[t("img",{attrs:{src:e.merInfoForm.backImage}})]):t("div",{staticClass:"upLoad"},[t("i",{staticClass:"el-icon-camera cameraIconfont"})]),e._v(" "),t("span",[e._v("请上传小于500kb的图片（375*180 px）")])])]),e._v(" "),t("el-form-item",{attrs:{label:"H5商户街背景图：",prop:"streetBackImage"}},[t("div",{staticClass:"upLoadPicBox acea-row",on:{click:function(a){return e.modalPicTap("1","streetBackImage")}}},[e.merInfoForm.streetBackImage?t("div",{staticClass:"pictrue"},[t("img",{attrs:{src:e.merInfoForm.streetBackImage}})]):t("div",{staticClass:"upLoad"},[t("i",{staticClass:"el-icon-camera cameraIconfont"})]),e._v(" "),t("span",[e._v("请上传小于500kb的图片（355*78 px）")])])]),e._v(" "),t("el-form-item",{attrs:{label:"H5商户封面图：",prop:"coverImage"}},[t("div",{staticClass:"upLoadPicBox acea-row",on:{click:function(a){return e.modalPicTap("1","coverImage")}}},[e.merInfoForm.coverImage?t("div",{staticClass:"pictrue"},[t("img",{attrs:{src:e.merInfoForm.coverImage}})]):t("div",{staticClass:"upLoad"},[t("i",{staticClass:"el-icon-camera cameraIconfont"})]),e._v(" "),t("span",[e._v("请上传小于500kb的图片（350*350 px）")])])]),e._v(" "),t("el-form-item",{attrs:{label:"H5商户logo（横）：",prop:"rectangleLogo"}},[t("div",{staticClass:"upLoadPicBox acea-row",on:{click:function(a){return e.modalPicTap("1","rectangleLogo")}}},[e.merInfoForm.rectangleLogo?t("div",{staticClass:"pictrue"},[t("img",{attrs:{src:e.merInfoForm.rectangleLogo}})]):t("div",{staticClass:"upLoad"},[t("i",{staticClass:"el-icon-camera cameraIconfont"})]),e._v(" "),t("span",[e._v("请上传小于500kb的图片（300*88 px）")])])]),e._v(" "),t("el-form-item",{attrs:{label:"商户简介：",prop:"intro"}},[t("el-input",{staticClass:"width100",attrs:{type:"textarea"},model:{value:e.merInfoForm.intro,callback:function(a){e.$set(e.merInfoForm,"intro",a)},expression:"merInfoForm.intro"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"商户关键字：",prop:"labelarr"}},[t("keyword",{staticClass:"width100",attrs:{labelarr:e.labelarr},on:{getLabelarr:e.getLabelarr}})],1),e._v(" "),t("el-form-item",{attrs:{label:"客服类型：",prop:"serviceType"}},[t("el-select",{staticClass:"width100",attrs:{placeholder:"请选择"},model:{value:e.merInfoForm.serviceType,callback:function(a){e.$set(e.merInfoForm,"serviceType",a)},expression:"merInfoForm.serviceType"}},e._l(e.serviceList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),"H5"===e.merInfoForm.serviceType?t("el-form-item",{attrs:{label:"H5链接：",prop:"serviceLink"}},[t("el-input",{staticClass:"width100",model:{value:e.merInfoForm.serviceLink,callback:function(a){e.$set(e.merInfoForm,"serviceLink",a)},expression:"merInfoForm.serviceLink"}})],1):e._e(),e._v(" "),"phone"===e.merInfoForm.serviceType?t("el-form-item",{attrs:{label:"电话：",prop:"servicePhone"}},[t("el-input",{staticClass:"width100",model:{value:e.merInfoForm.servicePhone,callback:function(a){e.$set(e.merInfoForm,"servicePhone",a)},expression:"merInfoForm.servicePhone"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"警戒库存：",prop:"alertStock"}},[t("el-input-number",{attrs:{min:1,max:10,label:"警戒库存"},model:{value:e.merInfoForm.alertStock,callback:function(a){e.$set(e.merInfoForm,"alertStock",a)},expression:"merInfoForm.alertStock"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"自提开关：",prop:"alertStock"}},[t("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},model:{value:e.merInfoForm.isTakeTheir,callback:function(a){e.$set(e.merInfoForm,"isTakeTheir",a)},expression:"merInfoForm.isTakeTheir"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"商户地址：",prop:"addressDetail"}},[t("el-input",{staticClass:"width100",attrs:{"enter-button":"查找位置",placeholder:"请查找位置",readonly:""},model:{value:e.merInfoForm.addressDetail,callback:function(a){e.$set(e.merInfoForm,"addressDetail",a)},expression:"merInfoForm.addressDetail"}}),e._v(" "),t("iframe",{attrs:{id:"mapPage",width:"100%",height:"500px",frameborder:"0",src:e.keyUrl}})],1),e._v(" "),e.checkPermi(["merchant:config:info:edit"])?t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(a){return e.handlerSubmit("merInfoForm")}}},[e._v("确定")])],1):e._e()],1)],1)]):e._e(),e._v(" "),"3"===e.loginType?t("div",{staticClass:"business-msg"},[t("div",{staticClass:"form-data"},[t("el-form",{ref:"settlementForm",attrs:{model:e.settlementForm,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"结算类型"}},[t("el-radio-group",{model:{value:e.settlementForm.settlementType,callback:function(a){e.$set(e.settlementForm,"settlementType",a)},expression:"settlementForm.settlementType"}},[t("el-radio",{attrs:{label:"bank"}},[e._v("银行卡")]),e._v(" "),t("el-radio",{attrs:{label:"wechat"}},[e._v("微信")]),e._v(" "),t("el-radio",{attrs:{label:"alipay"}},[e._v("支付宝")])],1)],1)],1),e._v(" "),"3"===e.loginType?t("z-b-parser",{attrs:{"is-create":1,"form-conf":e.formConf,"edit-data":e.transferData,"form-id":e.formId,"key-num":e.keyNum},on:{submit:e.transferhandlerSubmit,resetForm:e.resetForm}}):e._e()],1)]):e._e()],1)])],1)},i=[],s=t("8492"),o=t("e350"),n=(t("5f87"),t("61f7")),l={name:"Information",data:function(){var e=this,a=function(e,a,t){if(""===a)t(new Error("请输入客服电话"));else{var r=null,i=/^1(3|4|5|6|7|8|9)\d{9}$/,s=/^(0[0-9]{2,3}\-)([2-9][0-9]{4,7})+(\-[0-9]{1,4})?$/;if(r=0==a.charAt(0)?s:i,!r.test(a))return t(new Error("请填写客服电话(座机格式'区号-座机号码')"));t()}},t=function(a,t,r){0===e.labelarr.length?r(new Error("请输入后回车")):r()};return{formConf:{fields:[]},isCreate:0,loginType:"1",headeNum:[{type:"1",name:"商户信息"},{type:"2",name:"店铺信息"},{type:"3",name:"结算信息"}],merData:{},mpQRCode:"",submitLoading:!1,editData:{},transferData:{},keyNum:0,loading:!1,merInfoForm:{avatar:"",backImage:"",streetBackImage:"",rectangleLogo:"",coverImage:"",intro:"",keywords:"",alertStock:1,addressDetail:"",serviceType:"",serviceLink:null,servicePhone:"",latitude:"",longitude:"",isTakeTheir:!1},rules:{intro:[{required:!0,message:"请输入商户简介",trigger:"blur"}],avatar:[{required:!0,message:"请上传商户主头像",trigger:"change"}],backImage:[{required:!0,message:"请上传H5商户背景图",trigger:"change"}],streetBackImage:[{required:!0,message:"请上传H5商户街背景图",trigger:"change"}],coverImage:[{required:!0,message:"请上传商户封面图",trigger:"change"}],rectangleLogo:[{required:!0,message:"请上传商户logo（横）",trigger:"change"}],labelarr:[{required:!0,validator:t,trigger:"blur"}],alertStock:[{required:!0,message:"请输入警戒库存",trigger:"blur"}],serviceType:[{required:!0,message:"请选择客服类型",trigger:"change"}],serviceLink:[{required:!0,message:"请输入H5链接",trigger:"blur"}],servicePhone:[{required:!0,validator:a,trigger:"blur"}],addressDetail:[{required:!0,message:"请选择商户地址",trigger:"blur"}]},keyUrl:"",labelarr:[],serviceList:[{value:"H5",label:"H5链接"},{value:"phone",label:"电话"}],settlementForm:{settlementType:"bank"},formId:36}},watch:{"settlementForm.settlementType":{handler:function(e){switch(e){case"bank":this.formId=36;break;case"wechat":this.formId=40;break;default:this.formId=39;break}this.keyNum+=1},immediate:!1,deep:!0}},created:function(){this.getInfo(),this.getConfigInfo(),this.getMerchantTransfer()},mounted:function(){window.addEventListener("message",(function(e){var a=e.data;a&&"locationPicker"===a.module&&window.parent.selectAdderss(a)}),!1),window.selectAdderss=this.selectAdderss,this.keyUrl="https://apis.map.qq.com/tools/locpicker?type=1&key=RFZBZ-EF2AB-YVPUO-NW6H3-6L4D6-5DBHU&referer=OOSEEK"},methods:{checkPermi:o["a"],getLabelarr:function(e){this.labelarr=e},modalPicTap:function(e,a){var t=this;this.$modalUpload((function(e){if(e)switch(a){case"avatar":t.merInfoForm.avatar=e[0].sattDir;break;case"backImage":t.merInfoForm.backImage=e[0].sattDir;break;case"rectangleLogo":t.merInfoForm.rectangleLogo=e[0].sattDir;break;case"coverImage":t.merInfoForm.coverImage=e[0].sattDir;break;default:t.merInfoForm.streetBackImage=e[0].sattDir;break}}),e,"content")},selectAdderss:function(e){this.merInfoForm.addressDetail=e.poiaddress+e.poiname,this.merInfoForm.latitude=e.latlng.lat,this.merInfoForm.longitude=e.latlng.lng},changeSwitch:function(){var e=this,a=this.merData.isSwitch?"开启":"关闭";this.$modalSure("".concat(a,"该商户吗")).then((function(){Object(s["d"])().then((function(a){e.$modal.msgSuccess("修改成功")})).catch((function(){e.merData.isSwitch=!e.merData.isSwitch}))})).catch((function(){e.merData.isSwitch=!e.merData.isSwitch}))},handlerSubmit:Object(n["a"])((function(e){var a=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;Object(s["h"])(a.merInfoForm).then((function(e){a.$message.success("操作成功"),a.getConfigInfo()}))}))})),transferhandlerSubmit:Object(n["a"])((function(e){var a=this;Object(s["f"])({id:this.formId,settlementType:this.settlementForm.settlementType,alipayCode:e.alipayCode,alipayQrcodeUrl:e.alipayQrcodeUrl,bankAddress:e.bankAddress,bankCard:e.bankCard,bankName:e.bankName,bankUserName:e.bankUserName,wechatCode:e.wechatCode,wechatQrcodeUrl:e.wechatQrcodeUrl,realName:e.realName}).then((function(e){a.$message.success("操作成功"),a.getMerchantTransfer()}))})),getInfo:function(){var e=this;this.loading=!0,Object(s["a"])().then((function(a){return e.merData=a,Object(s["i"])({mid:a.id,path:"pages/index/index"})})).then((function(a){e.mpQRCode=a.code,e.loading=!1})).catch((function(){e.loading=!1}))},getConfigInfo:function(){var e=this;Object(s["c"])().then((function(a){e.merInfoForm=a,e.labelarr=a.keywords.split(",")||[]}))},getMerchantTransfer:function(){var e=this;Object(s["e"])().then((function(a){e.transferData=a,e.settlementForm=a}))}}},c=l,m=(t("ae6f"),t("2877")),d=Object(m["a"])(c,r,i,!1,null,"********",null);a["default"]=d.exports},2909:function(e,a,t){},ae6f:function(e,a,t){"use strict";t("2909")}}]);