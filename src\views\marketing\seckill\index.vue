<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <div class="clearfix">
        <div class="container">
          <el-form size="small" label-width="100px">
            <el-form-item label="选择时间：" class="width100 mb10">
              <el-radio-group
                v-model="tableFrom.dateLimit"
                type="button"
                class="mr20"
                size="small"
                @change="selectChange(tableFrom.dateLimit)"
              >
                <el-radio-button v-for="(item, i) in fromList.fromTxt" :key="i" :label="item.val">{{
                  item.text
                }}</el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-model="timeVal"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                size="small"
                type="daterange"
                placement="bottom-end"
                start-placeholder="开始日期"
                end-placeholder="结束时间"
                style="width: 220px"
                @change="onchangeTime"
              />
            </el-form-item>
            <el-row :gutter="20" style="max-width: 800px">
              <el-col :span="10">
                <el-form-item label="活动编号：" class="mb10">
                  <el-input
                    v-model="tableFrom.activityNo"
                    placeholder="请输入活动编号"
                    size="small"
                    clearable
                    style="width: 200px"
                    @keyup.enter.native="seachList"
                    @clear="seachList"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="活动名称：" class="mb10">
                  <el-input
                    v-model="tableFrom.name"
                    placeholder="请输入活动名称"
                    size="small"
                    clearable
                    style="width: 200px"
                    @keyup.enter.native="seachList"
                    @clear="seachList"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-button type="primary" size="small" icon="el-icon-search" @click="seachList">搜索</el-button>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="max-width: 800px">
              <el-col :span="10">
                <el-form-item label="后台名称：" class="mb10">
                  <el-input
                    v-model="tableFrom.backName"
                    placeholder="请输入后台活动名称"
                    size="small"
                    clearable
                    style="width: 200px"
                    @keyup.enter.native="seachList"
                    @clear="seachList"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="活动状态：" class="mb10">
                  <el-select
                    v-model="tableFrom.isOpen"
                    placeholder="请选择活动状态"
                    size="small"
                    clearable
                    style="width: 200px"
                    @clear="seachList"
                    @change="seachList"
                  >
                    <el-option label="未开始" value="0" />
                    <el-option label="进行中" value="1" />
                    <el-option label="已结束" value="2" />
                    <el-option label="已暂停" value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-row :gutter="20" style="max-width: 800px">
              <el-col :span="10">
                <el-form-item label="活动归属：" class="mb10">
                  <el-select
                    v-model="tableFrom.belongTo"
                    placeholder="请选择活动归属"
                    size="small"
                    clearable
                    style="width: 200px"
                    @clear="seachList"
                    @change="seachList"
                  >
                    <el-option label="平台活动" value="platform" />
                    <el-option label="商户活动" value="merchant" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="创建人：" class="mb10">
                  <el-input
                    v-model="tableFrom.createBy"
                    placeholder="请输入创建人"
                    size="small"
                    clearable
                    style="width: 200px"
                    @keyup.enter.native="seachList"
                    @clear="seachList"
                  />
                </el-form-item>
              </el-col>
            </el-row> -->
          </el-form>
        </div>
      </div>
    </el-card>
    <el-card class="box-card mt10">
      <div class="mb10">
        <el-button type="primary" size="small" style="width: 120px" @click="addActivity">新建活动</el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="mini"
        class="table"
        highlight-current-row
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column prop="id" label="ID" min-width="60" />
        <el-table-column prop="activityNo" label="活动编号" min-width="120" />
        <el-table-column prop="name" label="活动名称" min-width="120" />
        <el-table-column prop="backName" label="后台活动名称" min-width="140" />
        <el-table-column prop="instruction" label="活动简介" min-width="150" show-overflow-tooltip />
        <el-table-column label="活动时间" min-width="200">
          <template slot-scope="scope">
            {{ formatTime(scope.row.beginTime) }} 至 {{ formatTime(scope.row.overTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isOpen === '0'" type="warning">未开始</el-tag>
            <el-tag v-else-if="scope.row.isOpen === '1'" type="success">进行中</el-tag>
            <el-tag v-else-if="scope.row.isOpen === '2'" type="info">已结束</el-tag>
            <el-tag v-else-if="scope.row.isOpen === '3'" type="danger">已暂停</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createByName" label="创建人" min-width="100" />
        <el-table-column prop="belongTo" label="活动归属" min-width="100" />
        <el-table-column prop="createTime" label="创建时间" min-width="150" />
        <el-table-column label="操作" min-width="150" fixed="right" align="center">
          <template slot-scope="scope">
            <!-- 未开始：修改/详情 -->
            <template v-if="scope.row.isOpen === '0'">
              <el-button type="text" size="small" class="mr10" @click="handleEdit(scope.row)">修改</el-button>
              <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
            <!-- 进行中：暂停/详情 -->
            <template v-else-if="scope.row.isOpen === '1'">
              <el-button
                type="text"
                size="small"
                class="mr10"
                v-hasPermi="['platform:marketing:activity:update:status']"
                @click="handleStop(scope.row)"
                >暂停</el-button
              >
              <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
            <!-- 已暂停：开始/详情 -->
            <template v-else-if="scope.row.isOpen === '3'">
              <el-button
                type="text"
                size="small"
                class="mr10"
                v-hasPermi="['platform:marketing:activity:update:status']"
                @click="handleStart(scope.row)"
                >开始</el-button
              >
              <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
            <!-- 已结束：详情 -->
            <template v-else-if="scope.row.isOpen === '2'">
              <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | OOSEEK [ OOSEEK赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
import { discountListApi, discountStatusApi, discountDeleteApi } from '@/api/discount';

export default {
  name: 'discountActivity',
  data() {
    return {
      tableData: {
        data: [],
        total: 0,
      },
      listLoading: true,
      tableFrom: {
        activityNo: '',
        name: '',
        backName: '',
        type: '2', // 秒杀活动
        isOpen: '',
        belongTo: '',
        createBy: '',
        dateLimit: '',
        timeStart: '',
        timeEnd: '',
        page: 1,
        limit: 20,
      },
      timeVal: [],
      fromList: this.$constants.fromList,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    /**
     * 搜索列表
     */
    seachList() {
      this.tableFrom.page = 1;
      this.getList();
    },
    /**
     * 时间选择变化
     * @param {Array} e - 时间范围
     */
    onchangeTime(e) {
      this.timeVal = e;
      if (e && e.length === 2) {
        this.tableFrom.timeStart = e[0];
        this.tableFrom.timeEnd = e[1];
        // 清空快捷时间选择
        this.tableFrom.dateLimit = '';
      } else {
        this.tableFrom.timeStart = '';
        this.tableFrom.timeEnd = '';
      }
      this.tableFrom.page = 1;
      this.getList();
    },
    /**
     * 获取活动列表
     */
    getList() {
      this.listLoading = true;

      // 构建请求参数
      const params = {
        page: this.tableFrom.page,
        limit: this.tableFrom.limit,
        type: this.tableFrom.type,
      };

      // 添加非空参数
      if (this.tableFrom.activityNo) {
        params.activityNo = this.tableFrom.activityNo;
      }
      if (this.tableFrom.name) {
        params.name = this.tableFrom.name;
      }
      if (this.tableFrom.backName) {
        params.backName = this.tableFrom.backName;
      }
      if (this.tableFrom.isOpen !== '') {
        params.isOpen = this.tableFrom.isOpen;
      }
      if (this.tableFrom.belongTo) {
        params.belongTo = this.tableFrom.belongTo;
      }
      if (this.tableFrom.createBy) {
        params.createBy = this.tableFrom.createBy;
      }
      // 优先使用具体的时间参数
      if (this.tableFrom.timeStart) {
        params.timeStart = this.tableFrom.timeStart;
      }
      if (this.tableFrom.timeEnd) {
        params.timeEnd = this.tableFrom.timeEnd;
      }
      // 移除 dateLimit 参数传递，后端不支持
      // if (!this.tableFrom.timeStart && !this.tableFrom.timeEnd && this.tableFrom.dateLimit) {
      //   params.dateLimit = this.tableFrom.dateLimit;
      // }

      discountListApi(params)
        .then((data) => {
          // console.log('接口返回数据:', data);
          this.tableData.data = data.list || [];
          this.tableData.total = data.total || 0;
          this.listLoading = false;
        })
        .catch((error) => {
          console.error('获取活动列表失败:', error);
          this.$message.error('获取数据失败');
          this.tableData.data = [];
          this.tableData.total = 0;
          this.listLoading = false;
        });
    },
    /**
     * 分页变化
     * @param {Number} page - 页码
     */
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    /**
     * 每页数量变化
     * @param {Number} val - 每页数量
     */
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    /**
     * 新建活动
     */
    addActivity() {
      this.$router.push({ path: '/marketing/seckill/add' });
    },
    /**
     * 编辑活动
     * @param {Object} row - 行数据
     */
    handleEdit(row) {
      this.$router.push({ path: `/marketing/seckill/edit/${row.id}` });
    },
    /**
     * 查看详情
     * @param {Object} row - 行数据
     */
    handleDetail(row) {
      this.$router.push({ path: `/marketing/seckill/detail/${row.id}` });
    },
    /**
     * 开始活动
     * @param {Object} row - 行数据
     */
    // 开始活动
    handleStart(row) {
      this.$confirm('确定要开始该活动吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 确保传递了 id 和 status 参数
          discountStatusApi({
            id: row.id,
            status: 1,
          }).then(() => {
            this.$message.success('操作成功');
            this.getList();
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    /**
     * 暂停活动
     * @param {Object} row - 行数据
     */
    // 暂停活动
    handleStop(row) {
      this.$confirm('确定要暂停该活动吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 确保传递了 id 和 status 参数
          discountStatusApi({
            id: row.id,
            status: 3,
          }).then(() => {
            this.$message.success('操作成功');
            this.getList();
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    /**
     * 删除活动
     * @param {Object} row - 行数据
     */
    handleDelete(row) {
      this.$confirm('确定要删除该活动吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          discountDeleteApi({ id: row.id }).then(() => {
            this.$message.success('删除成功');
            this.getList();
          });
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },
    /**
     * 时间快捷选择
     * @param {String} val - 选择的时间范围
     */
    selectChange(val) {
      // 保留 dateLimit 用于按钮选中状态
      this.tableFrom.dateLimit = val;

      // 当选择快捷时间时，清空自定义时间选择
      if (val) {
        this.timeVal = [];

        // 根据快捷选择计算具体的时间范围
        const today = new Date();
        const formatDate = (date) => {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        };

        switch (val) {
          case 'today':
            this.tableFrom.timeStart = formatDate(today) + ' 00:00:00';
            this.tableFrom.timeEnd = formatDate(today) + ' 23:59:59';
            break;
          case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            this.tableFrom.timeStart = formatDate(yesterday) + ' 00:00:00';
            this.tableFrom.timeEnd = formatDate(yesterday) + ' 23:59:59';
            break;
          case 'lately7':
            // 最近7天：前7天到后7天（总共14天）
            const sevenDaysBefore = new Date(today);
            sevenDaysBefore.setDate(today.getDate() - 7);
            const sevenDaysAfter = new Date(today);
            sevenDaysAfter.setDate(today.getDate() + 7);
            this.tableFrom.timeStart = formatDate(sevenDaysBefore) + ' 00:00:00';
            this.tableFrom.timeEnd = formatDate(sevenDaysAfter) + ' 23:59:59';
            break;
          case 'lately30':
            // 最近30天：前30天到后30天（总共60天）
            const thirtyDaysBefore = new Date(today);
            thirtyDaysBefore.setDate(today.getDate() - 30);
            const thirtyDaysAfter = new Date(today);
            thirtyDaysAfter.setDate(today.getDate() + 30);
            this.tableFrom.timeStart = formatDate(thirtyDaysBefore) + ' 00:00:00';
            this.tableFrom.timeEnd = formatDate(thirtyDaysAfter) + ' 23:59:59';
            break;
          case 'month':
            // 本月：当月1号到当月最后一天
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            this.tableFrom.timeStart = formatDate(firstDayOfMonth) + ' 00:00:00';
            this.tableFrom.timeEnd = formatDate(lastDayOfMonth) + ' 23:59:59';
            break;
          case 'year':
            // 本年：当年1月1号到12月31号
            const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
            const lastDayOfYear = new Date(today.getFullYear(), 11, 31);
            this.tableFrom.timeStart = formatDate(firstDayOfYear) + ' 00:00:00';
            this.tableFrom.timeEnd = formatDate(lastDayOfYear) + ' 23:59:59';
            break;
          default:
            this.tableFrom.timeStart = '';
            this.tableFrom.timeEnd = '';
        }
      } else {
        this.tableFrom.timeStart = '';
        this.tableFrom.timeEnd = '';
      }
      this.seachList();
    },
    /**
     * 格式化时间，去除毫秒部分
     * @param {String} timeStr - 时间字符串
     * @returns {String} 格式化后的时间
     */
    formatTime(timeStr) {
      if (!timeStr) return '';
      // 去除毫秒部分，只保留到秒
      return timeStr.replace(/\.\d+$/, '');
    },
  },
};
</script>

<style lang="scss" scoped>
.relative {
  position: relative;
}
</style>
