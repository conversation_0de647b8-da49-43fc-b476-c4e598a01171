<template>
  <div class="infoBox">
    <el-drawer
      :visible.sync="dialogVisible"
      title="商品详情"
      :direction="direction"
      :append-to-body="true"
      custom-class="demo-drawer"
      size="1000px"
      ref="drawer"
      :wrapperClosable="true"
      :modal-append-to-body="false"
      class="infoBox"
      @close="close"
    >
      <div class="demo-drawer__content">
        <div class="divBox" v-if="formValidate">
          <div slot="header" class="clearfix">
            <el-tabs v-model="currentTab">
              <el-tab-pane label="商品信息" name="0"></el-tab-pane>
              <el-tab-pane label="商品详情" name="1"></el-tab-pane>
              <el-tab-pane label="其他设置" name="2"></el-tab-pane>
            </el-tabs>
          </div>

          <div v-loading="loading" class="content">
            <div v-show="currentTab === '0'">
              <!-- 商品信息-->

              <el-row :gutter="24">
                <el-col v-bind="grid2">
                  <div class="title">商品名称：</div>
                  <div class="value">{{ formValidate.name }}</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">平台商品分类：</div>
                  <div class="value">{{ adminCategory }}</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">品牌：</div>
                  <div class="value">{{ formValidate.brandName }}</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">商品关键字：</div>
                  <div class="value">{{ formValidate.keyword }}</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">单位：</div>
                  <div class="value">{{ formValidate.unitName }}</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">商品简介：</div>
                  <div class="value">{{ formValidate.intro }}</div>
                </el-col>

                <el-col :span="24" class="mb10">
                  <div class="title">商品封面图：</div>
                  <div class="acea-row" v-if="formValidate.image">
                    <div class="pictrue">
                      <el-image :src="formValidate.image" :preview-src-list="[formValidate.image]"> </el-image>
                    </div>
                  </div>
                  <div v-else>无</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">保障服务：</div>
                  <div class="value">
                    <el-tag v-for="item in guarantees" :key="item.id" style="margin-right: 8px">
                      {{ item.name }}
                    </el-tag>
                  </div>
                </el-col>

                <el-col :span="24" class="mb10" style="margin-top: 16px">
                  <div class="title">商品轮播图：</div>
                  <div class="acea-row" v-if="formValidate.sliderImages.length > 0">
                    <div v-for="(item, index) in formValidate.sliderImages" :key="index" class="pictrue">
                      <el-image :src="item" :preview-src-list="formValidate.sliderImages"> </el-image>
                    </div>
                  </div>
                  <div v-else class="value">无</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">商品规格：</div>
                  <div class="value">{{ formValidate.specType ? '多规格' : '单规格' }}</div>
                </el-col>

                <el-col v-bind="grid2">
                  <div class="title">佣金设置：</div>
                  <div class="value">{{ formValidate.isSub ? '单独设置' : '默认设置' }}</div>
                </el-col>

                <el-col :span="24" class="mt10" style="margin-top: 16px">
                  <div class="title">商品属性：</div>
                  <div class="value">
                    <!-- 单规格表格 -->
                    <template v-if="formValidate.specType === false">
                      <el-table :data="OneAttrValue" border class="tabNumWidth" size="mini">
                        <el-table-column align="center" label="图片" width="60">
                          <template slot-scope="scope">
                            <div class="upLoadPicBox">
                              <div v-if="formValidate.image" class="pictrue tabPic">
                                <el-image
                                  :src="scope.row.image"
                                  fit="contain"
                                  :preview-src-list="[scope.row.image]"
                                  style="width: 100% !important; height: 100% !important"
                                >
                                </el-image>
                              </div>
                              <div v-else class="upLoad tabPic">无</div>
                            </div>
                          </template>
                        </el-table-column>

                        <template v-if="type == 'eyeglass'">
                          <el-table-column align="center" label="试戴图" width="60">
                            <template slot-scope="scope">
                              <div class="upLoadPicBox">
                                <div v-if="scope.row.wearImage" class="pictrue tabPic">
                                  <el-image
                                    :src="scope.row.wearImage"
                                    fit="contain"
                                    :preview-src-list="[scope.row.wearImage]"
                                    style="width: 100% !important; height: 100% !important"
                                  >
                                  </el-image>
                                </div>
                                <div v-else class="upLoad tabPic">
                                  <i class="el-icon-camera cameraIconfont" />
                                </div>
                              </div>
                            </template>
                          </el-table-column>
                        </template>

                        <el-table-column
                          v-for="(item, iii) in attrValue"
                          :key="iii"
                          :label="formThead[iii].title"
                          align="center"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row[iii] !== '' ? scope.row[iii] : '无' }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </template>
                    <!-- 多规格 -->
                    <template v-if="formValidate.attr.length > 0 && formValidate.specType">
                      <el-table :data="ManyAttrValue" border class="tabNumWidth" size="mini">
                        <template v-if="moreTitles">
                          <el-table-column
                            v-for="(item, key) in moreTitles"
                            :key="key"
                            align="center"
                            :label="item.title"
                          >
                            <template slot-scope="scope">
                              <span class="priceBox" v-text="scope.row[key]" />
                            </template>
                          </el-table-column>
                        </template>

                        <el-table-column align="center" label="图片" width="60">
                          <template slot-scope="scope">
                            <div class="upLoadPicBox">
                              <div v-if="scope.row.image" class="pictrue tabPic">
                                <el-image
                                  :src="scope.row.image"
                                  fit="contain"
                                  :preview-src-list="[scope.row.image]"
                                  style="width: 100% !important; height: 100% !important"
                                >
                                </el-image>
                              </div>
                              <div v-else class="upLoad tabPic">
                                <i class="el-icon-camera cameraIconfont" />
                              </div>
                            </div>
                          </template>
                        </el-table-column>

                        <template v-if="type == 'eyeglass'">
                          <el-table-column align="center" label="试戴图" width="60">
                            <template slot-scope="scope">
                              <div class="upLoadPicBox">
                                <div v-if="scope.row.wearImage" class="pictrue tabPic">
                                  <el-image
                                    :src="scope.row.wearImage"
                                    fit="contain"
                                    :preview-src-list="[scope.row.wearImage]"
                                    style="width: 100% !important; height: 100% !important"
                                  >
                                  </el-image>
                                </div>
                                <div v-else class="upLoad tabPic">
                                  <i class="el-icon-camera cameraIconfont" />
                                </div>
                              </div>
                            </template>
                          </el-table-column>
                        </template>

                        <el-table-column
                          v-for="(item, iii) in attrValue"
                          :key="iii"
                          :label="formThead[iii].title"
                          align="center"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row[iii] !== '' ? scope.row[iii] : '无' }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </template>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 商品详情-->

            <div v-show="currentTab === '1'">
              <div class="description-item">
                <span v-html="formValidate.content || '无'" style="text-align: center"></span>
              </div>
            </div>

            <!-- 其他设置-->
            <div v-show="currentTab === '2'">
              <el-row :gutter="24">
                <el-col :span="24">
                  <div class="title">排序：</div>
                  <div class="value">{{ formValidate.sort }}</div>
                </el-col>

                <el-col :span="24">
                  <div class="title">优惠券：</div>
                  <div class="value">
                    <el-tag
                      v-for="(tag, index) in formValidate.couponList"
                      :key="index"
                      class="mr10 mb10"
                      :disable-transitions="false"
                    >
                      {{ tag.name }}
                    </el-tag>
                    <span v-if="formValidate.couponList.length === 0">无</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import {
  productCouponListApi,
  productAuditApi,
  brandListApi,
  productGuaranteeApi,
  productCreateApi,
  categoryApi,
  productDetailApi,
  productUpdateApi,
} from '@/api/product';
import { mapGetters } from 'vuex';
import { getFileType } from '@/utils/index';
const defaultObj = {
  image: '',
  sliderImages: [],
  videoLink: '',
  name: '',
  intro: '',
  keyword: '',
  couponList: [],
  unitName: '',
  sort: 0,
  giveIntegral: 0,
  ficti: 0,
  isShow: false,
  attrValue: [
    {
      image: '',
      smin: '0.00',
      smax: '0.00',
      cmin: '0.00',
      cmax: '0.00',
      price: 0,
      cost: 0,
      otPrice: 0,
      spec: '',
      stock: 0,
      weight: 0,
      volume: 0,
      brokerage: 0,
      brokerageTwo: 0,
    },
  ],
  attr: [],
  selectRule: '',
  isSub: false,
  content: '',
  specType: false,
  id: undefined,
  couponIds: [],
  coupons: [],
  postage: '1',
  categoryId: '',
  guaranteeIds: '',
  guaranteeIdsList: [],
  brandId: '',
  brandName: '',
};
const objTitle = {
  price: {
    title: '售价（元）',
    type: 'number',
  },
  cost: {
    title: '成本价（元）',
    type: 'number',
  },
  otPrice: {
    title: '原价（元）',
    type: 'number',
  },
  stock: {
    title: '库存',
    type: 'int',
  },
  barCode: {
    title: '商品编号',
    type: 'int',
  },
  spec: {
    title: '镜架参数',
    type: 'string',
  },
  weight: {
    title: '重量（KG）',
    type: 'number',
  },
  volume: {
    title: '体积(m³)',
    type: 'number',
  },
  brokerage: {
    title: '一级返佣(%)',
    type: 'number',
  },
  brokerageTwo: {
    title: '二级返佣(%)',
    type: 'number',
  },
  smin: {
    title: '可配最大度数（近视）',
    type: 'int',
  },
  smax: {
    title: '可配最大度数（远视）',
    type: 'int',
  },
  cmin: {
    title: '适用最低散光',
    type: 'int',
  },
  cmax: {
    title: '适用最高散光',
    type: 'int',
  },
};
export default {
  name: 'ProductInfo',
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    productId: {
      type: [Number, String],
      default: () => null,
    },
  },
  data() {
    return {
      dialogVisible: false,
      direction: 'rtl',
      OneAttrValue: [], // 单规格
      ManyAttrValue: [], // 多规格
      formThead: Object.assign({}, objTitle),
      formValidate: Object.assign({}, defaultObj),
      moreTitles: {},
      currentTab: '0',
      isAttr: false,
      showAll: false,
      videoLink: '',
      guaranteeList: [],
      brandList: [],
      search: {
        limit: 10,
        page: 1,
        cid: '',
        brandName: '',
      },
      loading: false,
      brand: '',
      category: '',
      grid2: {
        xl: 12,
        lg: 12,
        md: 12,
        sm: 24,
        xs: 24,
      },
      type: 'others',
    };
  },
  computed: {
    ...mapGetters(['adminProductClassify', 'adminEyeglassClassify', 'adminLensClassify', 'merProductClassify']),
    attrValue() {
      const obj = Object.assign({}, defaultObj.attrValue[0]);
      delete obj.image;

      if (this.type !== 'lens') {
        delete obj.smin;
        delete obj.smax;
        delete obj.cmin;
        delete obj.cmax;
      }

      if (this.type != 'eyeglass') {
        delete obj.spec;
      }

      if (!this.formValidate.isSub) {
        delete obj.brokerage;
        delete obj.brokerageTwo;
      }
      return obj;
    },
    guarantees() {
      return this.guaranteeList.filter((item) => {
        return this.formValidate.guaranteeIdsList.includes(item.id);
      });
    },
    adminCategory() {
      let category = this.getChild(this.adminProductClassify, this.formValidate.categoryId);
      return category && category.name;
    },
  },
  watch: {
    productId: {
      handler: function (val) {
        if (val != null) {
          this.currentTab = '0';
          this.getInfo(val);
        }
      },
      immediate: false,
      deep: true,
    },
    isShow: {
      handler: function (val) {
        this.dialogVisible = val;
      },
    },
  },
  created() {
    this.currentTab = '0';
    if (!this.adminProductClassify.length) this.$store.dispatch('product/getAdminProductClassify');
  },
  mounted() {
    this.getProductGuarantee();
  },
  methods: {
    /**
     * 计算当前选择的平台商品类型 镜架/镜片/其他
     */
    computeType() {
      // 先判断是不是镜架
      let isEyeglass = this.adminEyeglassClassify.find((item) => {
        return item.id === this.formValidate.categoryId;
      });

      if (isEyeglass) {
        this.type = 'eyeglass';
      } else {
        // 先判断是不是镜片
        let isLens = this.adminLensClassify.find((item) => {
          return item.id === this.formValidate.categoryId;
        });

        if (isLens) {
          this.type = 'lens';
        } else {
          this.type = 'others';
        }
      }
    },

    close() {
      this.$emit('close');
    },
    // 保障服务列表
    getProductGuarantee() {
      productGuaranteeApi().then((res) => {
        this.guaranteeList = res;
      });
    },
    // 品牌列表
    getbrandList() {
      this.search.cid = this.formValidate.categoryId;
      brandListApi(this.search).then((res) => {
        this.brandList = res.list;
        let brand = this.brandList.find((item) => item.id == this.formValidate.brandId);
        if (brand) {
          this.formValidate.brandName = brand.name;
        } else {
          this.formValidate.brandName = '其他';
        }
      });
    },

    getChild(items, id) {
      for (let i = 0, len = items.length; i < len; i++) {
        if (items[i].id == id) {
          return items[i];
        } else {
          if (Array.isArray(items[i].childList)) {
            let child = this.getChild(items[i].childList, id);
            if (child) {
              return child;
            }
          }
        }
      }
      return null;
    },
    // 详情
    getInfo(id) {
      this.loading = true;
      productDetailApi(id)
        .then((res) => {
          let info = res;

          let sliderImages = JSON.parse(info.sliderImage);
          sliderImages = sliderImages.map((img) => {
            return this.$selfUtil.setDomain(img);
          });

          let videoLink = '';
          if (getFileType(sliderImages[0]) == 'video') {
            videoLink = sliderImages.shift();
          }

          this.formValidate = {
            image: this.$selfUtil.setDomain(info.image),
            sliderImages,
            videoLink,
            name: info.name,
            intro: info.intro,
            keyword: info.keyword,
            cateIds: info.cateId.split(','), // 商品分类id
            cateId: info.cateId, // 商户商品分类id传值
            unitName: info.unitName,
            sort: info.sort,
            isShow: info.isShow,
            tempId: info.tempId,
            attr: info.attr,
            attrValue: info.attrValue,
            selectRule: info.selectRule,
            isSub: info.isSub,
            content: this.$selfUtil.replaceImgSrcHttps(info.content),
            specType: info.specType,
            id: info.id,
            giveIntegral: info.giveIntegral,
            ficti: info.ficti,
            coupons: info.coupons,
            couponIds: info.couponIds,
            postage: info.postage,
            brandId: info.brandId,
            brandName: '',
            categoryId: info.categoryId, //平台商品分类
            guaranteeIds: info.guaranteeIds, //保障服务传值
            couponList: info.couponList || [],
            guaranteeIdsList: info.guaranteeIds.split(',').map(Number), //保障服务
          };
          this.computeType(); // 查看当前商品的大类型

          this.getbrandList(); // 获取品牌列表

          if (info.specType) {
            this.formValidate.attr = info.attr.map((item) => {
              return {
                attrName: item.attrName,
                attrValue: item.attrValues.split(','),
              };
            });
            this.ManyAttrValue = info.attrValue.map((val) => {
              val.image = this.$selfUtil.setDomain(val.image);
              val.wearImage = this.$selfUtil.setDomain(val.wearImage);
              val.attrValue = JSON.parse(val.attrValue);
              return val;
            });

            const tmp = {};
            this.formValidate.attr.forEach((o, i) => {
              tmp[o.attrName] = { title: o.attrName, type: 'string' };
            });

            // 此处手动实现后台原本value0 value1的逻辑
            this.formValidate.attrValue.forEach((item) => {
              for (let attrValueKey in item.attrValue) {
                item[attrValueKey] = item.attrValue[attrValueKey];
              }
            });
            this.moreTitles = tmp;
            this.formThead = Object.assign({}, this.formThead, tmp);
          } else {
            this.OneAttrValue = info.attrValue.map((item) => {
              return item;
            });
          }
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
        });
    },
  },
};
</script>
<style scoped lang="scss">
.title {
  width: 120px;
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: #606266;
  line-height: 36px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-weight: 700;
}

.value {
  margin-left: 120px;
  line-height: 36px;
  position: relative;
  font-size: 14px;
  color: #606266;
}

.from-foot-btn {
  width: 100%;
  padding: 20px;
}

.fix {
  z-index: 10;
  position: absolute;
  left: 0;
  bottom: 0px;
  padding-bottom: 10px;
  background: #fff;
}

.btn-shadow {
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05);
}

.infoBox {
  ::v-deep.el-drawer__header {
    margin-bottom: 0;
    font-size: 20px;
  }

  ::v-deep .el-input.is-disabled .el-input__inner {
    background: none;
    cursor: none;
    color: #606266;
  }

  ::v-deep.el-icon-arrow-down,
  ::v-deep .el-icon-arrow-up {
    display: none;
  }
}

.divBox {
  ::v-deep .el-input__inner:hover,
  ::v-deep.el-input > input,
  ::v-deep.el-textarea > textarea {
    border: none;
    padding: 0;
  }

  ::v-deep .el-form-item {
    margin-bottom: 0;
  }

  ::v-deep.el-card__body {
    padding: 5px;
  }
}

.disLabel {
  ::v-deep .el-form-item__label {
    margin-left: 36px !important;
  }
}

.disLabelmoren {
  ::v-deep.el-form-item__label {
    margin-left: 120px !important;
  }
}

.priamry_border {
  border: 1px solid #1890ff;
  color: #1890ff;
}

.color-item {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  color: #fff;
  margin-right: 10px;
}

.color-list .color-item.blue {
  background-color: #1e9fff;
}

.color-list .color-item.yellow {
  background-color: rgb(254, 185, 0);
}

.color-list .color-item.green {
  background-color: #009688;
}

.color-list .color-item.red {
  background-color: #ed4014;
}

.proCoupon {
  ::v-deepel-form-item__content {
    margin-top: 5px;
  }
}

.tabPic {
  width: 40px !important;
  height: 40px !important;

  img {
    width: 100%;
    height: 100%;
  }
}

.noLeft {
  ::v-deepel-form-item__content {
    margin-left: 0 !important;
  }
}

/*.tabNumWidth{*/
/*::v-deepel-input-number--medium{*/
/*width: 121px !important;*/
/*}*/
/*::v-deepel-input-number__increase{*/
/*width: 20px !important;*/
/*font-size: 12px !important;*/
/*}*/
/*::v-deepel-input-number__decrease{*/
/*width: 20px !important;*/
/*font-size: 12px !important;*/
/*}*/
/*::v-deepel-input-number--medium .el-input__inner {*/
/*padding-left: 25px !important;*/
/*padding-right: 25px !important;*/
/*}*/
/*::v-deep thead{*/
/*line-height: normal !important;*/
/*}*/
/*::v-deep .el-table .cell{*/
/*line-height: normal !important;*/
/*}*/
/*}*/
.selWidth {
  width: 100%;
}

.selWidthd {
  width: 100px;
}

.button-new-tag {
  height: 28px;
  line-height: 26px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

.labeltop {
  ::v-deepel-form-item__label {
    float: none !important;
    display: inline-block !important;
    width: auto !important;
  }
}

.iview-video-style {
  width: 300px;
  height: 180px;
  border-radius: 10px;
  background-color: #707070;
  margin: 0 120px 20px;
  position: relative;
  overflow: hidden;
}

.iview-video-style .iconv {
  color: #fff;
  line-height: 180px;
  width: 50px;
  height: 50px;
  display: inherit;
  font-size: 26px;
  position: absolute;
  top: -74px;
  left: 50%;
  margin-left: -25px;
}

.iview-video-style .mark {
  position: absolute;
  width: 100%;
  height: 30px;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
}

::v-deep .el-tag {
  margin-top: 5px !important;
}
</style>
