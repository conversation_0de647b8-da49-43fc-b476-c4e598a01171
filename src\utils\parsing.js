// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === 'string') {
      time = time
        .replace(new RegExp(/-/gm), '/')
        .replace('T', ' ')
        .replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
  return time_str;
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params;
  search.params =
    typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (typeof propName === 'undefined') {
    search.params['beginTime'] = dateRange[0];
    search.params['endTime'] = dateRange[1];
  } else {
    search.params['begin' + propName] = dateRange[0];
    search.params['end' + propName] = dateRange[1];
  }
  return search;
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments,
    flag = true,
    i = 1;
  str = str.replace(/%s/g, function () {
    var arg = args[i++];
    if (typeof arg === 'undefined') {
      flag = false;
      return '';
    }
    return arg;
  });
  return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function praseStrEmpty(str) {
  if (!str || str == 'undefined' || str == 'null') {
    return '';
  }
  return str;
}

// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
}

/**
 * 构造树型结构数据
 * @param {Array} data 数据源
 * @param {String} idKey id字段 默认 'id'
 * @param {String} parentIdKey 父节点字段 默认 'parentId'
 * @param {String} childrenKey 孩子节点字段 默认 'children'
 */
export function handleTree(data, idKey, parentIdKey, childrenKey) {
  let config = {
    KEY_ID: idKey || 'id',
    KEY_PARENT_ID: parentIdKey || 'parentId',
    KEY_CHILDREN: childrenKey || 'children',
  };

  let map = {};
  for (let i = 0, len = data.length; i < len; i++) {
    let item = data[i];

    if (!map[item[config.KEY_ID]]) {
      map[item[config.KEY_ID]] = {
        [config.KEY_CHILDREN]: [],
      };
    }

    map[item[config.KEY_ID]] = {
      ...item,
      [config.KEY_CHILDREN]: map[item[config.KEY_ID]][config.KEY_CHILDREN],
    };

    if (!map[item[config.KEY_PARENT_ID]]) {
      map[item[config.KEY_PARENT_ID]] = {
        [config.KEY_CHILDREN]: [],
      };
    }
    map[item[config.KEY_PARENT_ID]][config.KEY_CHILDREN].push(map[item[config.KEY_ID]]);
  }

  return map[0][config.KEY_CHILDREN];
}

/**
 * 格式化路由数据信息，把路由数据转换成符合正确的格式
 * @param {Array} routes 路由列表
 * @param {Object} parent 父路由
 * @returns
 */
export function formatRoutes(routes, parent) {
  routes = Array.isArray(routes) ? routes : [routes];
  parent = parent || {};
  let paths = {
    0: '/',
  };
  let queue = [...routes];
  while (queue.length) {
    let route = queue.shift();
    route.url = route.component;
    route.extra = route.icon;
    route.child = route.childList;
    let routePath = `${paths[route.pid]}${route.pid}/`;
    paths[route.id] = routePath;
    route.path = routePath;
    if (Array.isArray(route.childList) && route.childList.length > 0) {
      queue = route.childList.concat(queue);
    }
  }
  return routes;
}

/**
 * 展开树
 * @param {Array} treeList 树列表
 * @param {String} childKey 子节点键名
 * @returns 展开后所有节点的列表
 */
export function flattenTree(tree, childKey) {
  childKey = childKey || 'childList';
  let nodes = [];
  let queue = Array.isArray(tree) ? tree : [tree];
  while (queue.length) {
    let node = queue.pop();
    let children = node[childKey];
    delete node[childKey];
    if (children && Array.isArray(children) && children.length > 0) {
      queue = queue.concat(children);
    }
    nodes.push(node);
  }

  return nodes;
}

/**
 * 从树中找到符合条件的单个节点数据
 * @param {Array} tree 树
 * @param {String} key 要查找的key
 * @param {any} value 要查找的key对应的值
 */
export function findNodeFromTree(tree, key, value) {
  let children = Array.isArray(tree) ? tree : [tree];
  let queue = [...children];
  let node = null;
  while (queue.length) {
    let child = queue.pop(); // 从后往前取
    if (child[key] === value) {
      node = child;
      break;
    } else {
      if (Array.isArray(child.childList) && child.childList.length > 0) {
        queue = queue.concat(child.childList);
      }
    }
  }
  return node;
}

/**
 * 从树中找到符合条件的所有节点数据
 * @param {Array} tree 树
 * @param {String} key 要查找的key
 * @param {any} value 要查找的key对应的值
 */
export function findNodesFromTree(tree, key, value) {
  let children = Array.isArray(tree) ? tree : [tree];
  let queue = [...children];
  let nodes = [];
  while (queue.length) {
    let child = queue.pop(); // 从后往前取
    if (child[key] === value) {
      nodes.push(child);
    }

    if (Array.isArray(child.childList) && child.childList.length > 0) {
      queue = queue.concat(child.childList);
    }
  }
  return nodes;
}
