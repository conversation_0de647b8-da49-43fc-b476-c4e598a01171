(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6bec7246"],{"0836":function(e,t,r){},"634a":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"divBox relative"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix"},[r("div",{staticClass:"container"},[r("el-form",{attrs:{size:"small","label-width":"100px"}},[e.checkPermi(["merchant:order:status:num"])?r("el-form-item",{attrs:{label:"订单状态："}},[r("el-radio-group",{attrs:{type:"button"},on:{change:e.seachList},model:{value:e.tableFrom.status,callback:function(t){e.$set(e.tableFrom,"status",t)},expression:"tableFrom.status"}},[r("el-radio-button",{attrs:{label:"all"}},[e._v("\n                全部 "+e._s(e.orderChartType.all)+"\n              ")]),e._v(" "),r("el-radio-button",{attrs:{label:"notShipped"}},[e._v("\n                未发货 "+e._s(e.orderChartType.notShipped)+"\n              ")]),e._v(" "),r("el-radio-button",{attrs:{label:"spike"}},[e._v("\n                待收货 "+e._s(e.orderChartType.spike)+"\n              ")]),e._v(" "),r("el-radio-button",{attrs:{label:"awaitVerification"}},[e._v("\n                待核销 "+e._s(e.orderChartType.verification)+"\n              ")]),e._v(" "),r("el-radio-button",{attrs:{label:"complete"}},[e._v("\n                交易完成 "+e._s(e.orderChartType.complete)+"\n              ")]),e._v(" "),r("el-radio-button",{attrs:{label:"refunded"}},[e._v("\n                已退款 "+e._s(e.orderChartType.refunded)+"\n              ")]),e._v(" "),r("el-radio-button",{attrs:{label:"deleted"}},[e._v("\n                已删除 "+e._s(e.orderChartType.deleted)+"\n              ")])],1)],1):e._e(),e._v(" "),r("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[r("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(t){return e.selectChange(e.tableFrom.dateLimit)}},model:{value:e.tableFrom.dateLimit,callback:function(t){e.$set(e.tableFrom,"dateLimit",t)},expression:"tableFrom.dateLimit"}},e._l(e.fromList.fromTxt,(function(t,i){return r("el-radio-button",{key:i,attrs:{label:t.val}},[e._v("\n                "+e._s(t.text)+"\n              ")])})),1),e._v(" "),r("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1),e._v(" "),r("el-form-item",{staticClass:"width100",attrs:{label:"订单号："}},[r("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入订单号",size:"small",clearable:""},model:{value:e.tableFrom.orderNo,callback:function(t){e.$set(e.tableFrom,"orderNo",t)},expression:"tableFrom.orderNo"}},[r("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1),e._v(" "),r("el-form-item",{staticClass:"width100"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:order:verification"],expression:"['merchant:order:verification']"}],attrs:{size:"small",type:"primary"},on:{click:e.onWriteOff}},[e._v("\n              核销订单\n            ")])],1)],1)],1)])]),e._v(" "),r("div",{staticClass:"mt20"}),e._v(" "),r("el-card",{staticClass:"box-card"},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:e.tableData.data,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"},"row-key":function(e){return e.orderNo}}},[e.checkedCities.includes("订单号")?r("el-table-column",{attrs:{label:"订单号","min-width":"185"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"acea-row"},[r("span",{directives:[{name:"show",rawName:"v-show",value:1===t.row.type,expression:"scope.row.type === 1"}],staticClass:"iconfont icon-shipinhao mr5",staticStyle:{color:"#f6ae02"}}),e._v(" "),r("span",{class:parseInt(t.row.refundStatus)>0?"red":"",staticStyle:{display:"block"},domProps:{textContent:e._s(t.row.orderNo)}})]),e._v(" "),r("span",{directives:[{name:"show",rawName:"v-show",value:parseInt(t.row.refundStatus)>0,expression:"parseInt(scope.row.refundStatus) > 0"}],staticStyle:{color:"#ed4014",display:"block"}},[e._v("\n            "+e._s(e._f("orderRefundStatusFilter")(t.row.refundStatus))+"\n          ")]),e._v(" "),r("span",{directives:[{name:"show",rawName:"v-show",value:t.row.isUserDel,expression:"scope.row.isUserDel"}],staticStyle:{color:"#ed4014",display:"block"}},[e._v("用户已删除")])]}}],null,!1,673321908)}):e._e(),e._v(" "),e.checkedCities.includes("用户昵称")?r("el-table-column",{attrs:{label:"用户昵称","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{class:1==t.row.isLogoff?"red":""},[e._v(e._s(t.row.nickName))]),e._v(" "),1==t.row.isLogoff?r("span",{class:1==t.row.isLogoff?"red":""},[e._v("|")]):e._e(),e._v(" "),1==t.row.isLogoff?r("span",{staticClass:"red"},[e._v("(已注销)")]):e._e()]}}],null,!1,4076123740)}):e._e(),e._v(" "),e.checkedCities.includes("实际支付")?r("el-table-column",{attrs:{prop:"payPrice",label:"实际支付","min-width":"80"}}):e._e(),e._v(" "),e.checkedCities.includes("支付方式")?r("el-table-column",{attrs:{label:"支付方式","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e._f("payTypeFilter")(t.row.payType)))])]}}],null,!1,2281245087)}):e._e(),e._v(" "),e.checkedCities.includes("订单状态")?r("el-table-column",{attrs:{label:"订单状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[3===t.row.refundStatus?r("span",{staticClass:"fontColor3"},[e._v("已退款")]):r("span",[e._v(e._s(e._f("orderStatusFilter")(t.row.status)))])]}}],null,!1,3658376581)}):e._e(),e._v(" "),e.checkedCities.includes("下单时间")?r("el-table-column",{attrs:{prop:"createTime",label:"下单时间","min-width":"150"}}):e._e(),e._v(" "),r("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center","render-header":e.renderHeader},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.status&&3!==t.row.refundStatus||2===t.row.status&&parseFloat(t.row.refundStatus)<3?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:order:send"],expression:"['merchant:order:send']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return e.sendOrder(t.row)}}},[e._v("\n            发货\n          ")]):e._e(),e._v(" "),e.checkPermi(["merchant:order:info"])?r("el-button",{staticClass:"mr10",attrs:{type:"text",size:"small"},nativeOn:{click:function(r){return e.onOrderDetails(t.row.orderNo)}}},[e._v("\n            订单详情\n          ")]):e._e(),e._v(" "),r("el-dropdown",{attrs:{trigger:"click"}},[r("span",{staticClass:"el-dropdown-link"},[e._v(" 更多"),r("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e.checkPermi(["merchant:order:mark"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.onOrderMark(t.row)}}},[e._v("\n                订单备注\n              ")]):e._e(),e._v(" "),1===t.row.isUserDel&&e.checkPermi(["merchant:order:delete"])?r("el-dropdown-item",{nativeOn:{click:function(r){return e.handleDelete(t.row,t.$index)}}},[e._v("\n                删除订单\n              ")]):e._e()],1)],1)]}}])})],1),e._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.card_select_show,expression:"card_select_show"}],staticClass:"card_abs"},[[r("div",{staticClass:"cell_ht"},[r("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("\n          全选\n        ")]),e._v(" "),r("el-button",{attrs:{size:"small",type:"text"},on:{click:function(t){return e.checkSave()}}},[e._v("保存")])],1),e._v(" "),r("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.checkedCities,callback:function(t){e.checkedCities=t},expression:"checkedCities"}},e._l(e.columnData,(function(t){return r("el-checkbox",{key:t,staticClass:"check_cell",attrs:{label:t}},[e._v(e._s(t))])})),1)]],2),e._v(" "),r("el-dialog",{attrs:{title:"操作记录",visible:e.dialogVisibleJI,width:"700px"},on:{"update:visible":function(t){e.dialogVisibleJI=t}}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.LogLoading,expression:"LogLoading"}],staticStyle:{width:"100%"},attrs:{border:"",data:e.tableDataLog.data}},[r("el-table-column",{attrs:{prop:"oid",align:"center",label:"ID","min-width":"80"}}),e._v(" "),r("el-table-column",{attrs:{prop:"changeMessage",label:"操作记录",align:"center","min-width":"280"}}),e._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:"操作时间",align:"center","min-width":"280"}})],1),e._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":e.tableFromLog.limit,"current-page":e.tableFromLog.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableDataLog.total},on:{"size-change":e.handleSizeChangeLog,"current-change":e.pageChangeLog}})],1)],1),e._v(" "),r("details-from",{ref:"orderDetail",attrs:{orderNo:e.orderNo}}),e._v(" "),r("order-send",{ref:"send",attrs:{orderNo:e.orderNo},on:{submitFail:e.seachList}})],1)},o=[],n=r("f8b7"),a=r("129b"),s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{staticClass:"order_box",attrs:{visible:e.modals,title:"发送货","before-close":e.handleClose,width:"1000px"},on:{"update:visible":function(t){e.modals=t}}},[r("el-form",{ref:"formItem",attrs:{model:e.formItem,"label-width":"110px",rules:e.rules},nativeOn:{submit:function(e){e.preventDefault()}}},[r("div",[r("el-form-item",{attrs:{label:"发货类型：",prop:"expressId"}},[r("el-radio-group",{on:{change:function(t){return e.changeRadio(e.formItem.expressRecordType)}},model:{value:e.formItem.expressRecordType,callback:function(t){e.$set(e.formItem,"expressRecordType",t)},expression:"formItem.expressRecordType"}},[r("el-radio",{attrs:{label:"1"}},[e._v("普通快递")])],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"快递公司：",prop:"expressCode"}},[r("el-select",{staticStyle:{width:"80%"},attrs:{filterable:""},on:{change:function(t){return e.onChangeExport(e.formItem.expressCode)}},model:{value:e.formItem.expressCode,callback:function(t){e.$set(e.formItem,"expressCode",t)},expression:"formItem.expressCode"}},e._l(e.express,(function(e,t){return r("el-option",{key:t,attrs:{value:e.code,label:e.name}})})),1)],1),e._v(" "),"1"===e.formItem.expressRecordType?r("el-form-item",{attrs:{label:"快递单号：",prop:"expressNumber"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入快递单号"},model:{value:e.formItem.expressNumber,callback:function(t){e.$set(e.formItem,"expressNumber",t)},expression:"formItem.expressNumber"}})],1):e._e(),e._v(" "),"2"===e.formItem.expressRecordType?[r("el-form-item",{staticClass:"express_temp_id",attrs:{label:"电子面单：",prop:"expressTempId"}},[r("div",{staticClass:"acea-row"},[r("el-select",{class:[e.formItem.expressTempId?"width9":"width8"],attrs:{placeholder:"请选择电子面单"},on:{change:e.onChangeImg},model:{value:e.formItem.expressTempId,callback:function(t){e.$set(e.formItem,"expressTempId",t)},expression:"formItem.expressTempId"}},e._l(e.exportTempList,(function(e,t){return r("el-option",{key:t,attrs:{value:e.temp_id,label:e.title}})})),1),e._v(" "),e.formItem.expressTempId?r("div",{staticStyle:{position:"relative"}},[r("div",{staticClass:"tempImgList ml10"},[r("div",{staticClass:"demo-image__preview"},[r("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.tempImg,"preview-src-list":[e.tempImg]}})],1)])]):e._e()],1)]),e._v(" "),r("el-form-item",{attrs:{label:"寄件人姓名：",prop:"toName"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人姓名"},model:{value:e.formItem.toName,callback:function(t){e.$set(e.formItem,"toName",t)},expression:"formItem.toName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"寄件人电话：",prop:"toTel"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人电话"},model:{value:e.formItem.toTel,callback:function(t){e.$set(e.formItem,"toTel",t)},expression:"formItem.toTel"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"寄件人地址：",prop:"toAddr"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入寄件人地址"},model:{value:e.formItem.toAddr,callback:function(t){e.$set(e.formItem,"toAddr",t)},expression:"formItem.toAddr"}})],1)]:e._e()],2),e._v(" "),r("el-form-item",{attrs:{label:"分单发货：",prop:"isSplit"}},[r("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},model:{value:e.formItem.isSplit,callback:function(t){e.$set(e.formItem,"isSplit",t)},expression:"formItem.isSplit"}}),e._v(" "),r("p",{directives:[{name:"show",rawName:"v-show",value:e.formItem.isSplit,expression:"formItem.isSplit"}],staticClass:"area-desc"},[e._v("\n        可选择表格中的商品单独发货，发货后会生成新的订单且不能撤回，请谨慎操作！\n      ")])],1),e._v(" "),e.formItem.isSplit?[r("el-table",{ref:"multipleSelection",staticStyle:{width:"97%","margin-left":"35px"},attrs:{data:e.productList,"tooltip-effect":"dark",size:"mini","row-key":function(e){return e.id}},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{align:"center",type:"selection",selectable:e.selectable,"reserve-selection":!0,"min-width":"50"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"商品信息","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"acea-row",staticStyle:{"align-items":"center"}},[r("div",{staticClass:"demo-image__preview"},[r("el-image",{attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1),e._v(" "),r("span",{staticClass:"priceBox",staticStyle:{width:"150px"}},[e._v(e._s(t.row.productName))])])]}}],null,!1,2876500606)}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"规格","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticClass:"priceBox"},[e._v(e._s(t.row.sku))])]}}],null,!1,969154261)}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"总数","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticClass:"priceBox"},[e._v(e._s(t.row.payNum))]),e._v(" "),r("div",{staticClass:"priceBox fontColor3"},[e._v("已发"+e._s(t.row.deliveryNum))])]}}],null,!1,3305381094)}),e._v(" "),r("el-table-column",{attrs:{label:"发货数量",align:"center","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticClass:"priceBox",attrs:{disabled:t.row.deliveryNum===t.row.payNum,min:0,max:t.row.payNum-t.row.deliveryNum,step:1},on:{blur:function(r){return e.limitCount(t.row,t.$index)}},model:{value:t.row["num"],callback:function(r){e.$set(t.row,"num",r)},expression:"scope.row['num']"}})]}}],null,!1,1439265686)})],1)]:e._e(),e._v(" "),"2"===e.formItem.type?r("div",[r("el-form-item",{attrs:{label:"送货人姓名：",prop:"deliveryName"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入送货人姓名"},model:{value:e.formItem.deliveryName,callback:function(t){e.$set(e.formItem,"deliveryName",t)},expression:"formItem.deliveryName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"送货人电话：",prop:"deliveryTel"}},[r("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入送货人电话"},model:{value:e.formItem.deliveryTel,callback:function(t){e.$set(e.formItem,"deliveryTel",t)},expression:"formItem.deliveryTel"}})],1)],1):e._e()],2),e._v(" "),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.putSend("formItem")}}},[e._v("提交")]),e._v(" "),r("el-button",{on:{click:function(t){return e.cancel("formItem")}}},[e._v("取消")])],1)],1)},l=[],c=r("2f2c"),u=r("e350"),d=r("61f7");function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach((function(t){f(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e,t,r){return t=v(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=g(e,"string");return"symbol"===m(t)?t:String(t)}function g(e,t){if("object"!==m(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!==m(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},n=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(O){l=function(e,t,r){return e[t]=r}}function c(e,t,r,o){var n=t&&t.prototype instanceof h?t:h,a=Object.create(n.prototype),s=new S(o||[]);return i(a,"_invoke",{value:L(e,r,s)}),a}function u(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(O){return{type:"throw",arg:O}}}e.wrap=c;var d={};function h(){}function p(){}function f(){}var v={};l(v,n,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(N([])));y&&y!==t&&r.call(y,n)&&(v=y);var w=f.prototype=h.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function o(i,n,a,s){var l=u(e[i],e,n);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==m(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,s)}),(function(e){o("throw",e,a,s)})):t.resolve(d).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,s)}))}s(l.arg)}var n;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){o(e,r,t,i)}))}return n=n?n.then(i,i):i()}})}function L(e,t,r){var i="suspendedStart";return function(o,n){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===o)throw n;return T()}for(r.method=o,r.arg=n;;){var a=r.delegate;if(a){var s=k(a,r);if(s){if(s===d)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===i)throw i="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i="executing";var l=u(e,t,r);if("normal"===l.type){if(i=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i="completed",r.method="throw",r.arg=l.arg)}}}function k(e,t){var r=t.method,i=e.iterator[r];if(void 0===i)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=u(i,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var n=o.arg;return n?n.done?(t[e.resultName]=n.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):n:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function N(e){if(e){var t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function t(){for(;++i<e.length;)if(r.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return p.prototype=f,i(w,"constructor",{value:f,configurable:!0}),i(f,"constructor",{value:p,configurable:!0}),p.displayName=l(f,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,l(e,s,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},_(x.prototype),l(x.prototype,a,(function(){return this})),e.AsyncIterator=x,e.async=function(t,r,i,o,n){void 0===n&&(n=Promise);var a=new x(c(t,r,i,o),n);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},_(w),l(w,s,"Generator"),l(w,n,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var i in t)r.push(i);return r.reverse(),function e(){for(;r.length;){var i=r.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},e.values=N,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(I),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(r,i){return a.type="throw",a.arg=e,t.next=r,i&&(t.method="next",t.arg=void 0),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o],a=n.completion;if("root"===n.tryLoc)return i("end");if(n.tryLoc<=this.prev){var s=r.call(n,"catchLoc"),l=r.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return i(n.catchLoc,!0);if(this.prev<n.finallyLoc)return i(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return i(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return i(n.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=e,a.arg=t,n?(this.method="next",this.next=n.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),I(r),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var o=i.arg;I(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:N(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),d}},e}function y(e,t,r,i,o,n,a){try{var s=e[n](a),l=s.value}catch(c){return void r(c)}s.done?t(l):Promise.resolve(l).then(i,o)}function w(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var n=e.apply(t,r);function a(e){y(n,i,o,a,s,"next",e)}function s(e){y(n,i,o,a,s,"throw",e)}a(void 0)}))}}var _=function(e,t,r){if(!t)return r(new Error("请填写手机号"));/^1[3456789]\d{9}$/.test(t)?r():r(new Error("手机号格式不正确!"))},x={name:"orderSend",props:{orderNo:String},watch:{},data:function(){return{productList:[],formItem:{deliveryType:"express",isSplit:!1,expressRecordType:"1",expressId:"",expressCode:"",expressNumber:"",expressTempId:"",toAddr:"",toName:"",toTel:"",orderNo:"",detailList:[]},modals:!1,express:[],exportTempList:[],tempImg:"",rules:{toName:[{required:!0,message:"请输寄件人姓名",trigger:"blur"}],toTel:[{required:!0,validator:_,trigger:"blur"}],toAddr:[{required:!0,message:"请输入寄件人地址",trigger:"blur"}],expressCode:[{required:!0,message:"请选择快递公司",trigger:"change"}],expressNumber:[{required:!0,message:"请输入快递单号",trigger:"blur"}],expressTempId:[{required:!0,message:"请选择电子面单",trigger:"change"}],deliveryName:[{required:!0,message:"请输入送货人姓名",trigger:"blur"}],deliveryTel:[{required:!0,validator:_,trigger:"blur"}],isSplit:[{required:!0,message:"请选择分单发货",trigger:"change"}]},expressType:"normal",detailList:[],multipleSelection:[]}},mounted:function(){},methods:{checkPermi:u["a"],selectable:function(e,t){return e.deliveryNum!==e.payNum},sheetInfo:function(){var e=this;Object(n["r"])().then(function(){var t=w(b().mark((function t(r){return b().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.formItem.toAddr=r.exportToAddress||"",e.formItem.toName=r.exportToName||"",e.formItem.toTel=r.exportToTel||"";case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},limitCount:function(e,t){e.num>e.payNum&&(e.num=e.payNum)},handleSelectionChange:function(e){this.multipleSelection=e},onChangeExport:function(e){this.formItem.expressTempId="","2"===this.formItem.expressRecordType&&this.exportTemp(e)},exportTemp:function(e){var t=this;Object(c["b"])({com:e}).then(function(){var e=w(b().mark((function e(r){return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.exportTempList=r.data.data||[];case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},onChangeImg:function(e){var t=this;this.exportTempList.map((function(r){r.temp_id===e&&(t.tempImg=r.pic)}))},changeRadioType:function(){this.formItem.expressId="",this.formItem.expressCode=""},changeRadio:function(e){this.expressType=2==e?"elec":"normal",this.formItem.expressId="",this.formItem.expressCode="",this.getList()},orderProDetail:function(e){var t=this;Object(n["i"])(e).then(function(){var e=w(b().mark((function e(r){return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.productList=r;case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},getList:function(){var e=this;Object(c["c"])({type:this.expressType}).then(function(){var t=w(b().mark((function t(r){return b().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.express=r;case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},putSend:Object(d["a"])((function(e){var t=this,r={},i=[];if(this.formItem.orderNo=this.orderNo,this.multipleSelection.map((function(e){i.push({orderDetailId:e.id,num:e.num})})),this.formItem.detailList=i,this.formItem.isSplit){if(!this.formItem.detailList.length)return this.$message.warning("请选择分单发货商品");var o=!1;if(this.formItem.detailList.map((function(e){e.num||(o=!0)})),o)return void this.$message.warning("请填写发货数量");r=this.formItem}else r=p({},this.formItem),delete r.detailList;this.$refs[e].validate((function(i){i?Object(n["l"])(r).then((function(r){t.$message.success("发送货成功"),t.modals=!1,t.$refs[e].resetFields(),t.$emit("submitFail")})):t.$message.error("请填写信息")}))})),handleClose:function(){this.cancel("formItem")},cancel:function(e){this.modals=!1,this.$refs[e].resetFields(),this.formItem.expressRecordType="1"}}},L=x,k=(r("ccfc"),r("2877")),C=Object(k["a"])(L,s,l,!1,null,"401d75e2",null),I=C.exports,S=(r("a78e"),r("ed08")),N=r("c4c8"),T={name:"orderlistDetails",components:{detailsFrom:a["a"],orderSend:I},data:function(){return{RefuseVisible:!1,RefuseData:{},orderNo:"",refundVisible:!1,refundData:{},dialogVisibleJI:!1,tableDataLog:{data:[],total:0},tableFromLog:{page:1,limit:10,orderNo:0},LogLoading:!1,isCreate:1,editData:null,dialogVisible:!1,tableData:{data:[],total:0},listLoading:!0,tableFrom:{status:"all",dateLimit:"",orderNo:"",page:1,limit:20},orderChartType:{},timeVal:[],fromList:this.$constants.fromList,fromType:[{value:"all",text:"全部"},{value:"info",text:"普通"},{value:"pintuan",text:"拼团"},{value:"bragin",text:"砍价"},{value:"miaosha",text:"秒杀"}],selectionList:[],ids:"",orderids:"",cardLists:[],isWriteOff:Object(S["f"])(),proType:0,active:!1,card_select_show:!1,checkAll:!1,checkedCities:["订单号","订单类型","用户昵称","实际支付","支付方式","订单状态","下单时间"],columnData:["订单号","订单类型","用户昵称","实际支付","支付方式","订单状态","下单时间"],isIndeterminate:!0,orderDatalist:null}},mounted:function(){this.getList(),this.getOrderStatusNum()},methods:{checkPermi:u["a"],onWriteOff:function(e){var t=this;this.$prompt("核销订单",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入核销码",inputType:"text",inputPlaceholder:"请输入核销码",inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(e){var r=e.value;Object(n["s"])({verifyCode:r}).then((function(){t.$message.success("核销成功"),t.seachList()}))})).catch((function(){t.$message.info("取消输入")}))},resetFormRefundhandler:function(){this.refundVisible=!1},resetFormRefusehand:function(){this.RefuseVisible=!1},resetForm:function(e){this.dialogVisible=!1},seachList:function(){this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},sendOrder:function(e){this.orderNo=e.orderNo,this.$refs.send.modals=!0,this.$refs.send.getList(),this.$refs.send.orderProDetail(e.orderNo)},handleDelete:function(e,t){var r=this;e.isDel?this.$modalSure().then((function(){Object(n["b"])({orderNo:e.orderNo}).then((function(){r.$message.success("删除成功"),r.tableData.data.splice(t,1)}))})):this.$confirm("您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！","提示",{confirmButtonText:"确定",type:"error"})},onOrderDetails:function(e){this.orderNo=e,this.$refs.orderDetail.getDetail(e),this.$refs.orderDetail.getOrderInvoiceList(e),this.$refs.orderDetail.dialogVisible=!0},getDetail:function(e){var t=this;this.loading=!0,Object(n["c"])(e).then((function(e){t.orderDatalist=e,t.loading=!1})).catch((function(){t.orderDatalist=null,t.loading=!1}))},onOrderLog:function(e){var t=this;this.dialogVisibleJI=!0,this.LogLoading=!0,this.tableFromLog.orderNo=e,Object(n["f"])(this.tableFromLog).then((function(e){t.tableDataLog.data=e.list,t.tableDataLog.total=e.total,t.LogLoading=!1})).catch((function(){t.LogLoading=!1}))},pageChangeLog:function(e){this.tableFromLog.page=e,this.onOrderLog()},handleSizeChangeLog:function(e){this.tableFromLog.limit=e,this.onOrderLog()},handleClose:function(){this.dialogVisible=!1},onOrderMark:function(e){var t=this;this.$prompt("订单备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入订单备注",inputType:"textarea",inputValue:e.merRemark,inputPlaceholder:"请输入订单备注",inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(r){var i=r.value;Object(n["g"])({remark:i,orderNo:e.orderNo}).then((function(){t.$message.success("操作成功"),t.getList()}))})).catch((function(){t.$message.info("取消输入")}))},handleSelectionChange:function(e){this.selectionList=e;var t=[];this.selectionList.map((function(e){t.push(e.orderNo)})),this.ids=t.join(",")},selectChange:function(e){this.timeVal=[],this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onchangeTime:function(e){this.timeVal=e,this.tableFrom.dateLimit=e?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},getList:function(){var e=this;this.listLoading=!0,Object(n["e"])(this.tableFrom).then((function(t){e.tableData.data=t.list||[],e.tableData.total=t.total,e.listLoading=!1,e.checkedCities=e.$cache.local.has("order_stroge")?e.$cache.local.getJSON("order_stroge"):e.checkedCities})).catch((function(){e.listLoading=!1}))},getOrderStatusNum:function(){var e=this;Object(n["m"])({dateLimit:this.tableFrom.dateLimit,type:this.tableFrom.type}).then((function(t){e.orderChartType=t}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},exports:function(){var e={dateLimit:this.tableFrom.dateLimit,orderNo:this.tableFrom.orderNo,status:this.tableFrom.status,type:this.tableFrom.type};Object(N["u"])(e).then((function(e){window.open(e.fileName)}))},renderHeader:function(e){var t=this;return e("p",[e("span",{style:"padding-right:5px;"},["操作"]),e("i",{class:"el-icon-setting",on:{click:function(){return t.handleAddItem()}}})])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCheckAllChange:function(e){this.checkedCities=e?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.columnData.length,this.isIndeterminate=t>0&&t<this.columnData.length},checkSave:function(){this.$set(this,"card_select_show",!1),this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("order_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)},onOrderPrint:function(e){var t=this;Object(n["h"])(e.orderNo).then((function(e){t.$modal.msgSuccess("打印成功")})).catch((function(e){t.$modal.msgError(e.message)}))}}},O=T,$=(r("a708"),Object(k["a"])(O,i,o,!1,null,"519630ef",null));t["default"]=$.exports},a708:function(e,t,r){"use strict";r("0836")},ccfc:function(e,t,r){"use strict";r("de4e")},de4e:function(e,t,r){}}]);