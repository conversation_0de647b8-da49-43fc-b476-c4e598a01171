<template>
  <div class="app-container">
    <div class="content-wrapper">
      <el-form ref="addForm" :model="formData" :rules="formRules" label-width="120px" size="small">
        <!-- 基础信息 -->
        <div class="section-header">
          <span>基础信息</span>
        </div>
        <div class="form-section">
          <el-form-item label="活动名称：" prop="activityName" required>
            <el-input v-model="formData.activityName" placeholder="请输入活动名称" style="width: 400px" />
          </el-form-item>

          <el-form-item label="后台活动名称：" prop="backendName" required>
            <el-input v-model="formData.backendName" placeholder="请输入后台活动名称" style="width: 400px" />
          </el-form-item>

          <el-form-item label="活动描述：" prop="description" required>
            <el-input v-model="formData.description" placeholder="请输入活动描述" style="width: 400px" />
          </el-form-item>

          <el-form-item label="活动标签：" prop="activityType" required>
            <el-radio-group v-model="formData.activityType">
              <el-radio :label="3">新人价</el-radio>
            </el-radio-group>
            <!-- <el-input
              v-model="formData.newPriceValue"
              placeholder="精确到两位小数"
              style="width: 200px; margin-left: 10px"
              @input="handlenewPriceValueChange"
            /> -->
          </el-form-item>

          <el-form-item label="活动时间：" prop="activityTime" required>
            <el-date-picker
              v-model="formData.activityTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 400px"
            />
          </el-form-item>
        </div>

        <!-- 限制条件 -->
        <div class="section-header">
          <span>限制条件</span>
        </div>
        <div class="form-section">
          <el-form-item label="活动对象：" prop="activityTarget" required>
            <el-radio-group v-model="formData.activityTarget">
              <el-radio :label="1">首次下单</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="是否可与其他优惠券叠加使用：" prop="canCombine" required>
            <el-radio-group v-model="formData.canCombine">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="限购数量：" prop="limitType" required>
            <el-radio-group v-model="formData.limitType" @change="handleLimitTypeChange">
              <el-radio :label="false">不限</el-radio>
              <el-radio :label="true">限</el-radio>
            </el-radio-group>
            <el-input
              v-model="formData.limitQuantity"
              placeholder="1-999"
              style="width: 150px; margin-left: 10px"
              @focus="handleLimitQuantityFocus"
              @input="handleLimitQuantityChange"
            />
            <span style="margin-left: 5px">次</span>
          </el-form-item>

          <el-form-item label="购买限制：" prop="buyLimit" required>
            <span style="margin-right: 10px">每单购买的商品需要满</span>
            <el-input v-model="formData.buyLimit" placeholder="精确到两位小数" style="width: 100px" />
            <span style="margin-left: 5px">元</span>
          </el-form-item>

          <el-form-item label="活动商品：" prop="productType" required>
            <el-radio-group v-model="formData.productType">
              <el-radio :label="2">商户商品</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-button class="add-product-btn" size="small" @click="addProduct">添加商品</el-button>
            <span style="margin-left: 10px; color: #999">已选{{ selectedProductCount }}件</span>
          </el-form-item>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-wrapper">
          <el-button type="primary" size="medium" @click="submitForm">提交</el-button>
        </div>
      </el-form>
    </div>

    <!-- 添加商品弹窗 -->
    <el-dialog title="添加商品" :visible.sync="addProductDialogVisible" width="80%" :close-on-click-modal="false">
      <!-- 标签页 - 参考detail.vue的样式 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 已选商品选项卡 -->
        <el-tab-pane label="已选商品" name="selected">
          <!-- 搜索区域 -->
          <!-- 已选商品搜索区域 -->
          <div class="search-form">
            <el-form :inline="true" size="mini">
              <el-form-item label="商品名称">
                <el-input
                  v-model="selectedProductSearch.name"
                  placeholder="请输入商品名称"
                  style="width: 200px"
                  @keyup.enter="searchSelectedProducts"
                />
              </el-form-item>
              <el-form-item label="商品状态">
                <el-select
                  v-model="selectedProductSearch.status"
                  placeholder="请选择状态"
                  style="width: 120px"
                  @change="searchSelectedProducts"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option label="上架" value="1"></el-option>
                  <el-option label="下架" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchSelectedProducts">搜索</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 商品表格 - 参考index.vue的表格样式 -->
          <el-table
            v-loading="selectedProductLoading"
            :data="selectedProductList"
            size="mini"
            class="table"
            highlight-current-row
            :header-cell-style="{ fontWeight: 'bold' }"
            @selection-change="handleSelectedProductSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="商品ID" min-width="80"></el-table-column>
            <el-table-column label="商品图片" min-width="100">
              <template slot-scope="scope">
                <img :src="scope.row.image" style="width: 60px; height: 60px; object-fit: cover" />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" min-width="150"></el-table-column>
            <el-table-column label="采购价(元)" min-width="120">
              <template slot-scope="scope">
                {{ scope.row.cost }}
              </template>
            </el-table-column>
            <el-table-column label="商品原价(元)" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.originalPrice"
                  placeholder="商品原价"
                  size="mini"
                  style="width: 100px"
                  disabled
                />
              </template>
            </el-table-column>
            <el-table-column label="活动价(元)" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.activityPrice"
                  placeholder="请输入活动价"
                  size="mini"
                  style="width: 100px"
                  @input="handleActivityPriceChange(scope.row, $event)"
                />
              </template>
            </el-table-column>
            <el-table-column label="奖励(%)" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.award"
                  placeholder="请输入奖励百分比"
                  size="mini"
                  style="width: 100px"
                  @input="handleAwardChange(scope.row, $event)"
                />
              </template>
            </el-table-column>
            <el-table-column label="库存" min-width="100">
              <template slot-scope="scope">
                {{ scope.row.stock }}
              </template>
            </el-table-column>
            <el-table-column label="商品状态" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.status === 1 ? '上架' : '下架' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="每人限购数量" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.limitQuantity"
                  placeholder="限购数量"
                  size="mini"
                  style="width: 100px"
                  :value="formData.limitType ? formData.limitQuantity || '0' : '0'"
                  @input="scope.row.limitQuantity = $event"
                  disabled
                />
                <!-- <span style="margin-left: 5px; color: #999; font-size: 12px">
                    {{ formData.limitType ? (formData.limitQuantity ? `限${formData.limitQuantity}件` : '不限制') : '不限制' }}
                  </span> -->
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="80" fixed="right">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  style="color: #f56c6c"
                  @click="deleteProduct(scope.row, scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 底部操作区域 -->
          <div class="table-footer">
            <!-- 左下角删除按钮 -->
            <div class="footer-left">
              <el-button
                size="small"
                class="delete-selected-btn"
                :disabled="selectedProductIds.length === 0"
                @click="batchDeleteProducts"
              >
                删除选中商品
              </el-button>
            </div>

            <!-- 右侧分页 -->
            <div class="footer-right">
              <!-- <el-pagination
                @size-change="handleSelectedProductSizeChange"
                @current-change="handleSelectedProductPageChange"
                :current-page="selectedProductPagination.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="selectedProductPagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="selectedProductPagination.total"
              >
              </el-pagination> -->
            </div>
          </div>
        </el-tab-pane>

        <!-- 未选商品选项卡 -->
        <el-tab-pane label="未选商品" name="unselected">
          <!-- 搜索区域 -->
          <div class="search-form">
            <el-form :inline="true" size="mini">
              <el-form-item label="商品名称">
                <el-input
                  v-model="unselectedProductSearch.name"
                  placeholder="请输入商品名称"
                  style="width: 200px"
                  @keyup.enter="searchUnselectedProducts"
                />
              </el-form-item>
              <el-form-item label="商品状态">
                <el-select
                  v-model="unselectedProductSearch.status"
                  placeholder="请选择状态"
                  style="width: 120px"
                  @change="searchUnselectedProducts"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option label="上架" value="1"></el-option>
                  <el-option label="下架" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchUnselectedProducts">搜索</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 商品表格 -->
          <el-table
            v-loading="unselectedProductLoading"
            :data="filteredUnselectedProductList"
            size="mini"
            class="table"
            highlight-current-row
            :header-cell-style="{ fontWeight: 'bold' }"
            @selection-change="handleUnselectedProductSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="商品ID" min-width="80"></el-table-column>
            <el-table-column label="商品图片" min-width="100">
              <template slot-scope="scope">
                <img :src="scope.row.image" style="width: 60px; height: 60px; object-fit: cover" />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" min-width="150"></el-table-column>
            <el-table-column label="库存" min-width="100">
              <template slot-scope="scope">
                {{ scope.row.stock }}
              </template>
            </el-table-column>
            <el-table-column label="商品状态" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.status === 1 ? '上架' : '下架' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="80" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color: #409eff" @click="addToSelected(scope.row)">
                  添加
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 底部操作区域 -->
          <div class="table-footer">
            <!-- 左下角批量添加按钮 -->
            <div class="footer-left">
              <el-button
                size="small"
                class="add-selected-btn"
                :disabled="unselectedProductIds.length === 0"
                @click="batchAddToSelected"
              >
                批量添加选中商品
              </el-button>
            </div>
            <!-- 右侧分页 -->
            <div class="footer-right">
              <!-- <el-pagination
                @size-change="handleUnselectedProductSizeChange"
                @current-change="handleUnselectedProductPageChange"
                :current-page="unselectedProductPagination.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="unselectedProductPagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="filteredUnselectedProductTotal"
              >
              </el-pagination> -->
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmAddProducts">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { productQueryListApi } from '@/api/product';
import { discountAddApi, activityProductBatchAddApi } from '@/api/discount';

export default {
  name: 'DiscountAdd',
  data() {
    return {
      formData: {
        activityName: '',
        backendName: '',
        description: '',
        activityType: 3,
        newPriceValue: '',
        activityTime: [],
        activityTarget: 1,
        canCombine: true,
        limitType: false,
        limitQuantity: '',
        buyLimit: '0',
        productType: 2,
      },
      formRules: {
        activityName: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        backendName: [{ required: true, message: '请输入后台活动名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入活动描述', trigger: 'blur' }],
        activityTime: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        activityTarget: [{ required: true, message: '请选择活动对象', trigger: 'change' }],
        canCombine: [{ required: true, message: '请选择是否可叠加使用', trigger: 'change' }],
        limitType: [{ required: true, message: '请选择限购类型', trigger: 'change' }],
        buyLimit: [
          { required: true, message: '请输入购买限制', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const numValue = parseFloat(value);
              if (isNaN(numValue) || numValue < 0 || numValue > 999999.99) {
                callback(new Error('购买限制金额必须在0.00-999999.99元之间'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        limitQuantity: [
          {
            validator: (rule, value, callback) => {
              // 如果启用了限购，则验证数量
              if (this.formData.limitType) {
                const numValue = parseInt(value);
                if (isNaN(numValue) || numValue < 1 || numValue > 999) {
                  callback(new Error('限购数量必须在1-999之间'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        productType: [{ required: true, message: '请选择活动商品类型', trigger: 'change' }],
      },

      // 添加商品弹窗相关
      addProductDialogVisible: false,
      activeTab: 'selected',
      selectedProductIds: [],
      selectedProductLoading: false,

      // 已选商品搜索
      selectedProductSearch: {
        name: '',
        status: '',
      },

      // 已选商品分页
      selectedProductPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 未选商品相关数据
      unselectedProductIds: [],
      unselectedProductLoading: false,
      unselectedProductSearch: {
        name: '',
        status: '',
      },
      unselectedProductList: [
        {
          id: 3,
          image: 'https://via.placeholder.com/60x60',
          name: '纯钛近视眼镜架全框配...',
          stock: 1100,
          status: 1,
        },
        {
          id: 4,
          image: 'https://via.placeholder.com/60x60',
          name: '纯钛近视眼镜架全框配...',
          stock: 1100,
          status: 1,
        },
      ],
      unselectedProductPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 2,
      },

      // 商品暂存数据
      tempSelectedProducts: [], // 暂存的已选商品列表

      // 添加原始数据存储
      originalUnselectedProductList: [], // 存储原始未选商品数据

      // 提交状态
      submitLoading: false,
    };
  },
  computed: {
    /**
     * 已选商品列表 - 支持搜索过滤
     */
    selectedProductList() {
      let list = this.tempSelectedProducts;

      // 按商品名称搜索
      if (this.selectedProductSearch.name) {
        list = list.filter((item) => item.name.toLowerCase().includes(this.selectedProductSearch.name.toLowerCase()));
      }

      // 按商品状态搜索
      if (this.selectedProductSearch.status !== '') {
        list = list.filter((item) => item.status.toString() === this.selectedProductSearch.status);
      }

      return list;
    },

    /**
     * 已选商品总数
     */
    selectedProductCount() {
      return this.tempSelectedProducts.length;
    },

    /**
     * 过滤后的未选商品列表 - 排除已添加的商品并支持搜索过滤
     */
    filteredUnselectedProductList() {
      const selectedIds = this.tempSelectedProducts.map((item) => item.id);
      let list = this.unselectedProductList.filter((item) => !selectedIds.includes(item.id));

      // 按商品名称搜索
      if (this.unselectedProductSearch.name) {
        list = list.filter((item) => item.name.toLowerCase().includes(this.unselectedProductSearch.name.toLowerCase()));
      }

      // 按商品状态搜索
      if (this.unselectedProductSearch.status !== '') {
        list = list.filter((item) => item.status.toString() === this.unselectedProductSearch.status);
      }

      return list;
    },
    filteredUnselectedProductTotal() {
      return this.filteredUnselectedProductList.length;
    },
  },
  methods: {
    /**
     * 限购类型变化处理
     * @param {String} val - 限购类型值
     */
    handleLimitTypeChange(val) {
      if (!val) {
        this.formData.limitQuantity = '';
      }

      // 同步更新已选商品的限购数量
      this.tempSelectedProducts.forEach((product) => {
        product.limitQuantity = val ? this.formData.limitQuantity || '0' : '0';
      });
    },

    /**
     * 限购数量输入框获得焦点时处理
     */
    handleLimitQuantityFocus() {
      if (!this.formData.limitType) {
        this.formData.limitType = true;
      }
    },

    /**
     * 限购数量变化处理
     */
    handleLimitQuantityChange() {
      // 同步更新已选商品的限购数量
      this.tempSelectedProducts.forEach((product) => {
        product.limitQuantity = this.formData.limitType ? this.formData.limitQuantity || '0' : '0';
      });
    },

    // 添加商品
    addProduct() {
      this.addProductDialogVisible = true;
      this.activeTab = 'selected';
    },

    /**
     * 搜索已选商品
     */
    searchSelectedProducts() {
      // 搜索逻辑已通过计算属性实现，这里只需要触发重新计算
      this.$forceUpdate();
    },

    // 已选商品表格选择变化
    handleSelectedProductSelectionChange(selection) {
      this.selectedProductIds = selection.map((item) => item.id);
    },

    // 批量删除商品
    batchDeleteProducts() {
      this.$confirm('确认删除选中的商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 删除选中的商品
          this.selectedProductList = this.selectedProductList.filter(
            (item) => !this.selectedProductIds.includes(item.id),
          );
          this.selectedProductIds = [];
          this.updateSelectedProductCount();
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 删除单个商品
    deleteProduct(row, index) {
      this.$confirm('确认删除该商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.selectedProductList.splice(index, 1);
          this.updateSelectedProductCount();
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 已选商品分页大小变化
    handleSelectedProductSizeChange(val) {
      this.selectedProductPagination.pageSize = val;
      this.selectedProductPagination.currentPage = 1;
      // 重新加载数据
    },

    // 已选商品页码变化
    handleSelectedProductPageChange(val) {
      this.selectedProductPagination.currentPage = val;
      // 重新加载数据
    },

    // 搜索未选商品
    searchUnselectedProducts() {
      this.unselectedProductPagination.currentPage = 1;
      this.$forceUpdate();
    },

    // 未选商品表格选择变化
    handleUnselectedProductSelectionChange(selection) {
      this.unselectedProductIds = selection.map((item) => item.id);
    },

    /**
     * 添加商品到已选列表（修改为本地操作）
     * @param {Object} product - 商品对象
     */
    addToSelected(product) {
      // 检查是否已存在
      const existingIndex = this.tempSelectedProducts.findIndex((item) => item.id === product.id);
      if (existingIndex !== -1) {
        this.$message.warning('该商品已添加');
        return;
      }

      // 添加商品并设置默认值
      const newProduct = {
        ...product,
        originalPrice: product.otPrice || '', // 使用后端返回的otPrice作为市场价
        discount: this.formData.newPriceValue, // 自动填入表单设置的折扣值
        activityPrice: '', // 初始化为空，提示用户设置活动价
        award: '', // 初始化奖励百分比为空
        limitQuantity: this.formData.limitType ? this.formData.limitQuantity || '0' : '0',
        singleLimitQuantity: '',
      };

      this.tempSelectedProducts.push(newProduct);

      // 从原始数据中移除
      this.originalUnselectedProductList = this.originalUnselectedProductList.filter((item) => item.id !== product.id);

      // 更新过滤后的列表
      this.updateFilteredProductLists();

      this.updateSelectedProductCount();
      this.$message.success('添加成功，请设置活动价格和奖励百分比');
    },

    /**
     * 批量添加选中商品到已选列表（修改为本地操作）
     */
    batchAddToSelected() {
      if (this.unselectedProductIds.length === 0) {
        this.$message.warning('请先选择要添加的商品');
        return;
      }

      let addedCount = 0;
      const productsToAdd = this.unselectedProductList.filter((item) => this.unselectedProductIds.includes(item.id));

      productsToAdd.forEach((product) => {
        // 检查是否已存在
        const existingIndex = this.tempSelectedProducts.findIndex((item) => item.id === product.id);
        if (existingIndex === -1) {
          // 添加商品并设置默认值
          const newProduct = {
            ...product,
            originalPrice: product.otPrice || '', // 使用后端返回的otPrice作为市场价
            discount: this.formData.newPriceValue, // 自动填入表单设置的折扣值
            activityPrice: '', // 初始化为空，提示用户设置活动价
            award: '', // 初始化奖励百分比为空
            limitQuantity: this.formData.limitType ? this.formData.limitQuantity || '0' : '0',
            singleLimitQuantity: '',
          };
          this.tempSelectedProducts.push(newProduct);
          addedCount++;
        }
      });

      // 从原始数据中移除
      this.originalUnselectedProductList = this.originalUnselectedProductList.filter(
        (item) => !this.unselectedProductIds.includes(item.id),
      );

      // 更新过滤后的列表
      this.updateFilteredProductLists();

      this.updateSelectedProductCount();
      this.unselectedProductIds = [];
      this.$message.success(`成功添加${addedCount}件商品，请设置活动价格和奖励百分比`);
    },

    /**
     * 删除单个商品（修改为本地操作）
     * @param {Object} row - 商品行数据
     * @param {Number} index - 索引
     */
    deleteProduct(row, index) {
      this.$confirm('确认删除该商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 从已选列表中移除
          this.tempSelectedProducts.splice(index, 1);

          // 添加回原始数据
          this.originalUnselectedProductList.push({
            id: row.id,
            name: row.name,
            otPrice: row.originalPrice,
            status: row.status,
            stock: row.stock,
            image: row.image,
            cost: row.cost,
            // 其他必要字段
          });

          // 更新过滤后的列表
          this.updateFilteredProductLists();

          this.updateSelectedProductCount();
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    /**
     * 批量删除商品（修改为本地操作）
     */
    batchDeleteProducts() {
      this.$confirm('确认删除选中的商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 获取要删除的商品
          const productsToDelete = this.tempSelectedProducts.filter((item) =>
            this.selectedProductIds.includes(item.id),
          );

          // 从已选列表中移除
          this.tempSelectedProducts = this.tempSelectedProducts.filter(
            (item) => !this.selectedProductIds.includes(item.id),
          );

          // 添加回原始数据
          productsToDelete.forEach((product) => {
            this.originalUnselectedProductList.push({
              id: product.id,
              name: product.name,
              otPrice: product.originalPrice,
              status: product.status,
              stock: product.stock,
              image: product.image,
              cost: product.cost,
              // 其他必要字段
            });
          });

          // 更新过滤后的列表
          this.updateFilteredProductLists();

          this.selectedProductIds = [];
          this.updateSelectedProductCount();
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    /**
     * 处理活动价格变化
     * @param {Object} row - 商品行数据
     * @param {String} value - 新的价格值
     */
    handleActivityPriceChange(row, value) {
      // 验证价格格式
      if (value && !/^\d+(\.\d{1,2})?$/.test(value)) {
        this.$message.error('请输入正确的价格格式，最多保留两位小数');
        return;
      }

      // 验证活动价不能高于原价
      const activityPrice = parseFloat(value) || 0;
      const originalPrice = parseFloat(row.originalPrice) || 0;

      if (activityPrice > originalPrice) {
        this.$message.error('活动价不能高于商品原价');
        row.activityPrice = originalPrice.toFixed(2);
        return;
      }

      row.activityPrice = value;
    },

    /**
     * 奖励百分比变化处理
     * @param {Object} product - 商品对象
     * @param {String} value - 输入的奖励百分比
     */
    handleAwardChange(product, value) {
      // 验证输入的百分比格式
      const award = parseFloat(value);
      if (isNaN(award) || award < 0 || award > 100) {
        this.$message.warning('请输入0-100之间的有效百分比');
        return;
      }

      // 更新商品的奖励百分比
      product.award = value;
    },

    /**
     * 确认添加商品（关闭对话框）
     */
    confirmAddProducts() {
      this.updateSelectedProductCount();
      this.addProductDialogVisible = false;
      this.$message.success('商品设置成功');
    },

    /**
     * 更新已选商品数量
     */
    updateSelectedProductCount() {
      this.selectedProductPagination.total = this.tempSelectedProducts.length;
    },

    /**
     * 带重试机制的活动商品批量添加
     * @param {Object} productData - 商品数据
     * @param {Number} maxRetries - 最大重试次数
     * @param {Number} retryDelay - 重试间隔（毫秒）
     * @returns {Promise<Boolean>} - 成功返回true，失败返回false
     */
    async retryActivityProductBatchAdd(productData, maxRetries = 3, retryDelay = 1000) {
      let lastError = null;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          await activityProductBatchAddApi(productData);
          return true; // 成功
        } catch (error) {
          lastError = error;
          console.warn(`商品批量添加第${attempt}次尝试失败:`, error);

          if (attempt < maxRetries) {
            this.$message.warning(`商品添加失败，正在进行第${attempt + 1}次重试...`);
            // 等待指定时间后重试
            await new Promise((resolve) => setTimeout(resolve, retryDelay));
          }
        }
      }

      // 所有重试都失败
      console.error('商品批量添加所有重试都失败:', lastError);
      return false;
    },

    async submitForm() {
      // 校验是否至少选择了一件商品
      if (this.tempSelectedProducts.length === 0) {
        this.$message.error('请至少选择一件商品');
        return;
      }

      // 校验所有商品是否都设置了活动价和奖励百分比
      for (let product of this.tempSelectedProducts) {
        if (!product.activityPrice || product.activityPrice === '') {
          this.$message.error(`商品"${product.name}"未设置活动价格`);
          return;
        }

        if (!product.award || product.award === '') {
          this.$message.error(`商品"${product.name}"未设置奖励百分比`);
          return;
        }

        const activityPrice = parseFloat(product.activityPrice);
        const originalPrice = parseFloat(product.originalPrice);
        const award = parseFloat(product.award);

        if (isNaN(activityPrice) || activityPrice <= 0) {
          this.$message.error(`商品"${product.name}"的活动价格无效`);
          return;
        }

        if (isNaN(award) || award < 0 || award > 100) {
          this.$message.error(`商品"${product.name}"的奖励百分比无效，请输入0-100之间的数值`);
          return;
        }

        if (activityPrice > originalPrice) {
          this.$message.error(`商品"${product.name}"的活动价不能高于原价`);
          return;
        }
      }

      // 自定义校验：购买限制金额和限购数量（无论是否选择商品都校验）
      const buyLimit = parseFloat(this.formData.buyLimit) || 0;
      if (buyLimit < 0 || buyLimit > 999999.99) {
        this.$message.error('购买限制金额必须在0.00-999999.99元之间');
        return;
      }

      // 如果启用了限购，校验限购数量
      if (this.formData.limitType) {
        const limitQuantity = parseInt(this.formData.limitQuantity) || 0;
        if (limitQuantity < 1 || limitQuantity > 999) {
          this.$message.error('限购数量必须在1-999之间');
          return;
        }
      }

      // 表单验证
      const valid = await this.$refs.addForm.validate().catch(() => false);
      if (!valid) {
        return;
      }

      // 构造提交数据
      const submitData = {
        name: this.formData.activityName,
        backName: this.formData.backendName,
        instruction: this.formData.description,
        type: this.formData.activityType.toString(),
        newPrice: parseFloat(this.formData.newPriceValue),
        beginTime: this.formData.activityTime[0],
        overTime: this.formData.activityTime[1],
        target: this.formData.activityTarget.toString(),
        createBy: this.$store.getters.name, // 当前登录用户的用户名
      };

      try {
        this.submitLoading = true;

        // 1. 创建折扣活动
        const activityResult = await discountAddApi(submitData);
        const activityId = activityResult.activityId;

        // 2. 如果有选择商品，则批量添加活动商品
        if (this.tempSelectedProducts.length > 0) {
          const productIds = this.tempSelectedProducts.map((product) => product.id);

          const productData = {
            productIds: productIds,
            activityId: activityId,
            merchantId: 1,
            ifCoupon: this.formData.canCombine,
            astrictNumber: this.formData.limitType ? parseInt(this.formData.limitQuantity) || 0 : 0,
            astrictAmount: parseFloat(this.formData.buyLimit) || 0,
            productType: '2',
            remark: '',
            // 添加商品价格信息
            productPrices: this.tempSelectedProducts.map((product) => ({
              productId: product.id,
              activityPrice: parseFloat(product.activityPrice),
              award: parseFloat(product.award),
              limitQuantity: parseInt(product.limitQuantity) || 0,
            })),
          };

          // 使用重试机制添加商品
          const addProductSuccess = await this.retryActivityProductBatchAdd(productData);

          if (addProductSuccess) {
            this.$message.success('新建新人活动成功');
          } else {
            this.$message.warning('新人活动创建成功，但商品添加失败，请手动添加商品');
          }
        } else {
          this.$message.success('新建新人活动成功');
        }

        // 跳转到列表页面
        this.$router.push('/marketing/newuser');
      } catch (error) {
        console.error('提交失败:', error);
        if (error.message && error.message.includes('activityId')) {
          this.$message.error('活动创建失败，请重试');
        } else {
          this.$message.error('提交失败，请重试');
        }
      } finally {
        this.submitLoading = false;
      }
    },

    // 未选商品分页大小变化
    handleUnselectedProductSizeChange(val) {
      this.unselectedProductPagination.pageSize = val;
      this.unselectedProductPagination.currentPage = 1;
      // 本地分页，不需要重新请求数据
    },

    /**
     * 获取所有商品列表（重命名并修改原 getUnselectedProductList 方法）
     */
    async getAllProductList() {
      this.unselectedProductLoading = true;
      try {
        const params = {
          limit: -1,
          activityId: -1, // -1表示没有关联活动的商品
        };

        const response = await productQueryListApi(params);
        const allProducts = response.list || [];

        // 存储原始数据
        this.originalUnselectedProductList = allProducts;

        // 初始化过滤后的列表
        this.updateFilteredProductLists();
      } catch (error) {
        console.error('获取商品列表失败:', error);
        this.$message.error('获取商品列表失败');
      } finally {
        this.unselectedProductLoading = false;
      }
    },

    /**
     * 更新过滤后的商品列表（本地过滤）
     */
    updateFilteredProductLists() {
      let filteredProducts = [...this.originalUnselectedProductList];

      // 根据搜索条件过滤
      if (this.unselectedProductSearch.status) {
        filteredProducts = filteredProducts.filter(
          (product) => product.status.toString() === this.unselectedProductSearch.status,
        );
      }

      if (this.unselectedProductSearch.name) {
        const searchName = this.unselectedProductSearch.name.toLowerCase();
        filteredProducts = filteredProducts.filter((product) => product.name.toLowerCase().includes(searchName));
      }

      // 更新未选商品列表
      this.unselectedProductList = filteredProducts;
      this.unselectedProductPagination.total = filteredProducts.length;
    },

    /**
     * 搜索未选商品（修改为本地搜索）
     */
    searchUnselectedProducts() {
      this.unselectedProductPagination.currentPage = 1;
      // 使用本地过滤而不是后端请求
      this.updateFilteredProductLists();
    },

    /**
     * 未选商品页码变化
     * @param {Number} val - 页码
     */
    handleUnselectedProductPageChange(val) {
      this.unselectedProductPagination.currentPage = val;
      // 本地分页，不需要重新请求数据
    },

    /**
     * 添加商品弹窗打开时初始化数据
     */
    addProduct() {
      this.addProductDialogVisible = true;
      this.activeTab = 'selected';
      // 一次性获取所有商品数据
      this.getAllProductList();
    },
    /**
     * 折扣值变化处理
     */
    handlenewPriceValueChange() {
      // 同步更新已选商品的折扣值和活动价格
      this.tempSelectedProducts.forEach((product) => {
        product.discount = this.formData.newPriceValue;
        // 直接使用新人价作为活动价格
        const newPrice = parseFloat(this.formData.newPriceValue) || 0;
        product.activityPrice = newPrice.toFixed(2);
      });
    },
  },

  mounted() {
    this.updateSelectedProductCount();
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.app-breadcrumb {
  margin-bottom: 20px;
  font-size: 14px;
}

.content-wrapper {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
}

.section-header {
  background-color: #f5f7fa;
  padding: 12px 16px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #333;
}

.form-section {
  padding: 0 16px 20px 16px;
  margin-bottom: 20px;
}

.submit-wrapper {
  text-align: right;
  padding-top: 20px;
}

.add-product-btn {
  background-color: white;
  border: 1px solid #409eff;
  color: #409eff;

  &:hover {
    background-color: white;
    border-color: #66b1ff;
    color: #66b1ff;
  }

  &:focus {
    background-color: white;
    border-color: #409eff;
    color: #409eff;
  }
}

::v-deep .el-form-item {
  margin-bottom: 20px;
}

::v-deep .el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

// 弹窗内容样式
.search-section {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.batch-actions {
  margin-bottom: 10px;
}

.pagination-wrapper {
  text-align: right;
  margin-top: 20px;
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}

::v-deep .el-tabs__content {
  padding: 8px 0;
}

// 搜索表单样式
.search-form {
  padding: 0 0 8px 0;
  margin-bottom: 8px;
}

::v-deep .el-table th {
  background-color: #fafafa;
}

// 标签页样式 - 参考detail.vue
.detail-tabs {
  ::v-deep .el-tabs__header {
    margin: 0 0 20px 0;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    display: none;
  }

  ::v-deep .el-tabs__item {
    padding: 0 20px;
    height: 40px;
    line-height: 40px;
  }
}

// 搜索区域样式 - 移除背景色
.search-section {
  padding: 15px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

// 表格样式 - 参考index.vue，移除奇怪颜色
.table {
  ::v-deep .el-table__header {
    th {
      background-color: #fafafa;
    }
  }
}

// 底部操作区域
.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;

  .footer-left {
    flex: 1;
  }

  .footer-right {
    flex: 1;
    text-align: right;
  }
}

// 删除选中商品按钮样式
.delete-selected-btn {
  background-color: white;
  border: 1px solid #409eff;
  color: #409eff;

  &:hover {
    background-color: white;
    border-color: #66b1ff;
    color: #66b1ff;
  }

  &:focus {
    background-color: white;
    border-color: #409eff;
    color: #409eff;
  }

  &:disabled {
    background-color: white;
    border-color: #dcdfe6;
    color: #c0c4cc;
  }
}

// 批量添加选中商品按钮样式
.add-selected-btn {
  background-color: white;
  border: 1px solid #409eff;
  color: #409eff;

  &:hover {
    background-color: white;
    border-color: #66b1ff;
    color: #66b1ff;
  }

  &:focus {
    background-color: white;
    border-color: #409eff;
    color: #409eff;
  }

  &:disabled {
    background-color: white;
    border-color: #dcdfe6;
    color: #c0c4cc;
  }
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
