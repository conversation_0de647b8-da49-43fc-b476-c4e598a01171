(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0a41f1"],{"04a8":function(e,t){(function(){if(window.frameElement.id){var e=window.parent,t=e.$EDITORUI[window.frameElement.id.replace(/_iframe$/,"")],a=t.editor,o=e.UE,n=o.dom.domUtils,i=o.utils,r=(o.browser,o.ajax,function(e){return document.getElementById(e)});window.nowEditor={editor:a,dialog:t},i.loadFile(document,{href:a.options.themePath+a.options.theme+"/dialogbase.css?cache="+Math.random(),tag:"link",type:"text/css",rel:"stylesheet"});var s=a.getLang(t.className.split("-")[2]);s&&n.on(window,"load",(function(){var e=a.options.langPath+a.options.lang+"/images/";for(var t in s["static"]){var o=r(t);if(o){var d=o.tagName,c=s["static"][t];switch(c.src&&(c=i.extend({},c,!1),c.src=e+c.src),c.style&&(c=i.extend({},c,!1),c.style=c.style.replace(/url\s*\(/g,"url("+e)),d.toLowerCase()){case"var":o.parentNode.replaceChild(document.createTextNode(c),o);break;case"select":for(var l,w=o.options,p=0;l=w[p];)l.innerHTML=c.options[p++];for(var m in c)"options"!=m&&o.setAttribute(m,c[m]);break;default:n.setAttributes(o,c)}}}}))}})()}}]);