(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4f76f55a"],{"4a25":function(t,e,a){},cd05:function(t,e,a){"use strict";var n=a("b775"),i=a("bc3a"),r=a.n(i),o=r.a.create({timeout:4e4});o.interceptors.request.use((function(t){return t}),(function(t){Promise.reject(t)})),o.interceptors.response.use((function(t){var e=t;return 200!==e.status&&401!==e.status?(Message({message:e.data.msg||"Error",type:"error",duration:5e3}),Promise.reject()):e.data}),(function(t){}));function s(t,e){return n["a"].get("store/order/reconciliation/".concat(t,"/order"),e)}function l(t,e){return n["a"].get("store/order/reconciliation/".concat(t,"/refund"),e)}function c(t){return Object(n["a"])({url:"admin/merchant/finance/funds/flow",method:"get",params:t})}function u(t){return n["a"].get("financial_record/export",t)}function d(t){return n["a"].get("financial/export",t)}function m(t){return Object(n["a"])({url:"admin/merchant/finance/closing/apply",method:"post",data:t})}function p(t){return Object(n["a"])({url:"admin/merchant/finance/closing/record/list",method:"GET",params:t})}function f(){return Object(n["a"])({url:"admin/merchant/finance/closing/base/info",method:"GET"})}function _(t){return Object(n["a"])({url:"admin/merchant/finance/closing/record/detail/".concat(t),method:"GET"})}function b(t){return Object(n["a"])({url:"admin/merchant/finance/daily/statement/list",method:"get",params:t})}function h(t){return Object(n["a"])({url:"admin/merchant/finance/month/statement/list",method:"get",params:t})}a.d(e,"i",(function(){return s})),a.d(e,"j",(function(){return l})),a.d(e,"b",(function(){return c})),a.d(e,"a",(function(){return u})),a.d(e,"k",(function(){return d})),a.d(e,"c",(function(){return m})),a.d(e,"f",(function(){return p})),a.d(e,"d",(function(){return f})),a.d(e,"e",(function(){return _})),a.d(e,"g",(function(){return b})),a.d(e,"h",(function(){return h}))},d79c:function(t,e,a){"use strict";a("4a25")},f5bc:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card mb20"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("router-link",{attrs:{to:{path:"/finance/reconciliation"}}},[a("el-button",{staticClass:"mr20 mb20",attrs:{size:"small",icon:"el-icon-back"}},[t._v("返回")])],1)],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form",{staticClass:"demo-table-expand demo-table-expands",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"收货人："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.real_name)))])]),t._v(" "),a("el-form-item",{attrs:{label:"电话："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.user_phone)))])]),t._v(" "),a("el-form-item",{attrs:{label:"地址："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.user_address)))])]),t._v(" "),a("el-form-item",{attrs:{label:"商品总数："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.total_num)))])]),t._v(" "),a("el-form-item",{attrs:{label:"支付状态："}},[a("span",[t._v(t._s(t._f("payTypeFilter")(e.row.pay_type)))])]),t._v(" "),a("el-form-item",{attrs:{label:"支付时间："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.pay_time)))])]),t._v(" "),a("el-form-item",{attrs:{label:"对账备注："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.admin_mark)))])])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"order_id",label:"ID",width:"60"}}),t._v(" "),a("el-table-column",{attrs:{label:"是否对账","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("reconciliationFilter")(e.row.reconciliation_id)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"order_sn",label:"订单编号","min-width":"190"}}),t._v(" "),a("el-table-column",{attrs:{label:"商品信息","min-width":"330"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.orderProduct,(function(e,n){return a("div",{key:n,staticClass:"tabBox acea-row row-middle"},[a("div",{staticClass:"demo-image__preview"},[a("el-image",{attrs:{src:e.cart_info.product.image,"preview-src-list":[e.cart_info.product.image]}})],1),t._v(" "),a("span",{staticClass:"tabBox_tit"},[t._v(t._s(e.cart_info.product.store_name+" | ")+t._s(e.cart_info.productAttr.sku))]),t._v(" "),a("span",{staticClass:"tabBox_pice"},[t._v(t._s("￥"+e.cart_info.productAttr.price+" x "+e.product_num))])])}))}}])}),t._v(" "),a("el-table-column",{attrs:{label:"商品总价","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.getTotal(e.row.orderProduct)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"佣金金额","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(Number(e.row.extension_one)+Number(e.row.extension_two)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"pay_price",label:"实际支付","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"total_postage",label:"邮费","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"order_rate",label:"手续费","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"create_time",label:"下单时间","min-width":"150"}})],1),t._v(" "),a("div",{staticClass:"block mb20"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",staticStyle:{width:"100%"},attrs:{data:t.tableDataRefund.data,size:"mini","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form",{staticClass:"demo-table-expand demo-table-expandss",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"订单号："}},[a("span",[t._v(t._s(e.row.order.order_sn))])]),t._v(" "),a("el-form-item",{attrs:{label:"退款商品总价："}},[a("span",[t._v(t._s(t.getTotalRefund(e.row.refundProduct)))])]),t._v(" "),a("el-form-item",{attrs:{label:"退款商品总数："}},[a("span",[t._v(t._s(e.row.refund_num))])]),t._v(" "),a("el-form-item",{attrs:{label:"申请退款时间："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.create_time)))])]),t._v(" "),a("el-form-item",{attrs:{label:"对账备注："}},[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.admin_mark)))])])],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"refund_order_id",label:"ID",width:"60"}}),t._v(" "),a("el-table-column",{attrs:{label:"退款单号","min-width":"170"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticStyle:{display:"block"},domProps:{textContent:t._s(e.row.refund_order_sn)}}),t._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:e.row.is_del>0,expression:"scope.row.is_del > 0"}],staticStyle:{color:"#ed4014",display:"block"}},[t._v("用户已删除")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"是否对账","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("reconciliationFilter")(e.row.reconciliation_id)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"user.nickname",label:"用户信息","min-width":"130"}}),t._v(" "),a("el-table-column",{attrs:{prop:"refund_price",label:"退款金额","min-width":"130"}}),t._v(" "),a("el-table-column",{attrs:{prop:"nickname",label:"商品信息","min-width":"330"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.refundProduct,(function(e,n){return a("div",{key:n,staticClass:"tabBox acea-row row-middle"},[a("div",{staticClass:"demo-image__preview"},[a("el-image",{attrs:{src:e.product.cart_info.product.image,"preview-src-list":[e.product.cart_info.product.image]}})],1),t._v(" "),a("span",{staticClass:"tabBox_tit"},[t._v(t._s(e.product.cart_info.product.store_name+" | ")+t._s(e.product.cart_info.productAttr.sku))]),t._v(" "),a("span",{staticClass:"tabBox_pice"},[t._v(t._s("￥"+e.product.cart_info.productAttr.price+" x "+e.product.product_num))])])}))}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"serviceScore",label:"订单状态","min-width":"250"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticStyle:{display:"block"}},[t._v(t._s(t._f("orderRefundFilter")(e.row.status)))]),t._v(" "),a("span",{staticStyle:{display:"block"}},[t._v("退款原因："+t._s(e.row.refund_message))]),t._v(" "),a("span",{staticStyle:{display:"block"}},[t._v("状态变更时间："+t._s(e.row.status_time))])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableDataRefund.total},on:{"size-change":t.handleSizeChangeRefund,"current-change":t.pageChangeRefund}})],1)],1)],1)},i=[],r=a("cd05"),o={name:"Record",data:function(){return{chkName:"",chkNameRefund:"",isIndeterminate:!0,resource:[],visible:!1,timeVal:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-7776e6),t.$emit("pick",[a,e])}}]},listLoading:!0,tableData:{data:[],total:0},tableDataRefund:{data:[],total:0},tableFrom:{page:1,limit:10},ids:[],idsRefund:[]}},mounted:function(){this.getList(),this.getRefundList(),0===this.$route.params.type&&this.setTagsViewTitle()},created:function(){this.tempRoute=Object.assign({},this.$route)},methods:{isDisabled:function(t){return 3===t.status},onchangeTime:function(t){this.timeVal=t,this.tableFrom.data=this.timeVal?this.timeVal.join("-"):"",this.getList(),this.getRefundList()},getTotalRefund:function(t){for(var e=0,a=0;a<t.length;a++)e+=t[a].product.cart_info.productAttr.price*t[a].refund_num;return e},getTotal:function(t){for(var e=0,a=0;a<t.length;a++)e+=t[a].cart_info.productAttr.price*t[a].product_num;return e},getList:function(){var t=this;this.listLoading=!0,Object(r["i"])(this.$route.params.id,this.tableFrom).then((function(e){t.tableData.data=e.data.list,t.tableData.total=e.data.count,t.tableData.data.map((function(e){t.$set(e,{checked:!1})})),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e.message)}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.chkName="",this.getList()},getRefundList:function(){var t=this;this.listLoading=!0,Object(r["j"])(this.$route.params.id,this.tableFrom).then((function(e){t.tableDataRefund.data=e.data.list,t.tableDataRefund.total=e.data.count,t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e.message)}))},pageChangeRefund:function(t){this.tableFrom.page=t,this.getRefundList()},handleSizeChangeRefund:function(t){this.tableFrom.limit=t,this.getRefundList()},setTagsViewTitle:function(){var t="查看订单",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)}}},s=o,l=(a("d79c"),a("2877")),c=Object(l["a"])(s,n,i,!1,null,"10e1d635",null);e["default"]=c.exports}}]);