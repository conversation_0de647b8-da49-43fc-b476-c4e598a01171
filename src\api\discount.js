// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from '@/utils/request';

/**
 * 获取活动详情
 * @param {String|Number} id - 活动ID
 */
export function getActivityInfoApi(id) {
  return request({
    url: `/admin/merchant/marketing/activity/info/${id}`,
    method: 'get',
  });
}

/**
 * 折扣活动列表
 * @param {Object} params - 请求参数
 */
export function discountListApi(params) {
  return request({
    url: 'admin/merchant/marketing/activity/list',
    method: 'get',
    params,
  });
}

/**
 * 折扣活动详情
 * @param {Object} params - 请求参数
 */
export function discountInfoApi(params) {
  return request({
    url: '/admin/marketing/discount/info',
    method: 'get',
    params,
  });
}

/**
 * 新建折扣活动
 * @param {Object} data - 请求数据
 * @param {String} data.name - 活动名称
 * @param {String} data.backName - 后台活动名称
 * @param {String} data.instruction - 活动描述
 * @param {String} data.type - 活动标签
 * @param {Number} data.discount - 折扣比例
 * @param {String} data.beginTime - 活动开始时间
 * @param {String} data.overTime - 活动结束时间
 * @param {String} data.target - 活动对象
 * @param {Array} data.activityProducts - 活动商品列表
 */
export function discountAddApi(data) {
  return request({
    url: '/admin/merchant/marketing/activity/add',
    method: 'post',
    data,
  });
}

/**
 * 更新折扣活动
 * @param {Object} data - 请求数据
 */
export function discountUpdateApi(data) {
  return request({
    url: '/admin/merchant/marketing/activity/update/',
    method: 'post',
    data,
  });
}

/**
 * 删除折扣活动
 * @param {Object} params - 请求参数
 */
export function discountDeleteApi(params) {
  return request({
    url: '/admin/marketing/discount/delete',
    method: 'post',
    params,
  });
}

/**
 * 更新折扣活动状态
 * @param {Object} params - 请求参数
 * @param {String|Number} params.id - 营销活动ID
 * @param {String|Number} params.status - 状态值（0：未开始，1：进行中，2：已结束，3：已暂停）
 */
export function discountStatusApi(params) {
  return request({
    url: `/admin/merchant/marketing/activity/update/status/${params.id}`,
    method: 'post',
    params: {
      status: params.status,
    },
  });
}

/**
 * 批量添加活动商品
 * @param {Object} data - 请求数据
 * @param {Array} data.productIds - 商品ID数组
 * @param {Number} data.activityId - 活动ID
 * @param {Number} data.merchantId - 商户ID
 * @param {Boolean} data.ifCoupon - 是否可与优惠券叠加
 * @param {Number} data.astrictNumber - 限购数量
 * @param {Number} data.astrictAmount - 限购金额
 * @param {String} data.productType - 商品类型
 * @param {String} data.remark - 备注
 */
export function activityProductBatchAddApi(data) {
  return request({
    url: '/admin/merchant/activity/product/batch/add',
    method: 'post',
    data,
  });
}

/**
 * 获取活动参与商户列表
 * @param {Object} params - 请求参数
 * @param {Number} params.activityId - 活动ID
 * @param {Number} params.page - 页码
 * @param {Number} params.limit - 每页数量
 * @param {String} params.merchantName - 商户名称（可选）
 * @param {String} params.storeName - 门店名称（可选）
 */
export function getActivityMerchantListApi(params) {
  return request({
    url: '/admin/merchant/marketing/activity/merchant/list',
    method: 'get',
    params,
  });
}
