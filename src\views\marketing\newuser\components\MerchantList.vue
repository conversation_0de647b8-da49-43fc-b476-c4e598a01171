<template>
  <div class="merchants-content">
    <!-- 搜索表单和统计信息 -->
    <div class="merchant-header">
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="商户名称:">
            <el-input
              v-model="searchForm.merchantName"
              placeholder="请输入商户名称"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
          <el-form-item label="门店名称:">
            <el-input
              v-model="searchForm.storeName"
              placeholder="请输入门店名称"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <!-- 统计信息单独一行 -->
      <div class="merchant-stats">
        <span class="stats-text"
          >共<span class="stats-number">{{ pagination.total }}</span
          >家商户，<span class="stats-number">{{ merchantStoreCount }}</span
          >家门店</span
        >
      </div>
    </div>

    <!-- 商户表格 -->
    <el-table
      v-loading="loading"
      :data="merchantList"
      size="mini"
      class="table"
      highlight-current-row
      :header-cell-style="{ fontWeight: 'bold' }"
      :expand-row-keys="expandedRows"
      row-key="id"
    >
      <!-- 将type="expand"的列宽度设为0，隐藏展开箭头 -->
      <el-table-column type="expand" width="0">
        <template slot-scope="props">
          <div class="expanded-stores">
            <div class="store-header">
              <span class="store-column">门店ID</span>
              <span class="store-column">门店名称</span>
            </div>
            <div v-for="store in getStoresByMerchantId(props.row.id)" :key="store.id" class="store-row">
              <span class="store-column">{{ store.id }}</span>
              <span class="store-column">{{ store.name }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="商户ID" min-width="80" />
      <el-table-column prop="merchantName" label="商户名称" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作" min-width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="toggleExpand(scope.row)">
            {{ isRowExpanded(scope.row.id) ? '收起' : '展开' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="block">
      <el-pagination
        :page-sizes="[20, 40, 60, 80]"
        :page-size="pagination.limit"
        :current-page="pagination.page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MerchantList',
  props: {
    merchantList: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    pagination: {
      type: Object,
      required: true,
    },
    merchantStoreCount: {
      type: Number,
      default: 0,
    },
    allStores: {
      type: Array,
      required: true,
    },
    expandedRows: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      searchForm: {
        merchantName: '',
        storeName: '',
      },
    };
  },
  methods: {
    handleSearch() {
      this.$emit('search', this.searchForm);
    },
    toggleExpand(row) {
      this.$emit('toggle-expand', row);
    },
    isRowExpanded(rowId) {
      return this.expandedRows.includes(rowId);
    },
    getStoresByMerchantId(merchantId) {
      return this.allStores.filter((store) => store.merchantId === merchantId);
    },
    handleSizeChange(val) {
      this.$emit('size-change', val);
    },
    handlePageChange(val) {
      this.$emit('page-change', val);
    },
  },
};
</script>

<style scoped>
.merchants-content {
  .merchant-header {
    margin-bottom: 20px;

    .search-section {
      display: flex;
      align-self: flex-start;
    }

    .merchant-stats {
      margin-top: 10px;
      .stats-text {
        font-size: 14px;
        color: #0080ff;
      }
    }
  }

  .expanded-stores {
    background-color: #f5f5f5;
    padding: 10px;
    margin: 0;

    .store-header {
      display: flex;
      font-weight: bold;
      margin-bottom: 8px;

      .store-column {
        flex: 1;
        padding: 5px 10px;
        text-align: left;

        &:first-child {
          margin-right: 5px;
          max-width: 100px;
        }

        &:last-child {
          min-width: 200px;
        }
      }
    }

    .store-row {
      display: flex;
      margin-bottom: 5px;

      .store-column {
        flex: 1;
        padding: 5px 10px;
        text-align: left;

        &:first-child {
          margin-right: 5px;
          max-width: 100px;
        }

        &:last-child {
          min-width: 200px;
        }
      }
    }
  }

  .stats-text {
    color: #409eff;
  }

  .stats-number {
    color: #409eff;
  }

  .block {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
