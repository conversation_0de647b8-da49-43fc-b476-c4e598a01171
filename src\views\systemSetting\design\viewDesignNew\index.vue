<template>
  <div class="divBox">
    <el-card class="box-card" v-loading="loading" element-loading-text="页面加载中...">
      <!-- 页面类型 -->
      <div class="page-type">
        <el-radio-group v-model="pageType" @change="handlePageTypeChange">
          <el-radio label="1">首页</el-radio>
          <!-- <el-radio label="2">二级页</el-radio> -->
        </el-radio-group>
      </div>

      <!-- 基础信息 -->
      <div class="basic-info">
        <div class="section-header">基础信息</div>
        <div class="form-row">
          <label>标题</label>
          <el-input v-model="title" placeholder="0" style="width: 200px"></el-input>
        </div>
        <div class="form-row">
          <label>生效时间</label>
          <el-date-picker
            v-model="effectiveTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 400px"
          >
          </el-date-picker>
        </div>
      </div>

      <!-- 装修样式 -->
      <div class="decoration-style">
        <div class="section-header">装修样式</div>
        <div class="content-wrapper" style="height: 540px">
          <!-- 左侧选项 -->
          <div class="left-options" style="min-width: 200px">
            <div class="options-grid">
              <div class="option-item" :class="{ active: activeTab === 'ad' }" @click="switchTab('ad')">广告</div>
              <div class="option-item" :class="{ active: activeTab === 'spacing' }" @click="switchTab('spacing')">
                间距
              </div>
              <div class="option-item" :class="{ active: activeTab === 'quickIcon' }" @click="switchTab('quickIcon')">
                快捷图标
              </div>
              <!-- <div class="option-item" :class="{active: activeTab === 'merchantRecommend'}" @click="switchTab('merchantRecommend')">商户推荐</div>
              <div class="option-item" :class="{active: activeTab === 'coupon'}" @click="switchTab('coupon')">优惠券</div> -->
              <div class="option-item" :class="{ active: activeTab === 'product' }" @click="switchTab('product')">
                商品
              </div>
            </div>
          </div>

          <!-- 中间预览 -->
          <DesignPreview
            :activeTab="activeTab"
            :adsImg="adsImg"
            :adsHeight="adsHeight"
            :spacingColor="spacingColor"
            :spacingHeight="spacingHeight"
            :functionIconNum="functionIconNum"
            :functionIconRow="functionIconRow"
            :functionIcons="functionIcons"
            :isLocalDebug="isLocalDebug"
            :iconTitle="iconTitle"
            :iconSubtitle="iconSubtitle"
            :selectedMerchantList="selectedMerchantList"
            :selectedCouponList="selectedCouponList"
            :selectedProductList="selectedProductList"
            @icon-drag-sort="handleIconDragSort"
            @edit-icon="handleEditIcon"
            @delete-icon="handleDeleteIcon"
            @add-icon="handleAddIcon"
          />

          <!-- 右侧配置 -->
          <div class="config-area" style="height: 540px; width: 700px !important; flex: none !important">
            <!-- 广告配置 -->
            <div v-if="activeTab === 'ad'">
              <div class="config-header">广告</div>

              <div class="config-item inline-item">
                <label style="min-width: 100px">可加用户：</label>
                <el-radio-group v-model="adsRange" class="inline-options">
                  <el-radio label="1">全部</el-radio>
                  <el-radio label="2">新人</el-radio>
                </el-radio-group>
              </div>

              <div class="config-item inline-item">
                <label style="min-width: 100px">图片展示高度：</label>
                <el-radio-group
                  class="inline-options"
                  v-model="adsHeightLable"
                  style="margin-right: -20px"
                  @change="handleAdsHeightChange"
                >
                  <el-radio label="592">592</el-radio>
                  <el-radio label="custom">自定义</el-radio>
                </el-radio-group>
                <el-input
                  v-if="adsHeightLable === 'custom'"
                  v-model="adsHeight"
                  placeholder="请输入高度"
                  style="width: 120px"
                  @focus="adsHeightLable = 'custom'"
                  type="number"
                >
                </el-input>
              </div>

              <div class="config-item inline-item">
                <label>添加图片：</label>
                <div style="display: flex; align-items: center; gap: 15px">
                  <div class="upload-btn" @click="selectImage" v-if="!adsImg">
                    <i class="el-icon-plus"></i>
                  </div>
                  <div class="uploaded-image" v-if="adsImg" style="position: relative">
                    <img
                      :src="adsImg"
                      alt="广告图片"
                      style="width: 80px; height: 80px; object-fit: cover; border-radius: 4px"
                    />
                    <div
                      class="image-actions"
                      style="
                        position: absolute;
                        top: 0;
                        right: 0;
                        background: rgba(0, 0, 0, 0.5);
                        border-radius: 0 4px 0 4px;
                      "
                    >
                      <i
                        class="el-icon-edit"
                        @click="selectImage"
                        style="color: white; padding: 4px; cursor: pointer"
                      ></i>
                      <i
                        class="el-icon-delete"
                        @click="removeImage"
                        style="color: white; padding: 4px; cursor: pointer"
                      ></i>
                    </div>
                  </div>
                  <div class="upload-tip" style="margin-bottom: 0">
                    图片建议尺寸不超过750px，格式：jpg、png，大小不超过500kb
                  </div>
                </div>
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/jpg,image/jpeg,image/png"
                  style="display: none"
                  @change="handleFileSelect"
                />
              </div>

              <div class="config-item inline-item">
                <label>添加链接：</label>
                <el-select v-model="selectedLink" placeholder="请选择" style="width: 200px">
                  <el-option label="新人活动" value="/activity/newuser"></el-option>
                  <!-- <el-option label="充值活动" value="/activity/recharge"></el-option> -->
                  <el-option label="折扣活动" value="/activity/discount"></el-option>
                  <el-option label="秒杀活动" value="/activity/seckill"></el-option>
                </el-select>

                <el-button type="primary" size="small" @click="openActivityDialog" style="margin-left: 20px"
                  >选择活动</el-button
                >
              </div>

              <div class="config-item inline-item" style="margin-left: 85px">
                <span v-if="selectedActivityInfo.id" class="selected-info" style="margin-left: 10px">
                  已选择：{{ selectedActivityInfo.name }}
                </span>
                <span v-else class="selected-info" style="margin-left: 10px; color: #999"> 未选择活动 </span>
              </div>
            </div>

            <!-- 间距配置 -->
            <div v-if="activeTab === 'spacing'">
              <div class="config-header">间距</div>

              <div class="config-item inline-item">
                <label style="min-width: 100px">间距颜色：</label>
                <el-radio-group v-model="spacingColor" class="inline-options">
                  <el-radio label="white">白色</el-radio>
                  <el-radio label="gray">灰色</el-radio>
                </el-radio-group>
              </div>

              <div class="config-item inline-item">
                <label style="min-width: 100px">间距高度：</label>
                <el-radio-group class="inline-options" v-model="spacingHeightType" style="margin-right: -20px">
                  <el-radio label="20">20像素</el-radio>
                  <el-radio label="custom">自定义</el-radio>
                </el-radio-group>
                <el-input
                  v-if="spacingHeightType === 'custom'"
                  v-model="customSpacingHeight"
                  placeholder="请输入高度"
                  style="width: 120px"
                  type="number"
                >
                </el-input>
              </div>
            </div>

            <!-- 快捷图标配置 -->
            <div v-if="activeTab === 'quickIcon'">
              <div class="config-header">快捷图标</div>

              <div class="config-item inline-item">
                <label style="min-width: 120px">一行展示图标数量：</label>
                <el-radio-group v-model="functionIconNum" class="inline-options">
                  <el-radio :label="3">三图</el-radio>
                  <el-radio :label="4">四图</el-radio>
                  <el-radio :label="5">五图</el-radio>
                </el-radio-group>
              </div>

              <div class="config-item inline-item">
                <label style="min-width: 120px">行数：</label>
                <el-radio-group v-model="functionIconRow" class="inline-options">
                  <el-radio label="none">无</el-radio>
                  <el-radio :label="1">一行</el-radio>
                  <el-radio :label="2">两行</el-radio>
                </el-radio-group>
              </div>

              <div class="config-item">
                <div class="add-image-layout">
                  <label class="add-image-label">添加图片：</label>
                  <div class="quick-icon-add-wrapper">
                    <div class="upload-area">
                      <!-- 图标上传区域 -->
                      <div class="upload-box" @click="selectIconImage" v-if="!iconImg">
                        <i class="el-icon-plus"></i>
                        <div>上传图标</div>
                      </div>
                      <div class="uploaded-image" v-if="iconImg" style="position: relative">
                        <img
                          :src="iconImg"
                          alt="图标图片"
                          style="width: 100px; height: 100px; object-fit: cover; border-radius: 4px"
                        />
                        <div
                          class="image-actions"
                          style="
                            position: absolute;
                            top: 0;
                            right: 0;
                            background: rgba(0, 0, 0, 0.5);
                            border-radius: 0 4px 0 4px;
                          "
                        >
                          <i
                            class="el-icon-edit"
                            @click="selectIconImage"
                            style="color: white; padding: 4px; cursor: pointer"
                            title="编辑图片"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click="removeIconImage"
                            style="color: white; padding: 4px; cursor: pointer"
                            title="删除图片"
                          ></i>
                        </div>
                      </div>

                      <!-- 文件输入框 -->
                      <input
                        ref="iconFileInput"
                        type="file"
                        accept="image/*"
                        style="display: none"
                        @change="handleIconFileChange"
                      />

                      <!-- 添加按钮 - 只在非编辑模式下显示 -->
                      <el-button
                        v-if="!editingIconId"
                        type="primary"
                        size="small"
                        class="add-btn-below"
                        @click="addIconToPreview"
                        :disabled="!iconTitle.trim() || !iconImg"
                      >
                        添加
                      </el-button>
                    </div>
                    <div class="input-area">
                      <el-input v-model="iconTitle" placeholder="输入图标名称" style="margin-bottom: 12px"></el-input>
                      <el-input
                        v-model="iconSubtitle"
                        placeholder="输入图标副名称"
                        style="margin-bottom: 12px"
                      ></el-input>

                      <!-- 链接类型选择 -->
                      <div style="margin-bottom: 12px">
                        <label style="display: block; margin-bottom: 8px; font-size: 14px">链接类型：</label>
                        <el-radio-group v-model="iconLinkType" @change="handleIconLinkTypeChange">
                          <el-radio label="normal">常规</el-radio>
                          <el-radio label="activity">活动</el-radio>
                        </el-radio-group>
                      </div>

                      <!-- 常规链接输入 -->
                      <div v-if="iconLinkType === 'normal'">
                        <el-input v-model="iconLink" placeholder="链接"></el-input>
                      </div>

                      <!-- 活动链接选择 -->
                      <div v-if="iconLinkType === 'activity'">
                        <div style="display: flex; align-items: center; gap: 10px">
                          <el-select
                            v-model="iconSelectedActivityType"
                            placeholder="请选择活动类型"
                            style="width: 150px"
                            @change="handleIconActivityTypeChange"
                          >
                            <el-option label="折扣活动" value="newproduct"></el-option>
                            <el-option label="新人活动" value="newuser"></el-option>
                            <el-option label="秒杀活动" value="seckill"></el-option>
                          </el-select>
                          <el-button type="primary" size="small" @click="openIconActivityDialog">选择活动</el-button>
                        </div>
                        <div v-if="iconSelectedActivityInfo.id" style="margin-top: 8px; color: #666; font-size: 12px">
                          已选择：{{ iconSelectedActivityInfo.name }}
                        </div>
                        <div v-else style="margin-top: 8px; color: #999; font-size: 12px">未选择活动</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 商户推荐配置 -->
            <div v-if="activeTab === 'merchantRecommend'">
              <div class="config-header">商户推荐</div>

              <!-- 选择商户操作区域 -->
              <div class="merchant-select-header">
                <span>选择商户：</span>
                <el-button type="primary" size="small" @click="addMerchant">保存商户配置</el-button>
                <el-button type="text" size="small" class="suggest-btn">建议最多选择3家</el-button>
              </div>

              <div class="merchant-config">
                <!-- 搜索区域 -->
                <div class="search-section">
                  <el-input
                    v-model="merchantSearchKeyword"
                    placeholder="请输入商户名称"
                    @keyup.enter.native="searchMerchants"
                    style="width: 200px; margin-right: 10px"
                  >
                  </el-input>
                  <el-button type="primary" @click="searchMerchants">搜索</el-button>
                </div>

                <!-- 商户列表 -->
                <div class="merchant-list-section" style="margin-top: 20px" v-loading="merchantLoading">
                  <el-checkbox
                    v-for="merchant in merchantList"
                    :key="merchant.id"
                    v-model="merchant.checked"
                    @change="handleMerchantSelect(merchant)"
                    style="display: block; margin-bottom: 10px"
                  >
                    {{ merchant.name || merchant.merchantName }}
                  </el-checkbox>

                  <!-- 分页 -->
                  <!-- <el-pagination
                    v-if="merchantPagination.total > 0"
                    @size-change="handleMerchantSizeChange"
                    @current-change="handleMerchantCurrentChange"
                    :current-page="merchantPagination.currentPage"
                    :page-sizes="[10, 20, 50]"
                    :page-size="merchantPagination.pageSize"
                    :total="merchantPagination.total"
                    layout="total, sizes, prev, pager, next"
                    style="margin-top: 20px;">
                  </el-pagination> -->
                </div>
              </div>
            </div>
            <!-- 优惠券配置 -->
            <div v-if="activeTab === 'coupon'">
              <div class="config-header">优惠券</div>

              <div class="coupon-config">
                <div class="coupon-select-section">
                  <div class="select-header">
                    <span>选择优惠券：</span>
                    <el-button type="primary" size="small" @click="openCouponDialog">选择优惠券</el-button>
                    <span v-if="selectedCouponList.length > 0" class="selected-info">
                      已选择：{{ selectedCouponList.length }}张优惠券
                      <el-tooltip effect="dark" placement="top">
                        <div slot="content">
                          <div v-for="coupon in selectedCouponList" :key="coupon.id">
                            {{ coupon.name }}
                          </div>
                        </div>
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </span>
                    <span v-else class="selected-info">未选择优惠券</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 商品配置 -->
            <div v-if="activeTab === 'product'">
              <div class="config-header">商品</div>

              <div class="product-config">
                <div class="product-select-section">
                  <div class="select-header">
                    <span>选择商品：</span>
                    <el-button type="primary" size="small" @click="openProductDialog">选择商品</el-button>
                    <span v-if="selectedProductList.length > 0" class="selected-info">
                      已选择：{{ selectedProductList.length }}个商品
                      <el-tooltip effect="dark" placement="top">
                        <div slot="content">
                          <div v-for="product in selectedProductList" :key="product.id">
                            {{ product.name }}
                          </div>
                        </div>
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </span>
                    <span v-else class="selected-info">未选择商品</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="footer">
        <el-button type="primary" @click="saveConfig" :loading="loading">保存</el-button>
      </div>
    </el-card>

    <!-- 图标编辑弹窗 -->
    <el-dialog title="编辑图标" :visible.sync="showIconEditDialog" width="500px">
      <el-form :model="iconForm" label-width="80px">
        <el-form-item label="图标名称">
          <el-input v-model="iconForm.name" placeholder="请输入图标名称"></el-input>
        </el-form-item>
        <el-form-item label="副名称">
          <el-input v-model="iconForm.secondName" placeholder="请输入副名称"></el-input>
        </el-form-item>
        <el-form-item label="链接地址">
          <el-input v-model="iconForm.linkUrl" placeholder="请输入链接地址"></el-input>
        </el-form-item>
        <el-form-item label="说明">
          <el-input v-model="iconForm.illustrate" placeholder="请输入说明"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="showIconEditDialog = false">取消</el-button> -->
        <el-button type="primary" @click="saveIconEdit">保存</el-button>
      </span>
    </el-dialog>

    <!-- 优惠券选择弹窗 -->
    <el-dialog title="选择优惠券" :visible.sync="showCouponDialog" width="1100px" class="coupon-dialog">
      <div class="dialog-content" style="max-height: 600px; overflow-y: auto">
        <el-tabs v-model="activeCouponTab">
          <!-- 已选优惠券 -->
          <el-tab-pane label="已选优惠券" name="selected">
            <div class="coupon-search-section">
              <el-input
                v-model="selectedCouponSearch.name"
                placeholder="请输入优惠券名称"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchSelectedCoupons"
              >
              </el-input>
              <el-input
                v-model="selectedCouponSearch.code"
                placeholder="请输入优惠券编号"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchSelectedCoupons"
              >
              </el-input>
              <el-select
                v-model="selectedCouponSearch.status"
                placeholder="选择优惠券状态"
                style="width: 150px; margin-right: 10px"
                @keyup.enter.native="searchSelectedCoupons"
                @change="searchSelectedCoupons"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="进行中" value="1"></el-option>
                <el-option label="已结束" value="0"></el-option>
              </el-select>
              <el-select
                v-model="selectedCouponSearch.type"
                placeholder="优惠券类型"
                style="width: 150px; margin-right: 10px"
                @keyup.enter.native="searchSelectedCoupons"
                @change="searchSelectedCoupons"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="平台券" value="平台券"></el-option>
                <el-option label="商家券" value="商家券"></el-option>
              </el-select>
              <el-button type="primary" @click="searchSelectedCoupons">搜索</el-button>
            </div>

            <el-table
              :data="filteredSelectedCouponList"
              v-loading="selectedCouponLoading"
              style="margin-top: 10px"
              @selection-change="handleSelectedCouponSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="id" label="ID" width="80"></el-table-column>
              <el-table-column prop="name" label="优惠券名称" width="150"></el-table-column>
              <el-table-column prop="categoryName" label="类别" width="100"></el-table-column>
              <el-table-column prop="moneyStr" label="面值" width="100"></el-table-column>
              <el-table-column prop="receiveTimeStr" label="领取时间" width="120"></el-table-column>
              <el-table-column prop="useTimeStr" label="使用时间" width="150"></el-table-column>
              <el-table-column prop="total" label="发放数量" width="100"></el-table-column>
              <el-table-column prop="receiveTypeName" label="领取方式" width="120"></el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="removeCoupon(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- <div class="coupon-footer" style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center;"> -->
            <!-- <el-button 
              type="danger" 
              size="small" 
              :disabled="selectedCouponIds.length === 0"
              @click="batchRemoveCoupons">
              删除选中优惠券
            </el-button> -->
            <!-- <el-pagination
              @size-change="handleSelectedCouponSizeChange"
              @current-change="handleSelectedCouponCurrentChange"
              :current-page="selectedCouponPagination.currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="selectedCouponPagination.pageSize"
              :total="selectedCouponPagination.total"
              layout="total, sizes, prev, pager, next, jumper">
            </el-pagination> -->
            <!-- </div> -->
          </el-tab-pane>

          <!-- 未选优惠券 -->
          <el-tab-pane label="未选优惠券" name="unselected">
            <div class="coupon-search-section">
              <el-input
                v-model="unselectedCouponSearch.name"
                placeholder="请输入优惠券名称"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchUnselectedCoupons"
              >
              </el-input>
              <el-input
                v-model="unselectedCouponSearch.code"
                placeholder="请输入优惠券编号"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchUnselectedCoupons"
              >
              </el-input>
              <el-select
                v-model="unselectedCouponSearch.status"
                placeholder="选择优惠券状态"
                style="width: 150px; margin-right: 10px"
                @keyup.enter.native="searchUnselectedCoupons"
                @change="searchUnselectedCoupons"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="进行中" value="1"></el-option>
                <el-option label="已结束" value="0"></el-option>
              </el-select>
              <el-select
                v-model="unselectedCouponSearch.type"
                placeholder="优惠券类型"
                style="width: 150px; margin-right: 10px"
                @keyup.enter.native="searchUnselectedCoupons"
                @change="searchUnselectedCoupons"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="平台券" value="平台券"></el-option>
                <el-option label="商家券" value="商家券"></el-option>
              </el-select>
              <el-button type="primary" @click="searchUnselectedCoupons">搜索</el-button>
            </div>

            <el-table
              :data="filteredUnselectedCouponList"
              v-loading="unselectedCouponLoading"
              style="margin-top: 20px"
              @selection-change="handleUnselectedCouponSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="id" label="ID" width="80"></el-table-column>
              <el-table-column prop="name" label="优惠券名称" width="150"></el-table-column>
              <el-table-column prop="categoryName" label="类别" width="100"></el-table-column>
              <el-table-column prop="moneyStr" label="面值" width="100"></el-table-column>
              <el-table-column prop="receiveTimeStr" label="领取时间" width="120"></el-table-column>
              <el-table-column prop="useTimeStr" label="使用时间" width="150"></el-table-column>
              <el-table-column prop="total" label="发放数量" width="100"></el-table-column>
              <el-table-column prop="receiveTypeName" label="领取方式" width="120"></el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="addCoupon(scope.row)">添加</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- <div class="coupon-footer" style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center;"> -->
            <!-- <el-button 
              type="primary" 
              size="small" 
              :disabled="unselectedCouponIds.length === 0"
              @click="batchAddCoupons">
              批量添加选中优惠券
            </el-button> -->
            <!-- <el-pagination
              @size-change="handleUnselectedCouponSizeChange"
              @current-change="handleUnselectedCouponCurrentChange"
              :current-page="unselectedCouponPagination.currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="unselectedCouponPagination.pageSize"
              :total="unselectedCouponPagination.total"
              layout="total, sizes, prev, pager, next, jumper">
            </el-pagination> -->
            <!-- </div> -->
          </el-tab-pane>
        </el-tabs>
      </div>

      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="showCouponDialog = false">取消</el-button> -->
        <el-button type="primary" @click="confirmCouponSelect">提交</el-button>
      </span>
    </el-dialog>

    <!-- 活动选择弹窗 -->
    <el-dialog title="选择活动" :visible.sync="showActivityDialog" width="1000px">
      <el-tabs v-model="activeActivityTab">
        <!-- 已选活动 -->
        <el-tab-pane label="已选活动" name="selected">
          <div class="activity-search-section">
            <el-input
              v-model="selectedActivitySearch.name"
              placeholder="请输入活动名称"
              @keyup.enter.native="searchSelectedActivities"
              style="width: 200px; margin-right: 10px"
            >
            </el-input>
            <el-input
              v-model="selectedActivitySearch.id"
              placeholder="请输入活动ID"
              @keyup.enter.native="searchSelectedActivities"
              style="width: 200px; margin-right: 10px"
            >
            </el-input>
            <el-button type="primary" @click="searchSelectedActivities">搜索</el-button>
          </div>

          <el-table :data="filteredSelectedActivities" v-loading="selectedActivityLoading" style="margin-top: 20px">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="活动ID" width="80"></el-table-column>
            <el-table-column prop="name" label="活动名称" width="200"></el-table-column>
            <el-table-column prop="type" label="活动类型" width="120">
              <template slot-scope="scope">
                {{ getActivityTypeText(scope.row.type, scope.row.activityType) }}
              </template>
            </el-table-column>
            <el-table-column prop="isOpen" label="活动状态" width="100">
              <template slot-scope="scope">
                {{ scope.row.isOpen === '1' ? '进行中' : '已结束' }}
              </template>
            </el-table-column>
            <el-table-column prop="beginTime" label="开始时间" width="150"></el-table-column>
            <el-table-column prop="overTime" label="结束时间" width="150"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="removeActivity(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- <div class="activity-footer" style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center;"> -->
          <!-- <el-button type="danger" size="small">删除选中活动</el-button> -->
          <!-- <el-pagination
              @size-change="handleSelectedActivitySizeChange"
              @current-change="handleSelectedActivityCurrentChange"
              :current-page="selectedActivityPagination.currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="selectedActivityPagination.pageSize"
              :total="selectedActivityPagination.total"
              layout="total, sizes, prev, pager, next, jumper">
            </el-pagination> -->
          <!-- </div> -->
        </el-tab-pane>

        <!-- 未选活动 -->
        <el-tab-pane label="未选活动" name="unselected">
          <div class="activity-search-section">
            <el-input
              v-model="unselectedActivitySearch.name"
              placeholder="请输入活动名称"
              @keyup.enter.native="searchUnselectedActivities"
              style="width: 200px; margin-right: 10px"
            >
            </el-input>
            <el-input
              v-model="unselectedActivitySearch.id"
              placeholder="请输入活动ID"
              @keyup.enter.native="searchUnselectedActivities"
              style="width: 200px; margin-right: 10px"
            >
            </el-input>
            <el-button type="primary" @click="searchUnselectedActivities">搜索</el-button>
          </div>

          <el-table :data="filteredUnselectedActivities" v-loading="unselectedActivityLoading" style="margin-top: 20px">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="活动ID" width="80"></el-table-column>
            <el-table-column prop="name" label="活动名称" width="200"></el-table-column>
            <el-table-column prop="type" label="活动类型" width="120">
              <template slot-scope="scope">
                {{ getActivityTypeText(scope.row.type, scope.row.activityType) }}
              </template>
            </el-table-column>
            <el-table-column prop="isOpen" label="活动状态" width="100">
              <template slot-scope="scope">
                {{ scope.row.isOpen === '1' ? '进行中' : '已结束' }}
              </template>
            </el-table-column>
            <el-table-column prop="beginTime" label="开始时间" width="150"></el-table-column>
            <el-table-column prop="overTime" label="结束时间" width="150"></el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="addActivity(scope.row)">添加</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- <div class="activity-footer" style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center;"> -->
          <!-- <el-button type="primary" size="small">批量添加选中活动</el-button> -->
          <!-- <el-pagination
              @size-change="handleUnselectedActivitySizeChange"
              @current-change="handleUnselectedActivityCurrentChange"
              :current-page="unselectedActivityPagination.currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="unselectedActivityPagination.pageSize"
              :total="unselectedActivityPagination.total"
              layout="total, sizes, prev, pager, next, jumper">
            </el-pagination> -->
          <!-- </div> -->
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="showActivityDialog = false">取消</el-button> -->
        <el-button type="primary" @click="confirmActivitySelect">提交</el-button>
      </span>
    </el-dialog>

    <!-- 商品选择弹窗 -->
    <el-dialog title="选择商品" :visible.sync="showProductDialog" width="1100px" class="product-dialog">
      <div class="dialog-content" style="max-height: 600px; overflow-y: auto">
        <el-tabs v-model="activeProductTab">
          <!-- 已选商品 -->
          <el-tab-pane label="已选商品" name="selected">
            <div class="product-search-section">
              <el-input
                v-model="selectedProductSearch.name"
                placeholder="请输入商品名称"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchSelectedProducts"
              >
              </el-input>
              <el-input
                v-model="selectedProductSearch.code"
                placeholder="请输入商品货号"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchSelectedProducts"
              >
              </el-input>
              <el-button type="primary" @click="searchSelectedProducts">搜索</el-button>
            </div>

            <el-table
              :data="filteredSelectedProductList"
              v-loading="selectedProductLoading"
              style="margin-top: 10px"
              @selection-change="handleSelectedProductSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="id" label="商品ID" width="80"></el-table-column>
              <el-table-column label="商品图片" width="100">
                <template slot-scope="scope">
                  <img :src="scope.row.image" style="width: 50px; height: 50px; object-fit: cover" />
                </template>
              </el-table-column>
              <el-table-column prop="name" label="商品名称" width="200"></el-table-column>
              <el-table-column prop="cost" label="成本价" width="120"></el-table-column>
              <el-table-column prop="otPrice" label="原价" width="120"></el-table-column>
              <el-table-column prop="stock" label="库存" width="100"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  {{ scope.row.status === 1 ? '上架' : '下架' }}
                </template>
              </el-table-column>
              <el-table-column prop="merchantName" label="商户" width="150"></el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="removeProduct(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <div
              class="product-footer"
              style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center"
            >
              <el-button
                type="danger"
                size="small"
                :disabled="selectedProductIds.length === 0"
                @click="batchRemoveProducts"
              >
                删除选中商品
              </el-button>
            </div>
          </el-tab-pane>

          <!-- 未选商品 -->
          <el-tab-pane label="未选商品" name="unselected">
            <div class="product-search-section">
              <el-input
                v-model="unselectedProductSearch.name"
                placeholder="请输入商品名称"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchUnselectedProducts"
              >
              </el-input>
              <el-input
                v-model="unselectedProductSearch.code"
                placeholder="请输入商品货号"
                style="width: 200px; margin-right: 10px"
                @keyup.enter.native="searchUnselectedProducts"
              >
              </el-input>
              <el-button type="primary" @click="searchUnselectedProducts">搜索</el-button>
            </div>

            <el-table
              :data="filteredUnselectedProductList"
              v-loading="unselectedProductLoading"
              style="margin-top: 20px"
              @selection-change="handleUnselectedProductSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="id" label="商品ID" width="80"></el-table-column>
              <el-table-column label="商品图片" width="100">
                <template slot-scope="scope">
                  <img :src="scope.row.image" style="width: 50px; height: 50px; object-fit: cover" />
                </template>
              </el-table-column>
              <el-table-column prop="name" label="商品名称" width="200"></el-table-column>
              <el-table-column prop="cost" label="成本价" width="120"></el-table-column>
              <el-table-column prop="otPrice" label="原价" width="120"></el-table-column>
              <el-table-column prop="stock" label="库存" width="100"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  {{ scope.row.status === 1 ? '上架' : '下架' }}
                </template>
              </el-table-column>
              <el-table-column prop="merchantName" label="商户" width="150"></el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="addProduct(scope.row)">添加</el-button>
                </template>
              </el-table-column>
            </el-table>

            <div
              class="product-footer"
              style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center"
            >
              <el-button
                type="primary"
                size="small"
                :disabled="unselectedProductIds.length === 0"
                @click="batchAddProducts"
              >
                批量添加选中商品
              </el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="showProductDialog = false">取消</el-button> -->
        <el-button type="primary" @click="closeProductDialog">提交</el-button>
      </span>
    </el-dialog>

    <!-- 快捷图标活动选择弹窗 -->
    <el-dialog title="选择活动" :visible.sync="showIconActivityDialog" width="800px">
      <el-table :data="iconActivityList" style="width: 100%">
        <el-table-column prop="id" label="活动ID" width="80"></el-table-column>
        <el-table-column prop="name" label="活动名称" width="200"></el-table-column>
        <el-table-column prop="isOpen" label="状态" width="100">
          <template slot-scope="scope">
            {{ scope.row.isOpen === '1' ? '进行中' : '已结束' }}
          </template>
        </el-table-column>
        <el-table-column prop="beginTime" label="开始时间" width="150"></el-table-column>
        <el-table-column prop="overTime" label="结束时间" width="150"></el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="confirmIconActivitySelect(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showIconActivityDialog = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMerchantConfigApi,
  updatePlatformConfigApi,
  uploadAdsImageApi,
  getFitmentCouponListApi,
  updateFitmentCouponApi,
  getFitmentMerchantListApi,
  updateFitmentMerchantApi,
  getFitmentFunctionListApi,
  updateFitmentFunctionApi,
  getCarouselListApi,
  updateCarouselApi,
} from '@/api/systemSetting';
import { couponSelectListApi, topupListApi } from '@/api/marketing';
import { discountListApi } from '@/api/discount';
import { merchantListApi } from '@/api/merchant';
import { productQueryListApi } from '@/api/product';
import { fitmentProductListApi, fitmentProductUpdateApi } from '@/api/systemSetting';
import DesignPreview from './desginPreView.vue';

export default {
  name: 'ViewDesignNew',
  components: {
    DesignPreview,
  },
  data() {
    return {
      isLocalDebug: false,
      isIconActivitySelection: false, // 标记是否为图标活动选择
      tempIconActivityInfo: {
        // 临时保存图标活动信息
        id: null,
        name: '',
        type: null,
      },
      // 基础信息
      pageType: '1',
      title: '',
      effectiveTimeRange: [],
      // 广告
      adsRange: '',
      adsHeightLable: '592',
      adsHeight: 1,
      adsImg: '',
      // 图标
      functionIconNum: 3,
      functionIconRow: 1,
      // 间距
      spacingColor: 'white',
      spacingHeightType: '20',
      uploading: false,
      activeTab: 'ad',
      selectedLink: '',
      customSpacingHeight: '',
      iconTitle: '',
      iconSubtitle: '',
      iconLink: '',
      iconPosition: 1, // 图标所在行
      // 商户推荐相关数据
      merchantSearchKeyword: '',
      merchantList: [],
      selectedMerchantList: [], // 已选商户列表（装修关系）
      // 商户分页数据
      merchantPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      merchantLoading: false,
      // 优惠券相关数据
      homeFitmentCouponId: null, // 已选优惠券ID
      allCouponList: [], // 源数据，不变动
      filteredSelectedCouponList: [], // 已选优惠券的过滤结果
      filteredUnselectedCouponList: [], // 未选优惠券的过滤结果
      showCouponDialog: false,
      // 活动相关数据
      originalSelectedActivityList: [], // 原始已选活动列表
      originalUnselectedActivityList: [], // 原始未选活动列表
      showActivityDialog: false,
      activeActivityTab: 'selected',
      selectedActivitySearch: {
        name: '',
        id: '',
      },
      unselectedActivitySearch: {
        name: '',
        id: '',
        type: '',
      },
      selectedActivityList: [],
      unselectedActivityList: [],
      selectedActivityLoading: false,
      unselectedActivityLoading: false,
      selectedActivityPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      unselectedActivityPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      // 商品相关数据
      allProductList: [], // 所有商品列表
      selectedProductList: [], // 已选商品列表
      filteredSelectedProductList: [], // 已选商品过滤结果
      filteredUnselectedProductList: [], // 未选商品过滤结果
      showProductDialog: false,
      activeProductTab: 'selected',
      selectedProductSearch: {
        name: '',
        code: '',
      },
      unselectedProductSearch: {
        name: '',
        code: '',
      },
      selectedProductLoading: false,
      unselectedProductLoading: false,
      selectedProductPagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      unselectedProductPagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      selectedProductSelection: [],
      unselectedProductSelection: [],
      selectedProductIds: [],
      unselectedProductIds: [],
      activeCouponTab: 'selected',
      selectedCouponSearch: {
        name: '',
        code: '',
        status: '',
        type: '',
      },
      unselectedCouponSearch: {
        name: '',
        code: '',
        status: '',
        type: '',
      },
      selectedCouponList: [],
      unselectedCouponList: [],
      selectedCouponLoading: false,
      unselectedCouponLoading: false,
      selectedCouponPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 1,
      },
      unselectedCouponPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 2,
      },
      configId: null, // 添加配置ID字段
      loading: false, // 添加加载状态
      selectedActivityInfo: {
        id: null,
        name: '',
        type: null,
      },
      selectedCouponSelection: [], // 已选优惠券表格选中项
      unselectedCouponSelection: [], // 未选优惠券表格选中项
      selectedCouponIds: [],
      unselectedCouponIds: [],
      selectedProductSelection: [], // 已选商品表格选中项
      unselectedProductSelection: [], // 未选商品表格选中项
      selectedProductIds: [],
      unselectedProductIds: [],
      // 快捷图标相关数据
      functionIcons: [], // 快捷图标列表
      editingIcon: null, // 正在编辑的图标
      showIconEditDialog: false, // 图标编辑弹窗
      iconForm: {
        id: null,
        iconUrl: '',
        linkUrl: '',
        name: '',
        secondName: '',
        illustrate: '',
        position: 1,
        sort: 1,
        type: 1,
      },
      iconImg: '', // 图标图片URL
      iconUploading: false, // 图标上传状态
      editingIconId: null, // 当前编辑的图标ID
      iconLinkType: 'normal', // 图标链接类型：normal-常规，activity-活动
      iconSelectedActivityType: '', // 图标选择的活动类型
      iconSelectedActivityInfo: {
        // 图标选择的活动信息
        id: null,
        name: '',
        type: null,
      },
      showIconActivityDialog: false, // 快捷图标活动选择弹窗
      iconActivityList: [], // 快捷图标可选活动列表
      // 广告活动相关数据
      adsSelectedActivityType: '', // 广告选择的活动类型
      adsSelectedActivityInfo: {
        // 广告选择的活动信息
        id: null,
        name: '',
        type: null,
      },
      showAdsActivityDialog: false, // 广告活动选择弹窗
      adsActivityList: [], // 广告可选活动列表
    };
  },
  computed: {
    /**
     * 计算间距高度
     * @returns {Number} 间距高度值
     */
    spacingHeight() {
      if (this.spacingHeightType === 'custom') {
        return this.customSpacingHeight || 20;
      }
      return parseInt(this.spacingHeightType);
    },
    /**
     * 计算预览图标数量
     * @returns {Array} 预览图标数组
     */
    previewIcons() {
      const totalIcons = this.functionIconRow === 'none' ? 0 : this.functionIconNum * (this.functionIconRow || 1);
      return Array.from({ length: totalIcons }, (_, index) => ({ id: index }));
    },
    /**
     * 实时过滤的已选活动列表
     */
    filteredSelectedActivities() {
      let list = [...(this.originalSelectedActivityList || [])];

      // 按活动名称搜索
      if (this.selectedActivitySearch.name) {
        list = list.filter(
          (item) => item.name && item.name.toLowerCase().includes(this.selectedActivitySearch.name.toLowerCase()),
        );
      }

      // 按活动ID搜索
      if (this.selectedActivitySearch.id) {
        list = list.filter((item) => item.id && item.id.toString().includes(this.selectedActivitySearch.id));
      }

      return list;
    },

    /**
     * 实时过滤的未选活动列表
     */
    filteredUnselectedActivities() {
      let list = [...(this.originalUnselectedActivityList || [])];

      // 按活动名称搜索
      if (this.unselectedActivitySearch.name) {
        list = list.filter(
          (item) => item.name && item.name.toLowerCase().includes(this.unselectedActivitySearch.name.toLowerCase()),
        );
      }

      // 按活动ID搜索
      if (this.unselectedActivitySearch.id) {
        list = list.filter((item) => item.id && item.id.toString().includes(this.unselectedActivitySearch.id));
      }

      // 关键：排除已选的活动
      const selectedIds = (this.originalSelectedActivityList || []).map((item) => item.id);
      list = list.filter((item) => !selectedIds.includes(item.id));

      return list;
    },
  },
  async mounted() {
    this.loading = true;
    try {
      // 先获取所有优惠券作为源数据
      // await this.getCouponsList()
      // 先获取所有商品列表
      await this.getProductList();
      // 再获取已选商品
      await this.getFitmentProductList();
      this.getMerchantConfig();
      // this.getMerchantList()
      // this.getFitmentMerchantList()
      await this.getFunctionIcons();
      await this.getAdsList();
    } catch (error) {
      console.error('初始化失败:', error);
    } finally {
      this.loading = false;
    }
  },
  methods: {
    /**
     * 选择图片
     */
    selectImage() {
      this.$refs.fileInput.click();
    },
    /**
     * 处理文件选择
     * @param {Event} event - 文件选择事件
     */
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        this.$message.error('只支持jpg、png格式的图片');
        return;
      }

      // 验证文件大小（500KB）
      if (file.size > 500 * 1024) {
        this.$message.error('图片大小不能超过500KB');
        return;
      }

      // 调用上传方法
      this.uploadImage(file);
    },

    /**
     * 上传图片
     * @param {File} file - 图片文件
     */
    async uploadImage(file) {
      this.uploading = true;
      try {
        const formData = new FormData();
        formData.append('multipart', file);
        formData.append('model', 'system');
        formData.append('pid', '0');

        const response = await uploadAdsImageApi(formData);

        if (response && response.url) {
          // 直接使用接口返回的完整URL
          this.adsImg = response.url;

          this.$message.success('图片上传成功');
        } else {
          this.$message.error('图片上传失败：未返回有效URL');
        }
      } catch (error) {
        console.error('图片上传失败:', error);
        this.$message.error('图片上传失败：' + (error.message || '未知错误'));
      } finally {
        this.uploading = false;
        const fileInput = this.$refs.fileInput;
        if (fileInput) {
          fileInput.value = '';
        }
      }
    },
    /**
     * 删除图片
     */
    removeImage() {
      this.adsImg = '';
      this.$message.success('图片已删除');
    },
    handleAdsHeightChange(value) {
      if (value === '592') {
        this.adsHeight = 592;
      }
    },
    async getCouponsList() {
      try {
        // 获取完整优惠券列表作为源数据
        const response = await couponSelectListApi({
          publisher: 1, // 只允许关联平台优惠券
          limit: -1,
        });
        this.allCouponList = response.list || [];

        // 初始化过滤列表
        this.updateFilteredCouponLists();
      } catch (error) {
        console.error('获取优惠券列表失败:', error);
        this.$message.error('获取优惠券列表失败');
      }
    },
    /**
     * 更新过滤后的优惠券列表
     */
    updateFilteredCouponLists() {
      // 基于已选优惠券的couponId来分离
      const selectedCouponIds = this.selectedCouponList.map((item) => item.couponId || item.id);

      // 应用搜索条件到已选优惠券
      this.filteredSelectedCouponList = this.selectedCouponList.filter((coupon) => {
        const search = this.selectedCouponSearch;
        return (
          (!search.name || coupon.name.includes(search.name)) &&
          (!search.code || (coupon.code && coupon.code.includes(search.code))) &&
          (!search.status || coupon.status == search.status) &&
          (!search.type || coupon.categoryName == search.type)
        );
      });

      // 应用搜索条件到未选优惠券
      this.filteredUnselectedCouponList = this.allCouponList.filter((coupon) => {
        // 首先排除已选的
        if (selectedCouponIds.includes(coupon.id)) {
          return false;
        }

        // 然后应用搜索条件
        const search = this.unselectedCouponSearch;
        return (
          (!search.name || coupon.name.includes(search.name)) &&
          (!search.code || (coupon.code && coupon.code.includes(search.code))) &&
          (!search.status || coupon.status == search.status) &&
          (!search.type || coupon.categoryName == search.type)
        );
      });

      // 更新分页信息
      this.selectedCouponPagination.total = this.filteredSelectedCouponList.length;
      this.unselectedCouponPagination.total = this.filteredUnselectedCouponList.length;

      // 重置到第一页
      this.selectedCouponPagination.currentPage = 1;
      this.unselectedCouponPagination.currentPage = 1;
    },

    /**
     * 打开优惠券选择弹窗
     */
    openCouponDialog() {
      this.showCouponDialog = true;
      // 重置搜索条件
      this.selectedCouponSearch = {
        name: '',
        code: '',
        status: '',
        type: '',
      };
      this.unselectedCouponSearch = {
        name: '',
        code: '',
        status: '',
        type: '',
      };
      // 更新过滤列表
      this.updateFilteredCouponLists();
    },

    /**
     * 添加优惠券（限制最多选择1个）
     * @param {Object} coupon - 优惠券对象
     */
    addCoupon(coupon) {
      // 检查是否已经选中该优惠券
      if (this.selectedCouponList.find((item) => item.id === coupon.id)) {
        this.$message.warning('该优惠券已经选中');
        return;
      }

      // 检查是否已达到最大选择数量
      if (this.selectedCouponList.length >= 1) {
        this.$message.warning('最多只能选择1张优惠券，请先删除已选择的优惠券');
        return;
      }

      // 添加到已选列表
      this.selectedCouponList.push(coupon);

      // 更新过滤列表
      this.updateFilteredCouponLists();

      this.$message.success('已添加优惠券：' + coupon.name);
    },
    /**
     * 删除优惠券
     * @param {Object} coupon - 优惠券对象
     */
    removeCoupon(coupon) {
      // 从已选列表中移除
      this.selectedCouponList = this.selectedCouponList.filter((item) => item.id !== coupon.id);

      // 清空homeFitmentCouponId
      this.homeFitmentCouponId = null;

      // 更新过滤列表
      this.updateFilteredCouponLists();
    },
    /**
     * 确认选择优惠券
     */
    confirmCouponSelect() {
      this.showCouponDialog = false;

      if (this.selectedCouponList.length > 0) {
        this.$message.success(`已选择 ${this.selectedCouponList.length} 张优惠券`);
      } else {
        this.$message.info('未选择任何优惠券');
      }
    },
    /**
     * 已选优惠券分页大小改变
     * @param {Number} val - 分页大小
     */
    handleSelectedCouponSizeChange(val) {
      this.selectedCouponPagination.pageSize = val;
    },
    /**
     * 已选优惠券当前页改变
     * @param {Number} val - 当前页
     */
    handleSelectedCouponCurrentChange(val) {
      this.selectedCouponPagination.currentPage = val;
    },
    /**
     * 未选优惠券分页大小改变
     * @param {Number} val - 分页大小
     */
    handleUnselectedCouponSizeChange(val) {
      this.unselectedCouponPagination.pageSize = val;
    },
    /**
     * 未选优惠券当前页改变
     * @param {Number} val - 当前页
     */
    handleUnselectedCouponCurrentChange(val) {
      this.unselectedCouponPagination.currentPage = val;
    },
    /**
     * 搜索已选优惠券
     */
    searchSelectedCoupons() {
      this.updateFilteredCouponLists();
    },
    /**
     * 搜索未选优惠券
     */
    searchUnselectedCoupons() {
      this.updateFilteredCouponLists();
    },
    /**
     * 已选优惠券选择改变
     * @param {Array} selection - 选中的优惠券列表
     */
    handleSelectedCouponSelectionChange(selection) {
      this.selectedCouponSelection = selection;
      this.selectedCouponIds = selection.map((item) => item.id);
    },
    /**
     * 未选优惠券选择改变
     * @param {Array} selection - 选中的优惠券列表
     */
    handleUnselectedCouponSelectionChange(selection) {
      this.unselectedCouponSelection = selection;
      this.unselectedCouponIds = selection.map((item) => item.id);
    },
    /**
     * 批量添加选中的优惠券（限制最多选择1个）
     */
    batchAddCoupons() {
      const toAdd = this.filteredUnselectedCouponList.filter((item) => this.unselectedCouponIds.includes(item.id));

      // 检查选择数量限制
      const totalAfterAdd = this.selectedCouponList.length + toAdd.length;
      if (totalAfterAdd > 1) {
        this.$message.warning('最多只能选择1张优惠券');
        return;
      }

      toAdd.forEach((coupon) => {
        if (!this.selectedCouponList.find((item) => item.id === coupon.id)) {
          this.selectedCouponList.push(coupon);
        }
      });

      this.unselectedCouponIds = [];

      // 更新过滤列表
      this.updateFilteredCouponLists();

      this.$message.success(`成功添加 ${toAdd.length} 张优惠券`);
    },
    /**
     * 批量删除选中的优惠券
     */
    batchRemoveCoupons() {
      const toRemove = this.filteredSelectedCouponList.filter((item) => this.selectedCouponIds.includes(item.id));

      this.selectedCouponList = this.selectedCouponList.filter((item) => !this.selectedCouponIds.includes(item.id));

      this.selectedCouponIds = [];

      // 更新过滤列表
      this.updateFilteredCouponLists();

      this.$message.success(`成功删除 ${toRemove.length} 张优惠券`);
    },
    /**
     * 打开活动选择对话框
     */
    openActivityDialog() {
      // 根据选择的链接类型确定活动类型
      let activityType = '1'; // 默认折扣活动

      switch (this.selectedLink) {
        case '/activity/newuser':
          activityType = '3'; // 新人
          this.adsSelectedActivityType = 'newuser';
          break;
        case '/activity/discount':
          activityType = '1'; // 折扣活动
          this.adsSelectedActivityType = 'newproduct';
          break;
        case '/activity/seckill':
          activityType = '2'; // 秒杀
          this.adsSelectedActivityType = 'seckill';
          break;
        default:
          activityType = '1';
          this.adsSelectedActivityType = 'newproduct';
      }

      this.unselectedActivitySearch.type = activityType;
      this.unselectedActivitySearch.name = '';
      this.unselectedActivitySearch.id = '';

      // 一次性获取所有活动数据
      this.getAllActivitiesList();
      this.showActivityDialog = true;
    },
    /**
     * 获取所有活动列表（一次性获取，用于本地过滤）
     */
    async getAllActivitiesList() {
      try {
        this.selectedActivityLoading = true;
        this.unselectedActivityLoading = true;

        let allActivities = [];
        const activityType = this.unselectedActivitySearch.type;

        // 获取营销活动
        const discountParams = {
          belongTo: 'merchant',
          type: parseInt(activityType),
          isOpen: 1,
          page: 1,
          limit: -1, // 获取所有数据
        };

        const response = await discountListApi(discountParams);
        if (response.list) {
          allActivities = response.list.map((item) => ({
            ...item,
            activityType: 'marketing',
            type: activityType,
          }));
        }

        // 存储原始活动列表
        this.originalUnselectedActivityList = allActivities;

        // 分离已选和未选活动
        if (this.selectedActivityInfo && this.selectedActivityInfo.id) {
          // 如果有已选活动，从所有活动中找到它
          this.originalSelectedActivityList = allActivities.filter((item) => item.id === this.selectedActivityInfo.id);
        } else {
          // 如果没有已选活动，已选列表为空
          this.originalSelectedActivityList = [];
        }

        // 更新过滤后的列表
        this.updateFilteredActivityLists();
      } catch (error) {
        console.error('获取活动列表失败:', error);
        this.$message.error('获取活动列表失败');
      } finally {
        this.selectedActivityLoading = false;
        this.unselectedActivityLoading = false;
      }
    },
    /**
     * 更新过滤后的活动列表
     */
    updateFilteredActivityLists() {
      // 过滤已选活动
      let filteredSelected = this.originalSelectedActivityList || [];
      const selectedSearch = this.selectedActivitySearch;

      if (selectedSearch.name) {
        filteredSelected = filteredSelected.filter(
          (activity) => activity.name && activity.name.toLowerCase().includes(selectedSearch.name.toLowerCase()),
        );
      }

      if (selectedSearch.id) {
        filteredSelected = filteredSelected.filter(
          (activity) => activity.id && activity.id.toString().includes(selectedSearch.id),
        );
      }

      // 过滤未选活动
      let filteredUnselected = this.originalUnselectedActivityList || [];
      const unselectedSearch = this.unselectedActivitySearch;

      if (unselectedSearch.name) {
        filteredUnselected = filteredUnselected.filter(
          (activity) => activity.name && activity.name.toLowerCase().includes(unselectedSearch.name.toLowerCase()),
        );
      }

      if (unselectedSearch.id) {
        filteredUnselected = filteredUnselected.filter(
          (activity) => activity.id && activity.id.toString().includes(unselectedSearch.id),
        );
      }

      // 排除已选的活动
      const selectedActivityIds = filteredSelected.map((item) => item.id);
      filteredUnselected = filteredUnselected.filter((activity) => !selectedActivityIds.includes(activity.id));

      // 更新显示列表
      this.selectedActivityList = filteredSelected;
      this.unselectedActivityList = filteredUnselected;

      // 更新分页信息
      this.selectedActivityPagination.total = filteredSelected.length;
      this.unselectedActivityPagination.total = filteredUnselected.length;
    },
    /**
     * 获取活动类型文本
     * @param {String} type - 活动类型
     * @param {String} activityType - 活动分类
     * @returns {String} 活动类型文本
     */
    getActivityTypeText(type, activityType) {
      if (activityType === 'topup') {
        return '充值活动';
      }
      const typeMap = {
        1: '折扣活动',
        2: '秒杀活动',
        3: '新人专享活动',
      };
      return typeMap[type] || '未知类型';
    },
    /**
     * 添加活动
     * @param {Object} activity - 活动对象
     */
    addActivity(activity) {
      // 如果已经有选中的活动，先移除
      if (this.originalSelectedActivityList.length > 0) {
        this.originalSelectedActivityList = [];
      }

      // 添加新选中的活动
      this.originalSelectedActivityList.push(activity);

      // 更新过滤后的列表
      this.updateFilteredActivityLists();
    },
    /**
     * 删除活动
     * @param {Object} activity - 活动对象
     */
    removeActivity(activity) {
      this.originalSelectedActivityList = this.originalSelectedActivityList.filter((item) => item.id !== activity.id);

      // 更新过滤后的列表
      this.updateFilteredActivityLists();
    },
    /**
     * 确认选择活动
     */
    confirmActivitySelect() {
      this.showActivityDialog = false;

      if (this.originalSelectedActivityList.length > 0) {
        const selectedActivity = this.originalSelectedActivityList[0];

        // 更新广告活动信息
        this.adsSelectedActivityInfo = {
          id: selectedActivity.id,
          name: selectedActivity.name,
          type: this.adsSelectedActivityType,
        };

        // 更新selectedActivityInfo（保持原有逻辑）
        this.selectedActivityInfo = {
          id: selectedActivity.id,
          name: selectedActivity.name,
          type: selectedActivity.type || selectedActivity.activityType,
        };

        // 根据活动类型设置正确的selectedLink值（匹配UI选择框的选项）
        const activityTypeMap = {
          newuser: '/activity/newuser',
          newproduct: '/activity/discount',
          seckill: '/activity/seckill',
        };

        this.selectedLink = activityTypeMap[this.adsSelectedActivityType] || '/activity/discount';

        this.$message.success(`已选择活动：${selectedActivity.name}`);
      } else {
        // 清空活动信息
        this.adsSelectedActivityInfo = {
          id: null,
          name: '',
          type: null,
        };

        this.selectedActivityInfo = {
          id: null,
          name: '',
          type: null,
        };

        this.selectedLink = '';
      }
    },
    /**
     * 搜索已选活动 - 简化实现，因为使用了computed属性
     */
    searchSelectedActivities() {
      // 由于使用computed属性，搜索会自动触发，这里可以为空或者添加其他逻辑
    },
    /**
     * 搜索未选活动 - 简化实现，因为使用了computed属性
     */
    searchUnselectedActivities() {
      // 由于使用computed属性，搜索会自动触发，这里可以为空或者添加其他逻辑
    },
    /**
     * 活动类型改变时重新获取数据
     */
    handleActivityTypeChange() {
      this.unselectedActivityPagination.currentPage = 1;
      this.getAllActivitiesList();
    },
    /**
     * 已选活动分页大小改变
     * @param {Number} val - 分页大小
     */
    handleSelectedActivitySizeChange(val) {
      this.selectedActivityPagination.pageSize = val;
    },
    /**
     * 已选活动当前页改变
     * @param {Number} val - 当前页
     */
    handleSelectedActivityCurrentChange(val) {
      this.selectedActivityPagination.currentPage = val;
    },
    /**
     * 未选活动分页大小改变
     * @param {Number} val - 分页大小
     */
    handleUnselectedActivitySizeChange(val) {
      this.unselectedActivityPagination.pageSize = val;
    },
    /**
     * 未选活动当前页改变
     * @param {Number} val - 当前页
     */
    handleUnselectedActivityCurrentChange(val) {
      this.unselectedActivityPagination.currentPage = val;
    },

    /**
     * 获取商品列表
     * @param {Object} params - 查询参数
     */
    async getProductList(params = {}) {
      try {
        this.unselectedProductLoading = true;
        const defaultParams = {
          limit: -1,
          name: this.unselectedProductSearch.name,
          status: this.unselectedProductSearch.status,
        };

        const response = await productQueryListApi({ ...defaultParams, ...params });

        this.allProductList = response.list || [];
        this.separateProducts();
      } catch (error) {
        console.error('获取商品列表失败:', error);
        this.$message.error('获取商品列表失败');
      } finally {
        this.unselectedProductLoading = false;
      }
    },

    /**
     * 分离已选和未选商品
     */
    separateProducts() {
      // 基于已选商品的id来计算未选商品列表
      const selectedProductIds = this.selectedProductList.map((item) => item.id);
      this.unselectedProductList = this.allProductList.filter((product) => !selectedProductIds.includes(product.id));

      // 更新过滤列表
      this.updateFilteredProductLists();
    },

    /**
     * 更新过滤后的商品列表
     */
    updateFilteredProductLists() {
      // 过滤已选商品
      let filteredSelected = this.selectedProductList;
      const selectedSearch = this.selectedProductSearch;

      if (selectedSearch.name) {
        filteredSelected = filteredSelected.filter(
          (product) => product.name && product.name.toLowerCase().includes(selectedSearch.name.toLowerCase()),
        );
      }

      if (selectedSearch.code) {
        filteredSelected = filteredSelected.filter(
          (product) => product.id && product.id.toString().includes(selectedSearch.code),
        );
      }

      // 过滤未选商品
      const selectedProductIds = this.selectedProductList.map((item) => item.id);
      let filteredUnselected = this.allProductList.filter((product) => !selectedProductIds.includes(product.id));

      const unselectedSearch = this.unselectedProductSearch;

      if (unselectedSearch.name) {
        filteredUnselected = filteredUnselected.filter(
          (product) => product.name && product.name.toLowerCase().includes(unselectedSearch.name.toLowerCase()),
        );
      }

      if (unselectedSearch.code) {
        filteredUnselected = filteredUnselected.filter(
          (product) => product.id && product.id.toString().includes(unselectedSearch.code),
        );
      }

      // 直接显示全部过滤结果，不进行分页处理
      this.filteredSelectedProductList = filteredSelected;
      this.filteredUnselectedProductList = filteredUnselected;
    },

    /**
     * 打开商品选择弹窗
     */
    async openProductDialog() {
      this.showProductDialog = true;

      // 重置搜索条件
      this.selectedProductSearch = { name: '', code: '' };
      this.unselectedProductSearch = { name: '', code: '' };

      // 如果还没有加载过商品列表，才获取
      if (!this.allProductList || this.allProductList.length === 0) {
        await this.getProductList();
      } else {
        // 直接分离商品并更新过滤列表
        this.separateProducts();
      }

      // 更新过滤列表
      this.updateFilteredProductLists();
    },
    /**
     * 添加商品
     * @param {Object} product - 商品对象
     */
    addProduct(product) {
      // 检查是否已经选中该商品
      if (this.selectedProductList.find((item) => item.id === product.id)) {
        this.$message.warning('该商品已经选中');
        return;
      }

      // 添加完整的商品对象到已选列表
      this.selectedProductList.push(product);

      // 更新过滤列表
      this.updateFilteredProductLists();
    },

    /**
     * 删除商品
     * @param {Object} product - 商品对象
     */
    removeProduct(product) {
      this.selectedProductList = this.selectedProductList.filter((item) => item.id !== product.id);

      // 更新过滤列表
      this.updateFilteredProductLists();
    },

    /**
     * 获取装修商品列表
     */
    async getFitmentProductList() {
      try {
        // 如果还没有加载所有商品列表，先加载
        if (!this.allProductList || this.allProductList.length === 0) {
          await this.getProductList();
        }

        const response = await fitmentProductListApi({ type: parseInt(this.pageType) });
        if (response && response.length > 0) {
          // 根据productId匹配完整的商品信息
          this.selectedProductList = [];
          response.forEach((fitmentProduct) => {
            const fullProduct = this.allProductList.find((product) => product.id === fitmentProduct.productId);
            if (fullProduct) {
              this.selectedProductList.push(fullProduct);
            }
          });
        } else {
          this.selectedProductList = [];
        }

        // 分离已选和未选商品
        this.separateProducts();
      } catch (error) {
        console.error('获取装修商品失败:', error);
      }
    },

    /**
     * 关闭商品选择弹窗
     */
    closeProductDialog() {
      this.showProductDialog = false;
      this.$message.success(`已选择 ${this.selectedProductList.length} 个商品`);
    },

    /**
     * 搜索已选商品
     */
    searchSelectedProducts() {
      this.selectedProductPagination.currentPage = 1;
      this.updateFilteredProductLists();
    },

    /**
     * 搜索未选商品
     */
    searchUnselectedProducts() {
      this.unselectedProductPagination.currentPage = 1;
      this.updateFilteredProductLists();
    },

    /**
     * 已选商品分页大小改变
     * @param {Number} val - 分页大小
     */
    handleSelectedProductSizeChange(val) {
      this.selectedProductPagination.pageSize = val;
      this.selectedProductPagination.currentPage = 1;
      this.updateFilteredProductLists();
    },

    /**
     * 已选商品当前页改变
     * @param {Number} val - 当前页
     */
    handleSelectedProductCurrentChange(val) {
      this.selectedProductPagination.currentPage = val;
      this.updateFilteredProductLists();
    },

    /**
     * 未选商品分页大小改变
     * @param {Number} val - 分页大小
     */
    handleUnselectedProductSizeChange(val) {
      this.unselectedProductPagination.pageSize = val;
      this.unselectedProductPagination.currentPage = 1;
      this.updateFilteredProductLists();
    },

    /**
     * 未选商品当前页改变
     * @param {Number} val - 当前页
     */
    handleUnselectedProductCurrentChange(val) {
      this.unselectedProductPagination.currentPage = val;
      this.updateFilteredProductLists();
    },

    /**
     * 已选商品选择改变
     * @param {Array} selection - 选中的商品列表
     */
    handleSelectedProductSelectionChange(selection) {
      this.selectedProductSelection = selection;
      this.selectedProductIds = selection.map((item) => item.id);
    },

    /**
     * 未选商品选择改变
     * @param {Array} selection - 选中的商品列表
     */
    handleUnselectedProductSelectionChange(selection) {
      this.unselectedProductSelection = selection;
      this.unselectedProductIds = selection.map((item) => item.id);
    },

    /**
     * 批量添加选中的商品
     */
    batchAddProducts() {
      const toAdd = this.filteredUnselectedProductList.filter((item) => this.unselectedProductIds.includes(item.id));

      toAdd.forEach((product) => {
        if (!this.selectedProductList.find((item) => item.id === product.id)) {
          this.selectedProductList.push(product);
        }
      });

      this.unselectedProductIds = [];
      this.updateFilteredProductLists();

      this.$message.success(`成功添加 ${toAdd.length} 个商品`);
    },

    /**
     * 批量删除选中的商品
     */
    batchRemoveProducts() {
      this.selectedProductList = this.selectedProductList.filter((item) => !this.selectedProductIds.includes(item.id));

      this.selectedProductIds = [];
      this.updateFilteredProductLists();

      this.$message.success(`成功删除选中商品`);
    },
    /**
     * 获取平台配置
     * @param {String} type - 页面类型，可选参数
     */
    async getMerchantConfig(type) {
      try {
        this.loading = true;
        // 使用当前选中的页面类型或传入的类型
        const configType = type || this.pageType;
        const response = await getMerchantConfigApi({ type: configType });
        const config = response;

        // 标题
        this.title = config.title || '';
        // 生效时间
        if (config.effectiveTimeStart && config.effectiveTimeEnd) {
          this.effectiveTimeRange = [config.effectiveTimeStart, config.effectiveTimeEnd];
        } else {
          this.effectiveTimeRange = [];
        }

        // 填充广告数据
        // 可加用户
        if (config.adsRange) {
          this.adsRange = config.adsRange === 1 ? '1' : '2';
        } else {
          this.adsRange = '1'; // 默认值
        }

        // 广告图片展示高度
        if (config.adsHeight) {
          if (config.adsHeight == 592) {
            this.adsHeightLable = '592';
            this.adsHeight = 592;
          } else {
            this.adsHeightLable = 'custom';
            this.adsHeight = config.adsHeight;
          }
        } else {
          this.adsHeightLable = '592';
          this.adsHeight = 592;
        }

        // 广告图片url处理
        if (config.adsImg && this.isValidImageUrl(config.adsImg)) {
          // 使用工具方法获取显示URL
          this.adsImg = config.adsImg;
        } else {
          this.adsImg = '';
        }

        // 处理活动相关数据
        if (config.adsActivityType && config.adsActivityId) {
          // 根据adsActivityDetailType设置默认的selectedLink
          if (config.adsActivityDetailType) {
            switch (config.adsActivityDetailType) {
              case 1:
                this.selectedLink = '/activity/discount'; // 折扣活动
                break;
              case 2:
                this.selectedLink = '/activity/seckill'; // 秒杀活动
                break;
              case 3:
                this.selectedLink = '/activity/newuser'; // 新人专享活动
                break;
              case 4:
                this.selectedLink = '/activity/recharge'; // 充值活动
                break;
              default:
                this.selectedLink = '/activity/discount'; // 默认为折扣活动
            }
          } else {
            this.selectedLink = '/activity/discount'; // 如果没有adsActivityDetailType，默认设置为折扣活动
          }

          // 保存活动信息
          this.selectedActivityInfo = {
            id: config.adsActivityId,
            name: config.adsActivityName || '已选择活动',
            type: config.adsActivityType,
          };

          // 获取活动详细信息
          await this.loadSelectedActivityInfo(config.adsActivityId, config.adsActivityType);
        } else {
          // 清空活动信息
          this.selectedActivityInfo = {
            id: null,
            name: '',
            type: null,
          };
          this.selectedLink = '';
        }

        // 填充快捷图标数据
        this.functionIconNum = config.functionIconNum || 3;
        this.functionIconRow = config.functionIconRow || 1;

        // 填充间距数据
        this.spacingColor = config.spacingColor === 1 ? 'white' : 'gray';
        if (config.spacingHeight == 20) {
          this.spacingHeightType = '20';
          this.customSpacingHeight = 20;
        } else {
          this.spacingHeightType = 'custom';
          this.customSpacingHeight = config.spacingHeight || 20;
        }

        // 获取已选优惠券ID
        // this.homeFitmentCouponId = config.homeFitmentCouponId || null

        // // 获取已配置的装修优惠券
        // try {
        //   const fitmentCoupons = await getFitmentCouponListApi({ type: configType })
        //   if (fitmentCoupons && fitmentCoupons.length > 0) {
        //     // 根据couponId匹配完整的优惠券信息
        //     this.selectedCouponList = []
        //     fitmentCoupons.forEach(fitmentCoupon => {
        //       const fullCoupon = this.allCouponList.find(coupon => coupon.id === fitmentCoupon.couponId)
        //       if (fullCoupon) {
        //         this.selectedCouponList.push(fullCoupon)
        //       }
        //     })
        //   }
        // } catch (error) {
        //   console.error('获取装修优惠券失败:', error)
        // }

        // 更新过滤列表
        this.updateFilteredCouponLists();
      } catch (error) {
        console.error('获取配置失败:', error);
        this.$message.error('获取配置失败，请刷新重试');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 检查图片URL是否有效
     * @param {String} url - 图片URL
     * @returns {Boolean} URL是否有效
     */
    isValidImageUrl(url) {
      if (!url || typeof url !== 'string') {
        return false;
      }

      // 检查是否是有效的HTTP/HTTPS URL
      try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
      } catch (e) {
        // 如果不是完整URL，检查是否是相对路径或以/开头的路径
        return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
      }
    },
    /**
     * 切换选项卡
     * @param {String} tab - 选项卡名称
     */
    switchTab(tab) {
      this.activeTab = tab;
    },
    /**
     * 添加图标
     */
    addIcon() {
      this.iconList.push({
        image: '',
        title: '',
        subtitle: '',
        link: '',
      });
    },
    /**
     * 获取商户列表
     * @param {Object} params - 查询参数
     */
    async getMerchantList(params = {}) {
      try {
        this.merchantLoading = true;
        const requestParams = {
          // page: this.merchantPagination.currentPage,
          // limit: this.merchantPagination.pageSize,
          limit: -1,
          keywords: this.merchantSearchKeyword,
        };

        // 如果有搜索关键词，添加到请求参数中
        if (this.merchantSearchKeyword.trim()) {
          requestParams.name = this.merchantSearchKeyword.trim();
        }

        const response = await merchantListApi(requestParams);

        if (response && response.list) {
          this.merchantList = response.list.map((merchant) => ({
            ...merchant,
            checked: this.selectedMerchantList.some((selected) => selected.merId === merchant.id),
          }));
          this.merchantPagination.total = response.total || 0;
        } else {
          this.merchantList = [];
          this.merchantPagination.total = 0;
        }
      } catch (error) {
        console.error('获取商户列表失败:', error);
        this.$message.error('获取商户列表失败');
        this.merchantList = [];
        this.merchantPagination.total = 0;
      } finally {
        this.merchantLoading = false;
      }
    },

    /**
     * 获取装修商户关系列表
     */
    async getFitmentMerchantList() {
      try {
        const response = await getFitmentMerchantListApi({
          type: parseInt(this.pageType),
        });

        if (response && response.list) {
          this.selectedMerchantList = response.list;
          // 更新商户列表的选中状态
          this.merchantList.forEach((merchant) => {
            merchant.checked = this.selectedMerchantList.some((selected) => selected.merId === merchant.id);
          });
        }
      } catch (error) {
        console.error('获取装修商户关系失败:', error);
        this.$message.error('获取装修商户关系失败');
      }
    },

    /**
     * 添加商户 - 直接调用更新接口
     */
    async addMerchant() {
      const selectedMerchants = this.merchantList.filter((merchant) => merchant.checked);

      if (selectedMerchants.length === 0) {
        this.$message.warning('请先选择商户');
        return;
      }

      if (selectedMerchants.length > 3) {
        this.$message.warning('建议最多选择3家商户');
        return;
      }

      try {
        const merchantData = {
          merchants: selectedMerchants.map((merchant, index) => ({
            merId: merchant.id,
            type: parseInt(this.pageType),
            sort: index + 1,
          })),
        };

        await updateFitmentMerchantApi(merchantData);
        this.$message.success('商户配置更新成功');

        // 重新加载装修商户关系
        await this.getFitmentMerchantList();
      } catch (error) {
        console.error('更新商户配置失败:', error);
        this.$message.error('更新商户配置失败');
      }
    },
    /**
     * 搜索商户
     */
    searchMerchants() {
      this.merchantPagination.currentPage = 1;
      this.getMerchantList();
    },

    /**
     * 商户分页大小改变
     * @param {Number} val - 分页大小
     */
    handleMerchantSizeChange(val) {
      this.merchantPagination.pageSize = val;
      this.merchantPagination.currentPage = 1; // 重置到第一页
      this.getMerchantList();
    },

    /**
     * 商户当前页改变
     * @param {Number} val - 当前页
     */
    handleMerchantCurrentChange(val) {
      this.merchantPagination.currentPage = val;
      this.getMerchantList();
    },
    /**
     * 处理商户选择
     * @param {Object} merchant - 商户对象
     */
    handleMerchantSelect(merchant) {
      if (merchant.checked) {
        // 检查是否超过最大选择数量（建议最多3家）
        const currentSelected = this.merchantList.filter((item) => item.checked);
        if (currentSelected.length > 3) {
          this.$message.warning('建议最多选择3家商户');
          merchant.checked = false;
          return;
        }
      }
    },

    /**
     * 加载选中活动的详细信息
     * @param {Number} activityId - 活动ID
     * @param {Number} activityType - 活动类型
     */
    async loadSelectedActivityInfo(activityId, activityType) {
      try {
        let activityInfo = null;

        if (activityType === 2) {
          // 充值活动
          const response = await topupListApi({
            id: activityId,
            page: 1,
            limit: 1,
          });
          if (response && response.list && response.list.length > 0) {
            activityInfo = response.list.find((item) => item.id == activityId);
          }
        } else if (activityType === 1) {
          // 其他活动（折扣活动等）
          const response = await discountListApi({
            belongTo: 'merchant',
            id: activityId,
            page: 1,
            limit: 1,
          });
          if (response && response.list && response.list.length > 0) {
            activityInfo = response.list.find((item) => item.id == activityId);
          }
        }

        if (activityInfo) {
          this.selectedActivityInfo = {
            id: activityInfo.id,
            name: activityInfo.name || activityInfo.title,
            type: activityType,
          };
        }
      } catch (error) {
        console.error('获取活动信息失败:', error);
        // 如果获取失败，保持基本信息
        this.selectedActivityInfo = {
          id: activityId,
          name: `活动ID: ${activityId}`,
          type: activityType,
        };
      }
    },

    /**
     * 删除图片
     */
    removeImage() {
      this.adsImg = '';
    },
    /**
     * 获取要保存的相对路径
     * @param {String} displayUrl - 显示用的完整URL
     * @returns {String} 相对路径
     */
    getRelativePath(displayUrl) {
      if (!displayUrl) return '';

      // 使用正则表达式匹配并移除任何域名部分，只保留相对路径
      // 匹配 http:// 或 https:// 开头的完整URL，提取域名后的路径部分
      const urlPattern = /^https?:\/\/[^\/]+\/(.*)/;
      const match = displayUrl.match(urlPattern);

      if (match) {
        // 如果匹配到完整URL，返回域名后的路径部分
        return match[1];
      }

      // 如果不是完整URL，直接返回原值（可能已经是相对路径）
      return displayUrl;
    },
    /**
     * 获取快捷图标列表
     */
    async getFunctionIcons() {
      try {
        const response = await getFitmentFunctionListApi({ type: parseInt(this.pageType) });

        // 由于request.js拦截器已经处理了响应，直接使用response作为数据
        if (response && Array.isArray(response)) {
          this.functionIcons = response || [];

          // 只根据 sort 排序
          this.functionIcons.sort((a, b) => a.sort - b.sort);
        } else {
          console.log('API返回数据格式异常:', response);
        }
      } catch (error) {
        console.error('获取快捷图标失败:', error);
        this.$message.error('获取快捷图标失败');
      }
    },

    /**
     * 选择图标图片
     */
    selectIconImage() {
      this.$refs.iconFileInput.click();
    },

    /**
     * 处理图标文件选择
     * @param {Event} event - 文件选择事件
     */
    handleIconFileChange(event) {
      const file = event.target.files[0];
      if (file) {
        this.uploadIconImage(file);
      }
    },

    /**
     * 上传图标图片
     * @param {File} file - 图片文件
     */
    async uploadIconImage(file) {
      // 文件大小检查
      if (file.size > 5 * 1024 * 1024) {
        this.$message.error('图片大小不能超过5MB');
        return;
      }

      // 文件类型检查
      if (!file.type.startsWith('image/')) {
        this.$message.error('请选择图片文件');
        return;
      }

      this.iconUploading = true;

      try {
        const formData = new FormData();
        formData.append('multipart', file);
        formData.append('model', 'system');
        formData.append('pid', '0');

        const response = await uploadAdsImageApi(formData); // 复用广告上传API

        if (response && response.url) {
          this.iconImg = response.url;

          this.$message.success('图标上传成功');
        } else {
          this.$message.error('图标上传失败：未返回有效URL');
        }
      } catch (error) {
        console.error('图标上传失败:', error);
        this.$message.error('图标上传失败：' + (error.message || '未知错误'));
      } finally {
        this.iconUploading = false;
        const fileInput = this.$refs.iconFileInput;
        if (fileInput) {
          fileInput.value = '';
        }
      }
    },

    /**
     * 删除图标图片
     */
    removeIconImage() {
      this.iconImg = '';
      this.$message.success('图标图片已删除');
    },

    /**
     * 更新编辑中图标的预览
     */
    updateEditingIconPreview() {
      // 只在编辑模式下且有编辑ID时才更新预览
      if (this.editingIconId && this.iconTitle.trim()) {
        const iconIndex = this.functionIcons.findIndex((item) => item.id === this.editingIconId);
        if (iconIndex > -1) {
          // 实时更新预览数据，但不保存到后端
          this.$set(this.functionIcons, iconIndex, {
            ...this.functionIcons[iconIndex],
            iconUrl: this.iconImg || this.functionIcons[iconIndex].iconUrl,
            linkUrl: this.iconLink || '',
            name: this.iconTitle,
            secondName: this.iconSubtitle || '',
            illustrate: this.iconSubtitle || '',
          });
        }
      }
    },

    /**
     * 添加快捷图标到预览区域（本地操作）
     */
    addIconToPreview() {
      if (!this.iconTitle.trim()) {
        this.$message.warning('请输入图标名称');
        return;
      }

      if (!this.iconImg) {
        this.$message.warning('请上传图标图片');
        return;
      }

      // 根据链接类型生成最终链接
      let finalLink = '';
      if (this.iconLinkType === 'activity') {
        if (!this.iconSelectedActivityInfo.id) {
          this.$message.warning('请选择活动');
          return;
        }
        finalLink = `/pages/activity/index?type=${this.iconSelectedActivityType}&activityId=${this.iconSelectedActivityInfo.id}`;
      } else {
        finalLink = this.iconLink || '';
      }

      if (this.editingIconId) {
        // 编辑模式：更新现有图标
        const iconIndex = this.functionIcons.findIndex((item) => item.id === this.editingIconId);
        if (iconIndex > -1) {
          this.$set(this.functionIcons, iconIndex, {
            ...this.functionIcons[iconIndex],
            iconUrl: this.iconImg,
            linkUrl: finalLink,
            name: this.iconTitle,
            secondName: this.iconSubtitle || '',
            illustrate: this.iconSubtitle || '',
            // 保持原有的position值
          });
        }

        this.$message.success('图标已更新');

        // 立即保存更改到后端
        this.saveFunctionIcons();
      } else {
        // 添加模式：创建新图标
        const maxSort =
          this.functionIcons.length > 0 ? Math.max(...this.functionIcons.map((icon) => icon.sort || 0)) : 0;

        // 自动分配到未满的行
        let targetPos = 1;
        for (let i = 1; i <= this.functionIconRow; i++) {
          const count = this.functionIcons.filter((icon) => icon.position === i).length;
          if (count < this.functionIconNum) {
            targetPos = i;
            break;
          }
        }

        const newIcon = {
          id: Date.now(),
          iconUrl: this.iconImg,
          linkUrl: finalLink,
          name: this.iconTitle,
          secondName: this.iconSubtitle || '',
          illustrate: this.iconSubtitle || '',
          sort: maxSort + 1,
          position: targetPos, // 自动分配到未满的行
          type: parseInt(this.pageType),
          isNew: true,
        };

        this.functionIcons.push(newIcon);
        this.$message.success('图标已添加到预览区域');
      }

      // 清空输入框和编辑状态
      this.iconTitle = '';
      this.iconSubtitle = '';
      this.iconLink = '';
      this.iconImg = '';
      this.iconPosition = 1;
      this.editingIconId = null;
      this.iconLinkType = 'normal';
      this.iconSelectedActivityType = '';
      this.iconSelectedActivityInfo = {
        id: null,
        name: '',
        type: null,
      };
    },

    /**
     * 编辑图标
     * @param {Object} icon - 要编辑的图标
     */
    editIcon(icon) {
      this.editingIcon = icon;
      this.iconForm = {
        id: icon.id,
        iconUrl: icon.iconUrl,
        linkUrl: icon.linkUrl,
        name: icon.name,
        secondName: icon.secondName,
        illustrate: icon.illustrate,
        position: icon.position,
        sort: icon.sort,
        type: icon.type,
      };
      this.showIconEditDialog = true;
    },

    /**
     * 保存图标编辑（本地操作）
     */
    saveIconEdit() {
      if (!this.iconForm.name.trim()) {
        this.$message.warning('请输入图标名称');
        return;
      }

      const iconIndex = this.functionIcons.findIndex((icon) => icon.id === this.editingIcon.id);
      if (iconIndex !== -1) {
        // 更新图标信息
        this.functionIcons[iconIndex] = {
          ...this.functionIcons[iconIndex],
          ...this.iconForm,
          isModified: true, // 标记为已修改
        };
      }

      this.showIconEditDialog = false;
      this.editingIcon = null;
      this.$message.success('图标信息已更新');
    },

    /**
     * 删除图标（本地操作）
     * @param {Object} icon - 要删除的图标
     */
    deleteIcon(icon) {
      this.$confirm('确定要删除这个图标吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const iconIndex = this.functionIcons.findIndex((item) => item.id === icon.id);
        if (iconIndex !== -1) {
          this.functionIcons.splice(iconIndex, 1);
          this.$message.success('图标已删除');
        }
      });
    },

    /**
     * 处理编辑图标
     * @param {Object} icon - 图标对象
     */
    handleEditIcon(icon) {
      // 切换到快捷图标选项卡
      this.activeTab = 'quickIcon';

      // 解析链接类型
      const linkInfo = this.parseLinkType(icon.linkUrl);

      // 填充编辑数据
      this.iconTitle = icon.name || '';
      this.iconSubtitle = icon.secondName || icon.illustrate || '';
      this.iconLink = icon.linkUrl || '';
      this.iconPosition = icon.position || 1;

      let iconUrl = icon.iconUrl;
      if (this.isLocalDebug && iconUrl && iconUrl.startsWith('https://pa.xinhuasp.com')) {
        iconUrl = iconUrl.replace('https://pa.xinhuasp.com', 'http://localhost:8080');
      } else if (this.isLocalDebug && iconUrl && !iconUrl.startsWith('http')) {
        iconUrl = `http://localhost:8080/${iconUrl}`;
      }
      this.iconImg = iconUrl;

      // 标记当前编辑的图标
      this.editingIconId = icon.id;

      // 设置链接类型
      this.iconLinkType = linkInfo.type;

      if (linkInfo.type === 'activity') {
        this.iconSelectedActivityType = linkInfo.activityType;
        // 如果是活动链接，尝试获取活动信息
        this.getIconActivityInfo(linkInfo.activityId, linkInfo.activityType);
      } else {
        this.iconSelectedActivityType = '';
        this.iconSelectedActivityInfo = {
          id: null,
          name: '',
          type: null,
        };
      }

      // this.$message.info('已切换到编辑模式')
    },

    /**
     * 处理删除图标
     * @param {Object} icon - 图标对象
     * @param {Number} index - 图标索引
     */
    handleDeleteIcon(icon, index) {
      this.$confirm('确定要删除这个图标吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 从数组中移除图标
          const iconIndex = this.functionIcons.findIndex((item) => item.id === icon.id);
          if (iconIndex > -1) {
            this.functionIcons.splice(iconIndex, 1);

            // 重新排序，避免空隙
            this.reorderIconSort();

            this.$message.success('图标已删除');
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    /**
     * 重新排序图标的sort值，避免空隙
     */
    reorderIconSort() {
      // 按当前sort排序
      this.functionIcons.sort((a, b) => (a.sort || 0) - (b.sort || 0));

      // 重新分配连续的sort值
      this.functionIcons.forEach((icon, index) => {
        icon.sort = index + 1;
      });
    },

    /**
     * 处理添加图标
     */
    handleAddIcon() {
      // 切换到快捷图标选项卡
      this.activeTab = 'quickIcon';

      // 清空编辑数据
      this.iconTitle = '';
      this.iconSubtitle = '';
      this.iconLink = '';
      this.iconImg = '';
      this.iconPosition = 1;
      this.editingIconId = null;
      this.iconLinkType = 'normal';
      this.iconSelectedActivityType = '';
      this.iconSelectedActivityInfo = {
        id: null,
        name: '',
        type: null,
      };

      // this.$message.info('已切换到添加模式')
    },

    /**
     * 拖拽排序后处理图标顺序
     * @param {Array} newOrder - 拖拽后图标新顺序
     */
    handleIconDragSort(newOrder) {
      // 全部按当前顺序重新编号 sort
      newOrder.forEach((icon, idx) => {
        icon.sort = idx + 1;
      });
      this.functionIcons = newOrder;
    },

    /**
     * 保存前重排每行的 sort
     * @param {Array} icons - 所有图标数组
     * @returns {Array} 处理后的图标数组
     */
    normalizeSortByRow(icons) {
      // 按 position 分组
      const grouped = {};
      icons.forEach((icon) => {
        if (!grouped[icon.position]) grouped[icon.position] = [];
        grouped[icon.position].push(icon);
      });
      // 每组内 sort 从 1 开始递增
      const result = [];
      Object.keys(grouped).forEach((pos) => {
        grouped[pos]
          .sort((a, b) => a.sort - b.sort)
          .forEach((icon, idx) => {
            icon.sort = idx + 1;
            result.push(icon);
          });
      });
      return result;
    },
    /**
     * 保存快捷图标配置
     */
    async saveFunctionIcons() {
      try {
        // 保存前重排每行的 sort
        const icons = this.normalizeSortByRow(this.functionIcons);
        const functions = icons.map((icon) => ({
          id: icon.id,
          name: icon.name,
          iconUrl: this.getRelativePath(icon.iconUrl),
          linkUrl: icon.linkUrl,
          illustrate: icon.illustrate,
          sort: icon.sort,
          position: icon.position || 1, // 使用数据库中的position值
          type: parseInt(this.pageType),
          secondName: icon.secondName,
        }));

        const response = await updateFitmentFunctionApi({ functions });

        // 不显示成功消息，由调用方统一处理
      } catch (error) {
        console.error('保存快捷图标失败:', error);
        throw error; // 抛出错误让调用方处理
      }
    },

    /**
     * 页面类型切换处理
     * @param {String} newType - 新的页面类型
     */
    async handlePageTypeChange(newType) {
      // 切换页面类型时重新获取对应的配置数据
      this.getMerchantConfig();
      // 重新获取对应页面类型的商品数据
      await this.getFitmentProductList();
      // 重新获取对应页面类型的商户数据
      await this.getFitmentMerchantList();
      // 重新获取对应页面类型的快捷图标数据
      await this.getFunctionIcons();
    },
    /**
     * 处理快捷图标链接类型变化
     * @param {String} type - 链接类型
     */
    handleIconLinkTypeChange(type) {
      if (type === 'normal') {
        // 切换到常规时，清空活动相关数据
        this.iconSelectedActivityType = '';
        this.iconSelectedActivityInfo = {
          id: null,
          name: '',
          type: null,
        };
      } else {
        // 切换到活动时，清空常规链接
        this.iconLink = '';
      }
    },

    /**
     * 处理快捷图标活动类型变化
     * @param {String} activityType - 活动类型
     */
    handleIconActivityTypeChange(activityType) {
      // 清空之前选择的活动
      this.iconSelectedActivityInfo = {
        id: null,
        name: '',
        type: null,
      };
    },

    /**
     * 打开快捷图标活动选择弹窗
     */
    async openIconActivityDialog() {
      if (!this.iconSelectedActivityType) {
        this.$message.warning('请先选择活动类型');
        return;
      }

      // 清空快捷图标活动列表
      this.iconActivityList = [];

      // 根据活动类型获取对应的活动列表
      const activityTypeMap = {
        newproduct: '1', // 折扣活动
        newuser: '3', // 新人活动
        seckill: '2', // 秒杀活动
      };

      const activityType = activityTypeMap[this.iconSelectedActivityType] || '1';

      try {
        // 直接调用活动列表API获取数据
        const response = await discountListApi({
          belongTo: 'merchant',
          type: parseInt(activityType),
          page: 1,
          limit: -1,
          isOpen: '1', // 只获取进行中的活动
        });

        this.iconActivityList = response.list || [];
        this.showIconActivityDialog = true;
      } catch (error) {
        console.error('获取活动列表失败:', error);
        this.$message.error('获取活动列表失败');
      }
    },

    /**
     * 确认选择快捷图标活动
     * @param {Object} activity - 选中的活动
     */
    confirmIconActivitySelect(activity) {
      this.iconSelectedActivityInfo = {
        id: activity.id,
        name: activity.name,
        type: this.iconSelectedActivityType,
      };

      // 生成活动链接
      this.iconLink = `/pages/activity/index?type=${this.iconSelectedActivityType}&activityId=${activity.id}`;

      this.showIconActivityDialog = false;
      this.$message.success('活动选择成功');
    },

    /**
     * 获取快捷图标活动信息
     * @param {Number} activityId - 活动ID
     * @param {String} activityType - 活动类型
     */
    async getIconActivityInfo(activityId, activityType) {
      try {
        let activityInfo = null;

        if (activityType === 'newuser') {
          // 新人活动
          const response = await discountListApi({
            belongTo: 'merchant',
            id: activityId,
            type: 3,
            page: 1,
            limit: 1,
          });
          if (response && response.list && response.list.length > 0) {
            activityInfo = response.list.find((item) => item.id == activityId);
          }
        } else if (activityType === 'seckill') {
          // 秒杀活动
          const response = await discountListApi({
            belongTo: 'merchant',
            id: activityId,
            type: 2,
            page: 1,
            limit: 1,
          });
          if (response && response.list && response.list.length > 0) {
            activityInfo = response.list.find((item) => item.id == activityId);
          }
        } else if (activityType === 'newproduct') {
          // 折扣活动
          const response = await discountListApi({
            belongTo: 'merchant',
            id: activityId,
            type: 1,
            page: 1,
            limit: 1,
          });
          if (response && response.list && response.list.length > 0) {
            activityInfo = response.list.find((item) => item.id == activityId);
          }
        }

        if (activityInfo) {
          this.iconSelectedActivityInfo = {
            id: activityInfo.id,
            name: activityInfo.name,
            type: activityType,
          };
        } else {
          // 如果获取失败，显示默认信息
          this.iconSelectedActivityInfo = {
            id: activityId,
            name: `活动ID: ${activityId}`,
            type: activityType,
          };
        }
      } catch (error) {
        console.error('获取活动信息失败:', error);
        // 如果获取失败，显示默认信息
        this.iconSelectedActivityInfo = {
          id: activityId,
          name: `活动ID: ${activityId}`,
          type: activityType,
        };
      }
    },

    /**
     * 解析链接判断类型
     * @param {String} link - 链接地址
     * @returns {Object} 解析结果
     */
    parseLinkType(link) {
      if (!link) {
        return { type: 'normal', activityType: '', activityId: null };
      }

      // 检查是否为活动链接格式
      const activityPattern = /pages\/activity\/index\?type=([^&]+)&activityId=(\d+)/;
      const match = link.match(activityPattern);

      if (match) {
        return {
          type: 'activity',
          activityType: match[1],
          activityId: parseInt(match[2]),
        };
      }

      return { type: 'normal', activityType: '', activityId: null };
    },

    /**
     * 获取广告配置列表
     */
    async getAdsList() {
      try {
        this.loading = true;
        const params = { type: 1 };
        const response = await getCarouselListApi(params);

        if (response && response.length > 0) {
          // 取第一个广告配置
          const adConfig = response[0];
          this.adsImg = adConfig.imageUrl;
          this.adsHeight = adConfig.adsHeight;
          this.adsRange = adConfig.adsRange.toString();

          // 根据高度设置标签
          if (adConfig.adsHeight === 592) {
            this.adsHeightLable = '592';
          } else {
            this.adsHeightLable = 'custom';
          }

          try {
            // 解析链接获取活动信息
            if (adConfig.linkUrl) {
              const linkInfo = this.parseLinkType(adConfig.linkUrl);
              if (linkInfo.type === 'activity') {
                this.adsSelectedActivityType = linkInfo.activityType;

                // 根据活动类型设置选择框的值
                const activityTypeMap = {
                  newuser: '/activity/newuser',
                  newproduct: '/activity/discount',
                  seckill: '/activity/seckill',
                };

                this.selectedLink = activityTypeMap[linkInfo.activityType] || '/activity/discount';

                // 获取活动详细信息
                await this.getAdsActivityInfo(linkInfo.activityId, linkInfo.activityType);
              }
            }
          } catch (error) {
            this.$message.warning('广告链接解析失败');
          }
        }
      } catch (error) {
        this.$message.error('获取广告配置失败');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取广告活动信息
     * @param {Number} activityId - 活动ID
     * @param {String} activityType - 活动类型
     */
    async getAdsActivityInfo(activityId, activityType) {
      try {
        let activityInfo = null;

        const activityTypeMap = {
          newuser: 3,
          newproduct: 1,
          seckill: 2,
        };

        const typeId = activityTypeMap[activityType] || 1;

        const response = await discountListApi({
          belongTo: 'merchant',
          id: activityId,
          type: typeId,
          page: 1,
          limit: 1,
        });

        if (response && response.list && response.list.length > 0) {
          activityInfo = response.list.find((item) => item.id == activityId);
        }

        if (activityInfo) {
          this.adsSelectedActivityInfo = {
            id: activityInfo.id,
            name: activityInfo.name,
            type: activityType,
          };

          this.selectedActivityInfo = {
            id: activityInfo.id,
            name: activityInfo.name,
            type: activityType,
          };
        } else {
          // 如果获取失败，显示默认信息
          this.adsSelectedActivityInfo = {
            id: activityId,
            name: `活动ID: ${activityId}`,
            type: activityType,
          };

          this.selectedActivityInfo = {
            id: activityId,
            name: `活动ID: ${activityId}`,
            type: activityType,
          };
        }
      } catch (error) {
        console.error('获取活动信息失败:', error);
        // 如果获取失败，显示默认信息
        this.adsSelectedActivityInfo = {
          id: activityId,
          name: `活动ID: ${activityId}`,
          type: activityType,
        };

        this.selectedActivityInfo = {
          id: activityId,
          name: `活动ID: ${activityId}`,
          type: activityType,
        };
      }
    },

    /**
     * 保存广告配置
     */
    async saveAdsConfig() {
      try {
        if (!this.adsImg) {
          this.$message.warning('请先上传广告图片');
          return;
        }

        if (!this.adsSelectedActivityInfo.id) {
          this.$message.warning('请选择活动');
          return;
        }

        // 生成活动链接
        const finalLink = `/pages/activity/index?type=${this.adsSelectedActivityType}&activityId=${this.adsSelectedActivityInfo.id}`;

        const carousels = [
          {
            imageUrl: this.getRelativePath(this.adsImg),
            linkUrl: finalLink,
            adsHeight: parseInt(this.adsHeight),
            adsRange: parseInt(this.adsRange),
            type: 1,
          },
        ];

        await updateCarouselApi({ carousels });
        // this.$message.success('广告配置保存成功');
      } catch (error) {
        console.error('保存广告配置失败:', error);
        this.$message.error('保存广告配置失败');
      }
    },
    /**
     * 保存配置
     */
    async saveConfig() {
      this.loading = true;
      try {
        // 如果当前处于编辑模式，先保存编辑的内容
        if (this.editingIconId && this.iconTitle.trim()) {
          const iconIndex = this.functionIcons.findIndex((item) => item.id === this.editingIconId);
          if (iconIndex > -1) {
            // 更新编辑中的图标数据
            this.$set(this.functionIcons, iconIndex, {
              ...this.functionIcons[iconIndex],
              iconUrl: this.iconImg,
              linkUrl: this.iconLink || '',
              name: this.iconTitle,
              secondName: this.iconSubtitle || '',
              illustrate: this.iconSubtitle || '',
            });

            // 清空编辑状态
            this.iconTitle = '';
            this.iconSubtitle = '';
            this.iconLink = '';
            this.iconImg = '';
            this.iconPosition = 1;
            this.editingIconId = null;
          }
        }

        // 保存广告配置
        await this.saveAdsConfig();

        // 保存快捷图标配置
        await this.saveFunctionIcons();

        // 格式化日期函数
        const formatDate = (date) => {
          if (!date) return null;
          const d = new Date(date);
          const year = d.getFullYear();
          const month = String(d.getMonth() + 1).padStart(2, '0');
          const day = String(d.getDate()).padStart(2, '0');
          const hours = String(d.getHours()).padStart(2, '0');
          const minutes = String(d.getMinutes()).padStart(2, '0');
          const seconds = String(d.getSeconds()).padStart(2, '0');
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        };

        // 确定活动类型
        let adsActivityType = null;
        if (this.selectedActivityInfo.id) {
          // 根据选择的链接类型确定活动类型
          switch (this.selectedLink) {
            case '/activity/recharge':
              adsActivityType = 2; // 充值活动
              break;
            case '/activity/discount':
            case '/activity/newuser':
            case '/activity/seckill':
              adsActivityType = 1; // 其他活动
              break;
            default:
              adsActivityType = 1;
          }
        }

        // 构建请求数据
        const configData = {
          // 基础信息
          type: parseInt(this.pageType), // 确保type是数字类型
          title: this.title,
          effectiveTimeStart: formatDate(this.effectiveTimeRange && this.effectiveTimeRange[0]),
          effectiveTimeEnd: formatDate(this.effectiveTimeRange && this.effectiveTimeRange[1]),
          // 广告
          adsRange: parseInt(this.adsRange),
          adsHeight: parseInt(this.adsHeight),
          adsImg: this.getRelativePath(this.adsImg),
          // 活动相关字段
          adsActivityType: adsActivityType,
          adsActivityId: this.selectedActivityInfo.id,
          // 间距
          spacingColor: this.spacingColor === 'white' ? 1 : 2,
          spacingHeight: this.spacingHeight,
          // 快捷图标
          functionIconNum: this.functionIconNum,
          functionIconRow: this.functionIconRow === 'none' ? 0 : this.functionIconRow,
        };

        // 调用API
        await updatePlatformConfigApi(configData);

        // 单独处理优惠券装修配置
        if (this.selectedCouponList.length > 0) {
          const couponData = {
            coupons: this.selectedCouponList.map((coupon) => ({
              couponId: coupon.id,
              type: parseInt(this.pageType),
            })),
          };
          await updateFitmentCouponApi(couponData);
        }

        // 保存商品装修配置
        if (this.selectedProductList.length > 0) {
          const productData = {
            products: this.selectedProductList.map((product, index) => ({
              productId: product.id,
              sort: index + 1,
              type: parseInt(this.pageType),
            })),
          };
          await fitmentProductUpdateApi(productData);
        } else {
          // 清空所有商品
          await fitmentProductUpdateApi({ products: [] });
        }

        // 保存成功提示
        this.$message.success('保存成功');
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败，请重试');
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {
    // 监听已选优惠券搜索条件变化
    'selectedCouponSearch.name'() {
      this.updateFilteredCouponLists();
    },
    'selectedCouponSearch.code'() {
      this.updateFilteredCouponLists();
    },
    'selectedCouponSearch.status'() {
      this.updateFilteredCouponLists();
    },
    'selectedCouponSearch.type'() {
      this.updateFilteredCouponLists();
    },
    // 监听未选优惠券搜索条件变化
    'unselectedCouponSearch.name'() {
      this.updateFilteredCouponLists();
    },
    'unselectedCouponSearch.code'() {
      this.updateFilteredCouponLists();
    },
    'unselectedCouponSearch.status'() {
      this.updateFilteredCouponLists();
    },
    'unselectedCouponSearch.type'() {
      this.updateFilteredCouponLists();
    },
    // 监听已选商品搜索条件变化
    'selectedProductSearch.name'() {
      this.updateFilteredProductLists();
    },
    'selectedProductSearch.code'() {
      this.updateFilteredProductLists();
    },
    // 监听未选商品搜索条件变化
    'unselectedProductSearch.name'() {
      this.updateFilteredProductLists();
    },
    'unselectedProductSearch.code'() {
      this.updateFilteredProductLists();
    },
    // 监听图标标题变化
    iconTitle: {
      handler(newVal) {
        this.updateEditingIconPreview();
      },
      immediate: false,
    },
    // 监听图标副标题变化
    iconSubtitle: {
      handler(newVal) {
        this.updateEditingIconPreview();
      },
      immediate: false,
    },
    // 监听图标图片变化
    iconImg: {
      handler(newVal) {
        this.updateEditingIconPreview();
      },
      immediate: false,
    },
    // 监听图标链接变化
    iconLink: {
      handler(newVal) {
        this.updateEditingIconPreview();
      },
      immediate: false,
    },
    // 监听图标链接类型变化
    iconLinkType: {
      handler(newVal) {
        this.updateEditingIconPreview();
      },
      immediate: false,
    },
    // 监听图标活动信息变化
    'iconSelectedActivityInfo.id': {
      handler(newVal) {
        this.updateEditingIconPreview();
      },
      immediate: false,
    },
  },
};
</script>

<style scoped>
/* 页面类型 */
.divBox .box-card .page-type {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

/* 基础信息 */
.divBox .box-card .basic-info {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.divBox .box-card .basic-info .section-header {
  background-color: #f5f5f5;
  padding: 10px 15px;
  margin: -20px -20px 20px -20px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #e6e6e6;
}

.divBox .box-card .basic-info .form-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.divBox .box-card .basic-info .form-row label {
  width: 80px;
  margin-right: 10px;
}

/* 装修样式 */
.divBox .box-card .decoration-style {
  padding: 20px;
}

.divBox .box-card .decoration-style .section-header {
  background-color: #f5f5f5;
  padding: 10px 15px;
  margin: -20px -20px 20px -20px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #e6e6e6;
}

.divBox .box-card .decoration-style .content-wrapper {
  display: flex;
  gap: 20px;
  height: 540px;
}

/* 左侧选项 */
.divBox .box-card .decoration-style .left-options {
  width: 240px;
}

.divBox .box-card .decoration-style .left-options .options-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.divBox .box-card .decoration-style .left-options .option-item {
  padding: 8px 6px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e6e6e6;
  cursor: pointer;
  text-align: center;
  font-size: 12px;
}

.divBox .box-card .decoration-style .left-options .option-item.active {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

/* 预览区域 */
.divBox .box-card .decoration-style .preview-area {
  width: 300px;
  height: 540px;
  background-color: #f0f0f0;
}

/* 配置区域 */
.divBox .box-card .decoration-style .config-area {
  width: 700px;
  height: 540px;
  background-color: #f0f0f0;
  padding: 15px;
  border-radius: 4px;
  flex: none;
}

.divBox .box-card .decoration-style .config-area .config-header {
  background-color: #d0d0d0;
  padding: 8px 12px;
  margin: -15px -15px 15px -15px;
  font-size: 14px;
  color: #333;
}

.divBox .box-card .decoration-style .config-area .config-item {
  margin-bottom: 20px;
}

.divBox .box-card .decoration-style .config-area .config-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: normal;
}

/* 行内配置项 */
.divBox .box-card .decoration-style .config-area .config-item.inline-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.divBox .box-card .decoration-style .config-area .config-item.inline-item label {
  margin-bottom: 0;
  margin-right: 10px;
  min-width: 80px;
  display: inline-block;
}

.divBox .box-card .decoration-style .config-area .config-item .inline-options {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 上传按钮 */
.divBox .box-card .decoration-style .config-area .config-item .upload-btn {
  width: 80px;
  height: 80px;
  border: 1px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: white;
}

.divBox .box-card .decoration-style .config-area .config-item .upload-btn i {
  font-size: 24px;
  color: #409eff;
}

.divBox .box-card .decoration-style .config-area .config-item .upload-tip {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

/* 快捷图标添加布局 */
.divBox .box-card .decoration-style .config-area .add-image-layout {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.divBox .box-card .decoration-style .config-area .add-image-label {
  min-width: 80px;
  margin-bottom: 0;
  margin-top: 8px;
  display: block;
  font-weight: normal;
}

.divBox .box-card .decoration-style .config-area .quick-icon-add-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 30px;
  background: #fff;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  flex: 1;
}

.divBox .box-card .decoration-style .config-area .upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.divBox .box-card .decoration-style .config-area .upload-box {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #fafafa;
  border-radius: 6px;
  transition: all 0.3s;
  margin-bottom: 15px;
}

.divBox .box-card .decoration-style .config-area .upload-box:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.divBox .box-card .decoration-style .config-area .upload-box i {
  font-size: 32px;
  color: #409eff;
}

.divBox .box-card .decoration-style .config-area .upload-box div {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.divBox .box-card .decoration-style .config-area .add-btn-below {
  width: 80px;
}

.divBox .box-card .decoration-style .config-area .input-area {
  flex: 1;
  min-width: 250px;
  display: flex;
  flex-direction: column;
}

.divBox .box-card .decoration-style .config-area .input-area .el-input {
  width: 100%;
}

/* 底部按钮 */
.divBox .box-card .footer {
  padding: 20px;
  text-align: right;
}

/* 商户推荐样式 - 简化版 */
.merchant-config {
  background: white;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
}

.merchant-select-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.merchant-select-header span {
  font-size: 14px;
  color: #333;
}

.suggest-btn {
  color: #999;
  padding: 0;
}

.search-section {
  margin-bottom: 15px;
}

.merchant-list-section {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

/* 优惠券配置样式 */
.coupon-config {
  padding: 20px;
  border-radius: 4px;
}

.coupon-select-section {
  margin-bottom: 20px;
}

.coupon-search-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.select-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.selected-info {
  color: #409eff;
  font-size: 12px;
}

.selected-count {
  margin-left: 10px;
  color: #67c23a;
  font-size: 12px;
}

/* 商品配置样式 */
.product-config {
  padding: 20px;
}

.product-select-section {
  margin-bottom: 20px;
}

.product-select-section .select-header {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
