<template>
  <div class="basic-content">
    <div class="info-row" v-for="(item, key) in displayItems" :key="key">
      <span class="label">{{ item.label }}：</span>
      <span class="value">{{ item.value }}</span>
    </div>
  </div>
</template>

<script>
import { getActivityInfoApi } from '@/api/discount';

export default {
  name: 'BasicInfo',
  props: {
    activityId: {
      type: [String, Number],
      required: true,
    },
    activityData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      detailData: {},
    };
  },
  computed: {
    // 优先使用父组件传递的数据，如果没有则使用本地数据
    currentData() {
      return Object.keys(this.activityData).length > 0 ? this.activityData : this.detailData;
    },
    displayItems() {
      return [
        { label: '活动编码', value: this.currentData.activityNo },
        { label: '活动名称', value: this.currentData.name },
        { label: '后台活动名称', value: this.currentData.backName },
        { label: '活动标签', value: this.currentData.activityLabel },
        // { label: '秒杀价格', value: this.currentData.seckillPrice ? `${this.currentData.seckillPrice}元` : '未设置' },
        { label: '活动描述', value: this.currentData.instruction },
        { label: '活动时间', value: `${this.currentData.beginTime} 至 ${this.currentData.overTime}` },
        { label: '活动对象', value: this.getTargetText(this.currentData.target) },
        { label: '是否可与优惠券叠加', value: this.currentData.ifCoupon ? '是' : '否' },
        {
          label: '购买限制',
          value: `限购数量：${this.currentData.astrictNumber}，每单购买的商品需要满：${this.currentData.astrictAmount} 元`,
        },
      ];
    },
  },
  mounted() {
    this.getActivityInfo();
  },
  watch: {
    activityId: {
      handler() {
        this.getActivityInfo();
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * 获取活动详情
     */
    async getActivityInfo() {
      if (!this.activityId) return;
      try {
        this.detailData = await getActivityInfoApi(this.activityId);
      } catch (error) {
        console.error('获取活动详情失败:', error);
      }
    },

    /**
     * 获取活动对象文本
     * @param {String} target - 活动对象类型
     */
    getTargetText(target) {
      const targetMap = {
        0: '不限',
        1: '新人首次',
      };
      return targetMap[target] || '未知';
    },
  },
};
</script>

<style scoped>
.basic-content {
  .info-row {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;

    .label {
      width: 150px;
      color: #606266;
      font-size: 14px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      flex: 1;
    }
  }
}
</style>
