(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-239e6fb6"],{"1ec5":function(e,t,a){"use strict";a("af55")},af55:function(e,t,a){},f52f:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[e.checkPermi(["merchant:refund:order:status:num"])?a("el-form-item",{attrs:{label:"订单状态："}},[a("el-radio-group",{attrs:{type:"button"},on:{change:e.seachList},model:{value:e.tableFrom.refundStatus,callback:function(t){e.$set(e.tableFrom,"refundStatus",t)},expression:"tableFrom.refundStatus"}},[a("el-radio-button",{attrs:{label:"9"}},[e._v("全部 "+e._s(e.orderChartType.all)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"0"}},[e._v("待审核 "+e._s(e.orderChartType.await)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"1"}},[e._v("审核未通过 "+e._s(e.orderChartType.reject)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("退款中 "+e._s(e.orderChartType.refunding)+"\n              ")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("已退款 "+e._s(e.orderChartType.refunded)+"\n              ")])],1)],1):e._e(),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(t){return e.selectChange(e.tableFrom.dateLimit)}},model:{value:e.tableFrom.dateLimit,callback:function(t){e.$set(e.tableFrom,"dateLimit",t)},expression:"tableFrom.dateLimit"}},e._l(e.fromList.fromTxt,(function(t,i){return a("el-radio-button",{key:i,attrs:{label:t.val}},[e._v(e._s(t.text)+"\n              ")])})),1),e._v(" "),a("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"订单号："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入订单号",size:"small",clearable:""},model:{value:e.tableFrom.orderNo,callback:function(t){e.$set(e.tableFrom,"orderNo",t)},expression:"tableFrom.orderNo"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1),e._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"退款单号："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入退款单号",size:"small",clearable:""},model:{value:e.tableFrom.refundOrderNo,callback:function(t){e.$set(e.tableFrom,"refundOrderNo",t)},expression:"tableFrom.refundOrderNo"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1)],1)],1)])]),e._v(" "),a("div",{staticClass:"mt20"}),e._v(" "),a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:e.tableData.data,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"},"row-key":function(e){return e.refundOrderNo}}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand demo-table-expands",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"退款商品总数："}},[a("span",[e._v(e._s(t.row.totalNum))])]),e._v(" "),a("el-form-item",{attrs:{label:"商家备注："}},[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.merRemark)))])]),e._v(" "),a("el-form-item",{attrs:{label:"退款信息："}},[a("div",{staticClass:"pup_card flex-column"},[a("span",[e._v("退款原因："+e._s(t.row.refundReasonWap))]),e._v(" "),a("span",[e._v("备注说明："+e._s(t.row.refundReasonWapExplain))]),e._v(" "),a("span",{staticClass:"acea-row"},[e._v("\n                  退款凭证：\n                  "),t.row.refundReasonWapImg?e._l(t.row.refundReasonWapImg.split(","),(function(e,t){return a("div",{key:t,staticClass:"demo-image__preview",staticStyle:{width:"35px",height:"auto",display:"inline-block"}},[a("el-image",{attrs:{src:e,"preview-src-list":[e]}})],1)})):a("span",{staticStyle:{display:"inline-block"}},[e._v("无")])],2)]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.row.refundReason,expression:"props.row.refundReason"}],staticClass:"pup_card flex-column"},[a("span",[e._v("拒绝原因："+e._s(t.row.refundReason))])])])],1)]}}])}),e._v(" "),e.checkedCities.includes("退款单号")?a("el-table-column",{attrs:{label:"退款单号","min-width":"185"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"acea-row"},[a("span",{directives:[{name:"show",rawName:"v-show",value:1===t.row.type,expression:"scope.row.type === 1"}],staticClass:"iconfont icon-shipinhao mr5",staticStyle:{color:"#f6ae02"}}),e._v(" "),a("span",{staticStyle:{display:"block"},domProps:{textContent:e._s(t.row.refundOrderNo)}})])]}}],null,!1,3924775764)}):e._e(),e._v(" "),e.checkedCities.includes("订单号")?a("el-table-column",{attrs:{prop:"orderNo",label:"订单号","min-width":"180"}}):e._e(),e._v(" "),e.checkedCities.includes("用户信息")?a("el-table-column",{attrs:{prop:"userNickName",label:"用户信息","min-width":"180"}}):e._e(),e._v(" "),e.checkedCities.includes("退款金额")?a("el-table-column",{attrs:{prop:"refundPrice",label:"退款金额","min-width":"100"}}):e._e(),e._v(" "),e.checkedCities.includes("退款状态")?a("el-table-column",{attrs:{label:"退款状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[2===t.row.refundStatus||1===t.row.refundStatus?a("div",{staticClass:"refunding"},[[a("el-popover",{attrs:{trigger:"hover",placement:"left","open-delay":500}},[a("b",{staticStyle:{color:"#f124c7",cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(e._f("refundStatusFilter")(t.row.refundStatus)))]),e._v(" "),a("div",{staticClass:"pup_card flex-column"},[a("span",[e._v("退款原因："+e._s(t.row.refundReasonWap))]),e._v(" "),a("span",[e._v("备注说明："+e._s(t.row.refundReasonWapExplain))]),e._v(" "),a("span",{staticClass:"acea-row"},[e._v("\n                    退款凭证：\n                    "),t.row.refundReasonWapImg?e._l(t.row.refundReasonWapImg.split(","),(function(e,t){return a("div",{key:t,staticClass:"demo-image__preview",staticStyle:{width:"35px",height:"auto",display:"inline-block"}},[a("el-image",{attrs:{src:e,"preview-src-list":[e]}})],1)})):a("span",{staticStyle:{display:"inline-block"}},[e._v("无")])],2)]),e._v(" "),a("div",{staticClass:"pup_card flex-column"},[a("span",[e._v("拒绝原因："+e._s(t.row.refundReason))])])])]],2):a("span",[e._v(e._s(e._f("refundStatusFilter")(t.row.refundStatus)))])]}}],null,!1,3231509411)}):e._e(),e._v(" "),e.checkedCities.includes("创建时间")?a("el-table-column",{attrs:{prop:"createTime",label:"创建时间","min-width":"150"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center","render-header":e.renderHeader},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return e.onOrderDetails(t.row)}}},[e._v("订单详情")]),e._v(" "),0===t.row.refundStatus&&e.checkPermi(["merchant:refund:order:refuse"])?a("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return e.onOrderRefuse(t.row)}}},[e._v("拒绝退款")]):e._e(),e._v(" "),0===t.row.refundStatus&&e.checkPermi(["merchant:refund:order:refund"])?a("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return e.onOrderRefund(t.row)}}},[e._v("立即退款")]):e._e(),e._v(" "),e.checkPermi(["merchant:refund:order:mark"])?a("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return e.onOrderMark(t.row)}}},[e._v("订单备注")]):e._e()]}}])})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.card_select_show,expression:"card_select_show"}],staticClass:"card_abs"},[[a("div",{staticClass:"cell_ht"},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选\n        ")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.checkSave()}}},[e._v("保存")])],1),e._v(" "),a("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.checkedCities,callback:function(t){e.checkedCities=t},expression:"checkedCities"}},e._l(e.columnData,(function(t){return a("el-checkbox",{key:t,staticClass:"check_cell",attrs:{label:t}},[e._v(e._s(t))])})),1)]],2),e._v(" "),a("el-dialog",{attrs:{title:"操作记录",visible:e.dialogVisibleJI,width:"700px"},on:{"update:visible":function(t){e.dialogVisibleJI=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.LogLoading,expression:"LogLoading"}],staticStyle:{width:"100%"},attrs:{border:"",data:e.tableDataLog.data}},[a("el-table-column",{attrs:{prop:"oid",align:"center",label:"ID","min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{prop:"changeMessage",label:"操作记录",align:"center","min-width":"280"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"操作时间",align:"center","min-width":"280"}})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":e.tableFromLog.limit,"current-page":e.tableFromLog.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableDataLog.total},on:{"size-change":e.handleSizeChangeLog,"current-change":e.pageChangeLog}})],1)],1),e._v(" "),a("details-from",{ref:"orderDetail",attrs:{orderNo:e.orderNo}}),e._v(" "),e.RefuseVisible?a("el-dialog",{attrs:{title:"拒绝退款原因",visible:e.RefuseVisible,width:"500px","before-close":e.RefusehandleClose},on:{"update:visible":function(t){e.RefuseVisible=t}}},[a("z-b-parser",{attrs:{"form-id":106,"is-create":1,"edit-data":e.RefuseData},on:{submit:e.RefusehandlerSubmit,resetForm:e.resetFormRefusehand}})],1):e._e(),e._v(" "),a("el-dialog",{attrs:{title:"退款处理",visible:e.refundVisible,width:"500px","before-close":e.refundhandleClose},on:{"update:visible":function(t){e.refundVisible=t}}},[e.refundVisible?a("z-b-parser",{attrs:{"form-id":107,"is-create":1,"edit-data":e.refundData},on:{submit:e.refundhandlerSubmit,resetForm:e.resetFormRefundhandler}}):e._e()],1)],1)},s=[],r=a("f8b7"),n=a("129b"),o=(a("a78e"),a("ed08")),l=a("c4c8"),c=a("e350"),d={name:"orderRefund",components:{detailsFrom:n["a"]},data:function(){return{RefuseVisible:!1,RefuseData:{},orderNo:"",refundVisible:!1,refundData:{},dialogVisibleJI:!1,tableDataLog:{data:[],total:0},tableFromLog:{page:1,limit:10,orderNo:0},LogLoading:!1,isCreate:1,editData:null,dialogVisible:!1,tableData:{data:[],total:0},listLoading:!0,tableFrom:{refundStatus:"9",dateLimit:"",orderNo:"",refundOrderNo:"",page:1,limit:20},orderChartType:{},timeVal:[],fromList:this.$constants.fromList,selectionList:[],ids:"",orderids:"",cardLists:[],isWriteOff:Object(o["f"])(),proType:0,active:!1,card_select_show:!1,checkAll:!1,checkedCities:["退款单号","订单号","用户信息","退款金额","退款状态","创建时间"],columnData:["退款单号","订单号","用户信息","退款金额","退款状态","创建时间"],isIndeterminate:!0}},mounted:function(){this.getList(),this.getOrderStatusNum()},methods:{checkPermi:c["a"],resetFormRefundhandler:function(){this.refundVisible=!1},resetFormRefusehand:function(){this.RefuseVisible=!1},resetForm:function(e){this.dialogVisible=!1},seachList:function(){this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},RefusehandleClose:function(){this.RefuseVisible=!1},onOrderRefuse:function(e){var t=this;this.$prompt("拒绝退款",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入拒绝退款原因",inputType:"textarea",inputPlaceholder:"请输入拒绝退款原因",inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(a){var i=a.value;Object(r["k"])({reason:i,refundOrderNo:e.refundOrderNo}).then((function(){t.$message.success("操作成功"),t.getList()}))})).catch((function(){t.$message.info("取消输入")}))},RefusehandlerSubmit:function(e){var t=this;Object(r["k"])({refundOrderNo:e.refundOrderNo,reason:e.reason}).then((function(e){t.$message.success("操作成功"),t.RefuseVisible=!1,t.getList()}))},refundhandleClose:function(){this.refundVisible=!1},onOrderRefund:function(e){var t=this;this.$modalSure("退款操作吗？").then((function(){Object(r["j"])({refundOrderNo:e.refundOrderNo}).then((function(e){t.$message.success("操作成功"),t.tableFrom.page=1,t.getList()}))}))},refundhandlerSubmit:function(e){var t=this;Object(r["j"])({refundOrderNo:this.orderids}).then((function(e){t.$message.success("操作成功"),t.refundVisible=!1,t.tableFrom.page=1,t.getList()}))},onOrderDetails:function(e){this.orderNo=e.orderNo,this.$refs.orderDetail.getDetail(e.orderNo),this.$refs.orderDetail.getRefundOrderDetail(e.refundOrderNo),this.$refs.orderDetail.getOrderInvoiceList(e.orderNo),this.$refs.orderDetail.dialogVisible=!0},onOrderLog:function(e){var t=this;this.dialogVisibleJI=!0,this.LogLoading=!0,this.tableFromLog.orderNo=e,Object(r["f"])(this.tableFromLog).then((function(e){t.tableDataLog.data=e.list,t.tableDataLog.total=e.total,t.LogLoading=!1})).catch((function(){t.LogLoading=!1}))},pageChangeLog:function(e){this.tableFromLog.page=e,this.onOrderLog()},handleSizeChangeLog:function(e){this.tableFromLog.limit=e,this.onOrderLog()},handleClose:function(){this.dialogVisible=!1},onOrderMark:function(e){var t=this;this.$prompt("退款单备注",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入退款单备注",inputType:"textarea",inputValue:e.merRemark,inputPlaceholder:"请输入退款单备注",inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(a){var i=a.value;Object(r["o"])({remark:i,refundOrderNo:e.refundOrderNo}).then((function(){t.$message.success("操作成功"),t.getList()}))})).catch((function(){t.$message.info("取消输入")}))},handleSelectionChange:function(e){this.selectionList=e;var t=[];this.selectionList.map((function(e){t.push(e.orderNo)})),this.ids=t.join(",")},selectChange:function(e){this.timeVal=[],this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},onchangeTime:function(e){this.timeVal=e,this.tableFrom.dateLimit=e?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList(),this.getOrderStatusNum()},getList:function(){var e=this;this.listLoading=!0,Object(r["n"])(this.tableFrom).then((function(t){e.tableData.data=t.list||[],e.tableData.total=t.total,e.listLoading=!1,e.checkedCities=e.$cache.local.has("order_stroge")?e.$cache.local.getJSON("order_stroge"):e.checkedCities})).catch((function(){e.listLoading=!1}))},getOrderStatusNum:function(){var e=this;Object(r["q"])({dateLimit:this.tableFrom.dateLimit}).then((function(t){e.orderChartType=t}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},exports:function(){var e={dateLimit:this.tableFrom.dateLimit,orderNo:this.tableFrom.orderNo,refundStatus:this.tableFrom.status,type:this.tableFrom.type};Object(l["u"])(e).then((function(e){window.open(e.fileName)}))},renderHeader:function(e){var t=this;return e("p",[e("span",{style:"padding-right:5px;"},["操作"]),e("i",{class:"el-icon-setting",on:{click:function(){return t.handleAddItem()}}})])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCheckAllChange:function(e){this.checkedCities=e?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.columnData.length,this.isIndeterminate=t>0&&t<this.columnData.length},checkSave:function(){this.$set(this,"card_select_show",!1),this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("order_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)},onOrderPrint:function(e){var t=this;Object(r["h"])(e.orderNo).then((function(e){t.$modal.msgSuccess("打印成功")})).catch((function(e){t.$modal.msgError(e.message)}))}}},u=d,h=(a("1ec5"),a("2877")),m=Object(h["a"])(u,i,s,!1,null,"6bd67577",null);t["default"]=m.exports}}]);