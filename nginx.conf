#工作进程数  1 ,不要超过计算机的核数,四核配置4,八核配置8
worker_processes  1;

#工作连接数,也就是线程,一个进程有1024个线程,
events {
    worker_connections  1024;
}

#http请求配置
http {
    include mime.types;
    default_type  application/octet-stream;

	#sendfile为发送文件,要on开启
    sendfile        on;

	#keepalive_timeout超时时间
    keepalive_timeout  65;

	server {
	    #监听的端口,这里为80
		listen       80;
		#server_name就是域名,
		server_name  admin.mall.sccmall.com;

    location /admin/ {
      proxy_http_version 1.1;
      proxy_set_header Connection “”;
      proxy_pass http://192.168.0.1:8080;
    }

		#location域名代理地址
	    # / 代表所有请求路径
		location / {
			root /opt/nginx/html;
			index  index.html;
			try_files $uri $uri/ /index.html;
		}
	}
}

