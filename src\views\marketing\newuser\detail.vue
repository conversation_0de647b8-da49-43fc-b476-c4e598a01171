<template>
  <div class="app-container">
    <div class="content-wrapper">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 基本内容 -->
        <el-tab-pane label="基本内容" name="basic">
          <basic-info :activity-id="activityId" :activity-info="activityInfo" @activity-loaded="handleActivityLoaded" />
        </el-tab-pane>

        <!-- 活动商品 -->
        <el-tab-pane label="活动商品" name="products">
          <product-list :activity-id="activityId" :new-price="activityInfo.newPrice" />
        </el-tab-pane>

        <!-- 参与商户 -->
        <el-tab-pane label="参与商户" name="merchants">
          <merchant-list
            :merchant-list="merchantList"
            :loading="merchantLoading"
            :pagination="merchantPagination"
            :merchant-store-count="merchantStoreCount"
            :all-stores="allStores"
            :expanded-rows="expandedRows"
            @search="handleMerchantSearch"
            @toggle-expand="toggleMerchantExpand"
            @size-change="handleMerchantSizeChange"
            @page-change="handleMerchantPageChange"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { discountInfoApi, getActivityMerchantListApi, getActivityInfoApi } from '@/api/discount';
import BasicInfo from './components/BasicInfo.vue';
import ProductList from './components/ProductList.vue';
import MerchantList from './components/MerchantList.vue';

export default {
  name: 'DiscountDetail',
  components: {
    BasicInfo,
    ProductList,
    MerchantList,
  },
  data() {
    return {
      activeTab: 'basic',
      activityId: null,
      activityInfo: {}, // 新增：存储活动详情
      merchantList: [],
      merchantLoading: false,
      merchantPagination: {
        page: 1,
        limit: 20,
        total: 0,
      },
      merchantStoreCount: 0,
      expandedRows: [],
      allStores: [],
      filteredMerchantList: [],
      merchantSearchForm: {
        merchantName: '',
        storeName: '',
      },
    };
  },
  mounted() {
    this.activityId = this.$route.params.id;
    this.getActivityInfo(); // 新增：获取活动详情
    this.getMerchantList();
  },
  methods: {
    /**
     * 获取活动详情
     */
    async getActivityInfo() {
      if (!this.activityId) return;
      try {
        this.activityInfo = await getActivityInfoApi(this.activityId);
        console.log('获取活动详情成功:', this.activityInfo);
      } catch (error) {
        console.error('获取活动详情失败:', error);
        this.$message.error('获取活动详情失败');
      }
    },

    /**
     * 处理活动详情加载完成事件
     * @param {Object} activityInfo - 活动详情
     */
    handleActivityLoaded(activityInfo) {
      this.activityInfo = activityInfo;
    },

    /**
     * 获取参与商户列表
     */
    async getMerchantList() {
      this.merchantLoading = true;
      try {
        const params = {
          activityId: this.activityId,
          page: this.merchantPagination.page,
          limit: this.merchantPagination.limit,
          ...this.merchantSearchForm,
        };

        const data = await getActivityMerchantListApi(params);

        this.merchantList = data.list.map((item) => ({
          id: item.merchantId,
          merchantId: item.merchantId,
          merchantName: item.merchantName,
          stores: item.stores || [],
        }));

        this.allStores = [];
        data.list.forEach((merchant) => {
          if (merchant.stores && merchant.stores.length > 0) {
            merchant.stores.forEach((store) => {
              this.allStores.push({
                id: store.storeId,
                merchantId: merchant.merchantId,
                name: store.storeName,
              });
            });
          }
        });

        this.merchantPagination.page = data.page;
        // this.merchantPagination.limit = data.limit;
        this.merchantPagination.total = data.total;
        this.merchantStoreCount = this.allStores.length;

        console.log('获取参与商户列表成功:', data);
      } catch (error) {
        console.error('获取参与商户列表失败:', error);
        this.$message.error('获取参与商户列表失败');
      } finally {
        this.merchantLoading = false;
      }
    },

    /**
     * 商户搜索过滤
     * @param {Object} form - 搜索表单数据
     */
    handleMerchantSearch(form) {
      this.merchantSearchForm = form;
      this.merchantPagination.page = 1;
      this.getMerchantList();
    },

    /**
     * 切换商户展开状态
     * @param {Object} row - 商户行数据
     */
    toggleMerchantExpand(row) {
      const index = this.expandedRows.indexOf(row.id);
      if (index > -1) {
        this.expandedRows.splice(index, 1);
      } else {
        this.expandedRows.push(row.id);
      }
    },

    /**
     * 检查行是否展开
     * @param {Number} rowId - 行ID
     * @returns {Boolean} 是否展开
     */
    isRowExpanded(rowId) {
      return this.expandedRows.includes(rowId);
    },

    /**
     * 根据商户ID获取门店列表
     * @param {Number} merchantId - 商户ID
     * @returns {Array} 门店列表
     */
    getStoresByMerchantId(merchantId) {
      return this.allStores.filter((store) => store.merchantId === merchantId);
    },

    /**
     * 处理商户分页大小变化
     * @param {Number} val - 新的分页大小
     */
    handleMerchantSizeChange(val) {
      this.merchantPagination.limit = val;
      this.merchantPagination.page = 1;
      this.getMerchantList();
    },

    /**
     * 处理商户页码变化
     * @param {Number} val - 新的页码
     */
    handleMerchantPageChange(val) {
      this.merchantPagination.page = val;
      this.getMerchantList();
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.content-wrapper {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
}

.detail-tabs {
  .el-tab-pane {
    padding-top: 20px;
  }
}
</style>
