<template>
  <div class="inventory-warning">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="导入" name="import"></el-tab-pane>
      <el-tab-pane label="导出" name="export"></el-tab-pane>
    </el-tabs>

    <el-form :inline="true" :model="searchForm" class="search-form" size="small">
      <el-form-item label="选择时间：">
        <el-button-group>
          <el-button :type="searchForm.timeType === '' ? 'primary' : 'default'" @click="setTimeType('')"
            >全部</el-button
          >
          <el-button :type="searchForm.timeType === 'today' ? 'primary' : 'default'" @click="setTimeType('today')"
            >今天</el-button
          >
          <el-button
            :type="searchForm.timeType === 'yesterday' ? 'primary' : 'default'"
            @click="setTimeType('yesterday')"
            >昨天</el-button
          >
          <el-button :type="searchForm.timeType === '7days' ? 'primary' : 'default'" @click="setTimeType('7days')"
            >最近7天</el-button
          >
          <el-button :type="searchForm.timeType === '30days' ? 'primary' : 'default'" @click="setTimeType('30days')"
            >最近30天</el-button
          >
          <el-button :type="searchForm.timeType === 'month' ? 'primary' : 'default'" @click="setTimeType('month')"
            >本月</el-button
          >
          <el-button :type="searchForm.timeType === 'year' ? 'primary' : 'default'" @click="setTimeType('year')"
            >本年</el-button
          >
        </el-button-group>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          style="width: 280px"
        />
      </el-form-item>
    </el-form>
    <el-form :inline="true" :model="searchForm" class="search-form" size="small" style="margin-top: 10px">
      <el-form-item label="导入文件名称：">
        <el-input v-model="searchForm.fileName" placeholder="请输入文件名称" clearable />
      </el-form-item>
      <el-form-item label="操作名称：">
        <el-input v-model="searchForm.operName" placeholder="请输入操作名称" clearable />
      </el-form-item>
    </el-form>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px" size="small" v-loading="loading" class="mt20">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="fileName" label="文件名称"></el-table-column>
      <el-table-column prop="operName" label="操作名称"></el-table-column>
      <el-table-column prop="taskStatus" label="任务状态"></el-table-column>
      <el-table-column prop="dataStatus" label="数据状态"></el-table-column>
      <el-table-column prop="downSpeed" label="下载进度"></el-table-column>
      <el-table-column prop="operTime" label="操作日期"></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDownload(scope.row)">下载文件</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="block">
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { getFileSpeedTaskList } from '@/api/fileSpeedTask';
import { export_json_to_excel } from '@/vendor/Export2Excel';
import { parseTime } from '@/utils/parsing';

// 防抖函数
function debounce(fn, delay) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

// 日期格式化为 yyyy-MM-dd（本地时区）
function formatDateToYMD(date) {
  const y = date.getFullYear();
  const m = (date.getMonth() + 1).toString().padStart(2, '0');
  const d = date.getDate().toString().padStart(2, '0');
  return `${y}-${m}-${d}`;
}

export default {
  name: 'FileSpeedTaskListList',
  data() {
    return {
      activeTab: 'import',
      searchForm: {
        status: '',
        timeType: '',
        dateRange: [],
        fileName: '',
        operName: '',
      },
      tableData: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      loading: false,
    };
  },
  created() {
    this.getList = debounce(this.getList, 400);
    this.getList();
  },
  watch: {
    'searchForm.fileName': function () {
      this.currentPage = 1;
      this.getList();
    },
    'searchForm.operName': function () {
      this.currentPage = 1;
      this.getList();
    },
    'searchForm.dateRange': {
      handler() {
        this.currentPage = 1;
        this.getList();
      },
      deep: true,
    },
    'searchForm.timeType': function () {
      this.currentPage = 1;
      this.getList();
    },
    activeTab() {
      this.currentPage = 1;
      this.getList();
    },
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      const params = {
        fileName: this.searchForm.fileName,
        operName: this.searchForm.operName,
        page: this.currentPage,
        limit: this.pageSize,
        operType: this.activeTab === 'export' ? '导出' : '导入',
      };
      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        params.startTime = this.searchForm.dateRange[0];
        params.endTime = this.searchForm.dateRange[1];
      }
      getFileSpeedTaskList(params).then((res) => {
        console.log('getFileSpeedTaskList', res);
        this.tableData = res.list;
        this.total = res.total || 0;
        this.loading = false;
      });
    },
    handleNoRemind(row) {
      updateRemindStatus(row.id, 1).then(() => {
        this.$message.success(`ID:${row.id} 已设置为不再提示`);
        this.getList();
      });
    },
    handleRemindAgain(row) {
      updateRemindStatus(row.id, 0).then(() => {
        this.$message.success(`ID:${row.id} 已重新提醒`);
        this.getList();
      });
    },
    handleTabClick(tab) {
      this.currentPage = 1;
      this.getList();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.getList();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.getList();
    },
    setTimeType(type) {
      this.searchForm.timeType = type;
      const today = new Date();
      let start, end;
      let startStr, endStr;
      switch (type) {
        case 'today':
          start = new Date();
          start.setHours(0, 0, 0, 0);
          end = new Date();
          end.setHours(23, 59, 59, 999);
          startStr = formatDateToYMD(start);
          endStr = formatDateToYMD(end);
          this.searchForm.dateRange = [startStr, endStr];
          break;
        case 'yesterday':
          start = new Date();
          start.setDate(start.getDate() - 1);
          start.setHours(0, 0, 0, 0);
          end = new Date();
          end.setDate(end.getDate() - 1);
          end.setHours(23, 59, 59, 999);
          startStr = formatDateToYMD(start);
          endStr = formatDateToYMD(end);
          this.searchForm.dateRange = [startStr, endStr];
          break;
        case '7days':
          end = new Date();
          end.setHours(23, 59, 59, 999);
          start = new Date();
          start.setDate(start.getDate() - 6);
          start.setHours(0, 0, 0, 0);
          startStr = formatDateToYMD(start);
          endStr = formatDateToYMD(end);
          this.searchForm.dateRange = [startStr, endStr];
          break;
        case '30days':
          end = new Date();
          end.setHours(23, 59, 59, 999);
          start = new Date();
          start.setDate(start.getDate() - 29);
          start.setHours(0, 0, 0, 0);
          startStr = formatDateToYMD(start);
          endStr = formatDateToYMD(end);
          this.searchForm.dateRange = [startStr, endStr];
          break;
        case 'month':
          start = new Date(today.getFullYear(), today.getMonth(), 1, 0, 0, 0, 0);
          end = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59, 999);
          startStr = formatDateToYMD(start);
          endStr = formatDateToYMD(end);
          this.searchForm.dateRange = [startStr, endStr];
          break;
        case 'year':
          start = new Date(today.getFullYear(), 0, 1, 0, 0, 0, 0);
          end = new Date(today.getFullYear(), 11, 31, 23, 59, 59, 999);
          startStr = formatDateToYMD(start);
          endStr = formatDateToYMD(end);
          this.searchForm.dateRange = [startStr, endStr];
          break;
        default:
          this.searchForm.dateRange = [];
      }
    },
    handleDownload(row) {
      // 引入SettingMer
      import('@/utils/settingMer')
        .then(({ default: SettingMer }) => {
          console.log('返回文件下载地址', row.filePath);
          if (row.filePath) {
            // 拼接主请求路径，使用正确的静态文件访问前缀
            const downloadUrl = SettingMer.httpUrl + '/' + row.filePath;
            window.open(downloadUrl);
            this.$message.success('下载成功，文件开始下载');
          } else {
            this.$message.error('下载失败，未获取到下载链接');
          }
        })
        .catch((e) => {
          console.log(e);
          this.$message.error('下载失败');
          this.exportLoading = false;
          this.$modal.closeLoading();
        });
    },
  },
};
</script>

<style scoped lang="scss">
.inventory-warning {
  background: #fff;
  padding: 24px;
}

.search-form {
  margin: 20px 0 0 0;
}

.mt20 {
  margin-top: 20px;
}

.block {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 覆盖el-tabs默认边框
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

// 为tabs头部添加底部边框线
::v-deep .el-tabs__header {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e7ed;
}
</style>
