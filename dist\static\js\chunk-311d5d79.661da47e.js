(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-311d5d79"],{"29c1":function(e,r,t){"use strict";t.r(r);var i=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"divBox"},[t("el-card",{staticClass:"box-card"},[t("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"150px"}},[t("el-form-item",{attrs:{label:"优惠劵名称",prop:"name"}},[t("el-input",{staticStyle:{width:"350px"},attrs:{placeholder:"请输入优惠券名称",disabled:!!e.$route.params.edit},model:{value:e.ruleForm.name,callback:function(r){e.$set(e.ruleForm,"name",r)},expression:"ruleForm.name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"优惠劵类别"}},[t("el-radio-group",{attrs:{disabled:!!e.$route.params.edit},model:{value:e.ruleForm.category,callback:function(r){e.$set(e.ruleForm,"category",r)},expression:"ruleForm.category"}},[t("el-radio",{attrs:{label:1}},[e._v("商家券")]),e._v(" "),t("el-radio",{attrs:{label:2}},[e._v("商品券")])],1)],1),e._v(" "),2===e.ruleForm.category?t("el-form-item",{attrs:{label:"商品：",prop:"checked"}},[t("div",{staticClass:"acea-row"},[e.ruleForm.checked.length?e._l(e.ruleForm.checked,(function(r,i){return t("div",{key:i,staticClass:"pictrue"},[t("img",{attrs:{src:r.image}}),e._v(" "),t("i",{staticClass:"el-icon-error btndel",on:{click:function(r){return e.handleRemove(i)}}})])})):e._e(),e._v(" "),t("div",{staticClass:"upLoadPicBox",on:{click:e.changeGood}},[t("div",{staticClass:"upLoad"},[t("i",{staticClass:"el-icon-camera cameraIconfont"})])])],2)]):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"优惠券面值",prop:"money"}},[t("el-input-number",{attrs:{min:1,max:9999,label:"优惠券面值",disabled:!!e.$route.params.edit},model:{value:e.ruleForm.money,callback:function(r){e.$set(e.ruleForm,"money",r)},expression:"ruleForm.money"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"使用门槛"}},[t("el-radio-group",{attrs:{disabled:!!e.$route.params.edit},model:{value:e.threshold,callback:function(r){e.threshold=r},expression:"threshold"}},[t("el-radio",{attrs:{label:!1}},[e._v("无门槛")]),e._v(" "),t("el-radio",{attrs:{label:!0}},[e._v("有门槛")])],1)],1),e._v(" "),e.threshold?t("el-form-item",{attrs:{label:"优惠券最低消费",prop:"minPrice"}},[t("el-input-number",{attrs:{step:1,"step-strictly":"",min:1,label:"描述文字",disabled:!!e.$route.params.edit},model:{value:e.ruleForm.minPrice,callback:function(r){e.$set(e.ruleForm,"minPrice",r)},expression:"ruleForm.minPrice"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"使用有效期"}},[t("el-radio-group",{attrs:{disabled:!!e.$route.params.edit},model:{value:e.ruleForm.isFixedTime,callback:function(r){e.$set(e.ruleForm,"isFixedTime",r)},expression:"ruleForm.isFixedTime"}},[t("el-radio",{attrs:{label:!1}},[e._v("天数")]),e._v(" "),t("el-radio",{attrs:{label:!0}},[e._v("时间段")])],1)],1),e._v(" "),e.ruleForm.isFixedTime?e._e():t("el-form-item",{attrs:{label:"使用有效期限（天）",prop:"day"}},[t("el-input-number",{attrs:{min:0,max:999,label:"使用有效期限（天）",disabled:!!e.$route.params.edit},model:{value:e.ruleForm.day,callback:function(r){e.$set(e.ruleForm,"day",r)},expression:"ruleForm.day"}})],1),e._v(" "),e.ruleForm.isFixedTime?t("el-form-item",{attrs:{label:"使用有效期限",prop:"resource"}},[t("el-date-picker",{staticStyle:{width:"550px"},attrs:{disabled:!!e.$route.params.edit,type:"datetimerange","range-separator":"至","value-format":"yyyy-MM-dd HH:mm:ss","start-placeholder":"开始日期","picker-options":e.pickerOptions,"end-placeholder":"结束日期"},model:{value:e.termTime,callback:function(r){e.termTime=r},expression:"termTime"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"领取是否限时",prop:"isTimeReceive"}},[t("el-radio-group",{attrs:{disabled:!!e.$route.params.edit},model:{value:e.ruleForm.isTimeReceive,callback:function(r){e.$set(e.ruleForm,"isTimeReceive",r)},expression:"ruleForm.isTimeReceive"}},[t("el-radio",{attrs:{label:!0}},[e._v("限时")]),e._v(" "),t("el-radio",{attrs:{label:!1}},[e._v("不限时")])],1)],1),e._v(" "),e.ruleForm.isTimeReceive?t("el-form-item",{attrs:{label:"领取时间"}},[t("el-date-picker",{staticStyle:{width:"550px"},attrs:{disabled:!!e.$route.params.edit,type:"datetimerange","range-separator":"至","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":e.pickerOptions,"start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{blur:e.handleTimestamp},model:{value:e.isForeverTime,callback:function(r){e.isForeverTime=r},expression:"isForeverTime"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"领取方式",prop:"receiveType"}},[t("el-radio-group",{attrs:{disabled:!!e.$route.params.edit},model:{value:e.ruleForm.receiveType,callback:function(r){e.$set(e.ruleForm,"receiveType",r)},expression:"ruleForm.receiveType"}},[t("el-radio",{attrs:{label:1}},[e._v("手动领取")]),e._v(" "),t("el-radio",{attrs:{label:2}},[e._v("商品买赠券")])],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"是否限量",prop:"isLimited"}},[t("el-radio-group",{attrs:{disabled:!!e.$route.params.edit},model:{value:e.ruleForm.isLimited,callback:function(r){e.$set(e.ruleForm,"isLimited",r)},expression:"ruleForm.isLimited"}},[t("el-radio",{attrs:{label:!0}},[e._v("限量")]),e._v(" "),t("el-radio",{attrs:{label:!1}},[e._v("不限量")])],1)],1),e._v(" "),e.ruleForm.isLimited?t("el-form-item",{attrs:{label:"发布数量",prop:"total"}},[t("el-input-number",{attrs:{min:1,max:9999,disabled:!!e.$route.params.edit},model:{value:e.ruleForm.total,callback:function(r){e.$set(e.ruleForm,"total",r)},expression:"ruleForm.total"}})],1):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"排序",prop:"sort"}},[t("el-input-number",{attrs:{min:1,max:9999,label:"排序",disabled:!!e.$route.params.edit},model:{value:e.ruleForm.sort,callback:function(r){e.$set(e.ruleForm,"sort",r)},expression:"ruleForm.sort"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{attrs:{disabled:!!e.$route.params.edit},model:{value:e.ruleForm.status,callback:function(r){e.$set(e.ruleForm,"status",r)},expression:"ruleForm.status"}},[t("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),t("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),t("el-form-item",[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:coupon:save","merchant:coupon:product:join:edit"],expression:"['merchant:coupon:save', 'merchant:coupon:product:join:edit']"}],attrs:{size:"mini",type:"primary",loading:e.loading},on:{click:function(r){return e.submitForm("ruleForm")}}},[e._v("立即创建")])],1)],1)],1)],1)},a=[],s=t("c4c8"),o=t("61f7"),l={name:"creatCoupon",data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()-864e5||e.getTime()>Date.now()+5184e7}},loading:!1,threshold:!1,termTime:[],props2:{children:"child",label:"name",value:"id",checkStrictly:!0,emitPath:!1},couponType:0,term:"termday",ruleForm:{category:1,isFixedTime:!1,name:"",money:1,minPrice:1,day:null,isTimeReceive:!1,productIds:"",receiveType:2,isLimited:!1,useStartTime:"",useEndTime:"",receiveStartTime:"",receiveEndTime:"",sort:0,total:1,status:!1,checked:[]},isForeverTime:[],rules:{name:[{required:!0,message:"请输入优惠券名称",trigger:"blur"}],day:[{required:!0,message:"请输入使用有效期限（天）",trigger:"blur"}],money:[{required:!0,message:"请输入优惠券面值",trigger:"blur"}],productIds:[{required:!0,message:"请选择品类",trigger:"change"}],checked:[{required:!0,message:"请至少选择一个商品",trigger:"change",type:"array"}],isForeverTime:[{required:!0,message:"请选择领取时间",trigger:"change",type:"array"}],total:[{required:!0,message:"请输入发布数量",trigger:"blur"}],minPrice:[{required:!0,message:"请输入最低消费",trigger:"blur"}]}}},mounted:function(){this.$route.params.id&&(this.setTagsViewTitle(),this.getInfo())},methods:{setTagsViewTitle:function(){var e=this.$route.params.edit?"优惠券编辑":"优惠券添加",r=Object.assign({},this.tempRoute,{title:"".concat(e,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",r)},handleTimestamp:function(){},getInfo:function(){var e=this;this.loading=!0,Object(s["j"])(this.$route.params.id).then((function(r){var t=r;e.ruleForm={category:t.category,isFixedTime:t.isFixedTime,isTimeReceive:!!t.receiveEndTime,name:t.name,money:t.money,minPrice:t.minPrice,day:t.day,receiveType:t.receiveType,isLimited:t.isLimited,sort:t.sort,total:t.total,status:t.status,productIds:Number(t.productIds),checked:r.productList||[]},0==t.minPrice?e.threshold=!1:e.threshold=!0,t.isTimeReceive?e.isForeverTime=[t.receiveStartTime,t.receiveEndTime]:e.isForeverTime=[],t.isFixedTime&&t.useStartTime&&t.useEndTime?e.termTime=[t.useStartTime,t.useEndTime]:e.termTime=[],e.loading=!1})).catch((function(r){e.loading=!1}))},handleRemove:function(e){this.ruleForm.checked.splice(e,1)},changeGood:function(){var e=this;this.$modalGoodList((function(r){e.ruleForm.checked=r}),"many",e.ruleForm.checked)},save:function(e){var r=this;return this.ruleForm.isFixedTime&&!this.termTime||this.ruleForm.isFixedTime&&!this.termTime.length?this.$message.warning("请选择使用有效期限"):this.ruleForm.isTimeReceive&&!this.isForeverTime||this.ruleForm.isTimeReceive&&!this.isForeverTime.length?this.$message.warning("请选择请选择领取时间"):(this.threshold||(this.ruleForm.minPrice=0),this.ruleForm.isLimited||(this.ruleForm.total=0),this.ruleForm.isFixedTime&&this.termTime.length?(this.ruleForm.useStartTime=this.termTime[0],this.ruleForm.day=null):this.ruleForm.useStartTime="",this.ruleForm.isFixedTime&&this.termTime.length?(this.ruleForm.useEndTime=this.termTime[1],this.ruleForm.day=null):this.ruleForm.useEndTime="",this.ruleForm.isTimeReceive&&this.isForeverTime.length?this.ruleForm.receiveStartTime=this.isForeverTime[0]:this.ruleForm.receiveStartTime="",this.ruleForm.isTimeReceive&&this.isForeverTime.length?this.ruleForm.receiveEndTime=this.isForeverTime[1]:this.ruleForm.receiveEndTime="",void this.$refs[e].validate((function(e){if(!e)return r.loading=!1,!1;r.loading=!0,Object(s["m"])(r.ruleForm).then((function(){r.$message.success("编辑成功"),r.loading=!1,setTimeout((function(){r.$router.push({path:"/coupon/list"})}),200),r.closeSelectedTag()})).catch((function(){r.loading=!1}))})))},submitForm:Object(o["a"])((function(e){var r=this;2===this.ruleForm.category&&(this.ruleForm.productIds=this.ruleForm.checked.map((function(e){return e.id})).join(",")),1===this.ruleForm.category&&(this.ruleForm.productIds=""),this.$route.params.edit?this.$refs[e].validate((function(e){if(!e)return r.loading=!1,!1;r.loading=!0,Object(s["l"])({id:r.$route.params.id,productIds:r.ruleForm.productIds}).then((function(){r.$message.success("新增成功"),r.loading=!1,setTimeout((function(){r.$router.push({path:"/coupon/list"})}),200),r.closeSelectedTag()})).catch((function(){r.loading=!1}))})):this.save(e)})),closeSelectedTag:function(){var e=this;e.$store.dispatch("tagsView/delView",e.$route).then((function(e){e.visitedViews}))}}},m=l,u=(t("b124"),t("2877")),n=Object(u["a"])(m,i,a,!1,null,"26fe535e",null);r["default"]=n.exports},b124:function(e,r,t){"use strict";t("c5bf")},c5bf:function(e,r,t){}}]);