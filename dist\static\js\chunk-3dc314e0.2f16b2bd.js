(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3dc314e0"],{"4ab9":function(e,t,i){},a6ed:function(e,t,i){"use strict";i("4ab9")},adb8:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"components-container"},[i("category-list",{attrs:{biztype:e.constants.categoryType[0]}})],1)},n=[],r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.selectModel?[i("el-tree",{ref:"tree",attrs:{data:e.treeList,"show-checkbox":"","node-key":"id","default-checked-keys":e.select<PERSON>ode<PERSON>KeysNew,props:e.treeProps},on:{check:e.getCurrentNode}})]:[i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:category:add"],expression:"['merchant:product:category:add']"}],attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handleAddMenu({id:0,name:"顶层目录"})}}},[e._v("新增"+e._s(e.biztype.name))])],1),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:e.dataList,size:"mini","highlight-current-row":"","row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"}}},[i("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.name)+" | "+e._s(t.row.id)+" ")]}}])}),e._v(" "),e.selectModel?e._e():[i("el-table-column",{attrs:{label:"分类图标","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"demo-image__preview"},[t.row.icon?i("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.icon,"preview-src-list":[t.row.icon]}}):i("img",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.defaultImg,alt:""}})],1)]}}],null,!1,2354360158)}),e._v(" "),i("el-table-column",{attrs:{label:"排序",prop:"sort","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{label:"状态","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["admin:category:update:status"])?[i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"显示","inactive-text":"隐藏"},on:{change:function(i){return e.onchangeIsShow(t.row)}},model:{value:t.row.isShow,callback:function(i){e.$set(t.row,"isShow",i)},expression:"scope.row.isShow"}})]:void 0}}],null,!0)}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"200",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.pid?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleAddMenu(t.row)}}},[e._v("添加子目录")]):e._e(),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:category:update"],expression:"['merchant:product:category:update']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleEditMenu(t.row)}}},[e._v("编辑")]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:category:delete"],expression:"['merchant:product:category:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.handleDelMenu(t.row)}}},[e._v("删除")])]}}],null,!1,3109340136)})]],2)],1)],1)],e._v(" "),i("el-dialog",{attrs:{title:0===e.editDialogConfig.isCreate?"创建"+e.biztype.name:"编辑"+e.biztype.name,visible:e.editDialogConfig.visible,"destroy-on-close":"","close-on-click-modal":!1},on:{"update:visible":function(t){return e.$set(e.editDialogConfig,"visible",t)}}},[e.editDialogConfig.visible?i("edit",{attrs:{prent:e.editDialogConfig.prent,"is-create":e.editDialogConfig.isCreate,"edit-data":e.editDialogConfig.data,biztype:e.editDialogConfig.biztype,"all-tree-list":e.dataList},on:{hideEditDialog:e.hideEditDialog}}):e._e()],1)],2)},o=[],l=i("c4c8"),s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-tree",{attrs:{data:e.ddd,props:e.defaultProps},on:{"node-click":e.handleNodeClick}})],1)},c=[],d=i("651a"),u={props:{id:{type:Number,required:!0}},data:function(){return{defaultProps:{children:"children",label:"label"},ddd:[{label:"一级 1",children:[{label:"二级 1-1",children:[{label:"三级 1-1-1"}]}]},{label:"一级 2",children:[{label:"二级 2-1",children:[{label:"三级 2-1-1"}]},{label:"二级 2-2",children:[{label:"三级 2-2-1"}]}]},{label:"一级 3",children:[{label:"二级 3-1",children:[{label:"三级 3-1-1"}]},{label:"二级 3-2",children:[{label:"三级 3-2-1"}]}]}],dataList:{page:0,limit:0,totalPage:0,total:0,list:[]}}},mounted:function(){this.handlerGetTreeList(this.id)},methods:{handlerGetTreeList:function(e){var t=this;e?d["d"]({pid:e}).then((function(e){t.dataList=e})):this.$message.error("当前数据id不正确")},handleNodeClick:function(e){console.log("data:",e)}}},h=u,p=i("2877"),m=Object(p["a"])(h,s,c,!1,null,"2557cb1b",null),f=m.exports,g=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{ref:"editPram",attrs:{model:e.editPram,"label-width":"130px"}},[i("el-form-item",{attrs:{label:"分类名称",prop:"name",rules:[{required:!0,message:"请输入分类名称",trigger:["blur","change"]}]}},[i("el-input",{attrs:{maxlength:1===e.biztype.value?8:20,placeholder:"分类名称"},model:{value:e.editPram.name,callback:function(t){e.$set(e.editPram,"name",t)},expression:"editPram.name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"父级"}},[i("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{disabled:1===e.isCreate&&0===e.editPram.pid,options:e.allTreeList,props:e.categoryProps,clearable:""},on:{change:e.change},model:{value:e.editPram.pid,callback:function(t){e.$set(e.editPram,"pid",t)},expression:"editPram.pid"}})],1),e._v(" "),5===e.biztype.value?i("el-form-item",{attrs:{label:"菜单图标"}},[i("el-input",{attrs:{placeholder:"请选择菜单图标"},model:{value:e.editPram.icon,callback:function(t){e.$set(e.editPram,"icon",t)},expression:"editPram.icon"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-circle-plus-outline"},on:{click:e.addIcon},slot:"append"})],1)],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"分类图标(180*180)"}},[i("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("1")}}},[e.editPram.icon?i("div",{staticClass:"pictrue"},[i("img",{attrs:{src:e.editPram.icon}})]):i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])]),e._v(" "),i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{min:1,max:9999},model:{value:e.editPram.sort,callback:function(t){e.$set(e.editPram,"sort",t)},expression:"editPram.sort"}})],1),e._v(" "),1!==e.biztype.value&&3!==e.biztype.value&&5!==e.biztype.value?i("el-form-item",{attrs:{label:"扩展字段"}},[i("el-input",{attrs:{type:"textarea",placeholder:"扩展字段"},model:{value:e.editPram.extra,callback:function(t){e.$set(e.editPram,"extra",t)},expression:"editPram.extra"}})],1):e._e(),e._v(" "),i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:category:update"],expression:"['merchant:product:category:update']"}],attrs:{type:"primary",loading:e.loadingBtn},on:{click:function(t){return e.handlerSubmit("editPram")}}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)],1)},b=[],v=i("fca7"),y={props:{prent:{type:Object,required:!0},isCreate:{type:Number,default:0},editData:{type:Object},biztype:{type:Object,required:!0},allTreeList:{type:Array}},data:function(){return{loadingBtn:!1,constants:this.$constants,editPram:{icon:null,name:null,pid:0,sort:0,id:0},categoryProps:{value:"id",label:"name",children:"childList",expandTrigger:"hover",checkStrictly:!0,emitPath:!1},parentOptions:[]}},mounted:function(){this.initEditData()},methods:{change:function(){},addIcon:function(){var e=this;e.$modalIcon((function(t){e.editPram.extra=t}))},modalPicTap:function(e){var t=this;this.$modalUpload((function(e){e&&(t.editPram.icon=e[0].sattDir)}),e,"store")},close:function(){this.$emit("hideEditDialog")},initEditData:function(){var e=this.editData,t=e.icon,i=e.name,a=e.pid,n=e.sort,r=e.id;1===this.isCreate?(this.editPram.icon=t,this.editPram.name=i,this.editPram.pid=a,this.editPram.sort=n,this.editPram.id=r):this.editPram.pid=this.prent.id},handlerSubmit:function(e){var t=this;this.$refs[e].validate((function(e){e&&t.handlerSaveOrUpdate(0===t.isCreate)}))},handlerSaveOrUpdate:function(e){var t=this;e?(this.loadingBtn=!0,l["w"](this.editPram).then((function(e){t.$emit("hideEditDialog"),t.$message.success("创建目录成功"),t.$store.commit("product/SET_MerProductClassify",[]),t.loadingBtn=!1})).catch((function(){t.loadingBtn=!1}))):(this.loadingBtn=!0,l["A"](this.editPram).then((function(e){t.$emit("hideEditDialog"),t.$message.success("更新目录成功"),t.$store.commit("product/SET_MerProductClassify",[]),t.loadingBtn=!1})).catch((function(){t.loadingBtn=!1})))}}},P=y,w=Object(p["a"])(P,g,b,!1,null,"2f0c4ec9",null),C=w.exports,_=i("e350");function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function x(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,a)}return i}function S(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?x(Object(i),!0).forEach((function(t){D(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):x(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function D(e,t,i){return t=$(t),t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function $(e){var t=L(e,"string");return"symbol"===k(t)?t:String(t)}function L(e,t){if("object"!==k(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var a=i.call(e,t||"default");if("object"!==k(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var N={components:{info:f,edit:C},props:{biztype:{type:Object,default:{value:-1},validator:function(e){return e.value>0}},pid:{type:Number,default:0,validator:function(e){return e>=0}},selectModel:{type:Boolean,default:!1},selectModelKeys:{type:Array},rowSelect:{}},data:function(){return{listLoading:!1,selectModelKeysNew:this.selectModelKeys,loading:!1,constants:this.$constants,treeProps:{label:"name",children:"child"},multipleSelection:[],editDialogConfig:{visible:!1,isCreate:0,prent:{},data:{},biztype:this.biztype},dataList:[],treeList:[],listPram:{pid:this.pid,type:this.biztype.value,isShow:!1,name:"",page:this.$constants.page.page,limit:this.$constants.page.limit[0]},viewInfoConfig:{data:null,visible:!1},defaultImg:i("cf6b")}},mounted:function(){this.handlerGetTreeList()},methods:{checkPermi:_["a"],onchangeIsShow:function(e){var t=this;l["z"](e.id).then((function(){t.$message.success("修改成功"),t.handlerGetTreeList(),t.$store.commit("product/SET_MerProductClassify",[])}))},handleEditMenu:function(e){this.editDialogConfig.isCreate=1,this.editDialogConfig.data=e,this.editDialogConfig.prent=e,this.editDialogConfig.visible=!0},handleAddMenu:function(e){this.editDialogConfig.isCreate=0,this.editDialogConfig.prent=e,this.editDialogConfig.data={},this.editDialogConfig.biztype=this.biztype,this.editDialogConfig.visible=!0},getCurrentNode:function(e){var t=this.$refs.tree.getNode(e);this.childNodes(t),this.$emit("rulesSelect",this.$refs.tree.getCheckedKeys())},childNodes:function(e){for(var t=e.childNodes.length,i=0;i<t;i++)e.childNodes[i].checked=e.checked,this.childNodes(e.childNodes[i])},parentNodes:function(e){if(e.parent)for(var t in e)"parent"==t&&(e[t].checked=!0,this.parentNodes(e[t]))},handleDelMenu:function(e){var t=this;this.$modalSure("删除当前数据?").then((function(){l["x"](e.id).then((function(e){t.handlerGetTreeList(),t.$store.commit("product/SET_MerProductClassify",[]),t.$message.success("删除成功")}))}))},handlerGetList:function(){this.handlerGetTreeList()},changeNodes:function(e){if(e.length>0)for(var t=0;t<e.length;t++)!e[t].childList||e[t].childList.length<1?e[t].childList=void 0:this.changeNodes(e[t].childList);return e},handlerGetTreeList:function(){var e=this;l["y"]().then((function(t){var i=t.map((function(e){return S(S({},e),{},{parentId:e.pid,children:[]})}));e.dataList=e.handleTree(i,"id"),e.treeList=e.handleAddArrt(t),e.loading=!1})).catch((function(){e.loading=!1}))},handlerGetInfo:function(e){this.viewInfoConfig.data=e,this.viewInfoConfig.visible=!0},handleNodeClick:function(e){console.log("data:",e)},handleAddArrt:function(e){var t=v["addTreeListLabel"](e);return t},hideEditDialog:function(){var e=this;setTimeout((function(){e.editDialogConfig.prent={},e.editDialogConfig.type=0,e.editDialogConfig.visible=!1,e.handlerGetTreeList()}),200)},handleSelectionChange:function(e,t){t.checkedNodes;var i=t.checkedKeys;t.halfCheckedNodes,t.halfCheckedKeys;this.multipleSelection=i,this.$emit("rulesSelect",this.multipleSelection)}}},O=N,z=(i("a6ed"),Object(p["a"])(O,r,o,!1,null,"2fec0a09",null)),j=z.exports,T={components:{categoryList:j},data:function(){return{constants:this.$constants}}},E=T,M=Object(p["a"])(E,a,n,!1,null,"660db424",null);t["default"]=M.exports}}]);