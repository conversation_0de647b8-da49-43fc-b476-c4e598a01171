(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-aef19e8e"],{"49f2":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[t.isDisabled?t._e():a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-steps",{attrs:{active:t.currentTab,"align-center":"","finish-status":"success"}},[a("el-step",{attrs:{title:"选择视频号商品"}}),t._v(" "),a("el-step",{attrs:{title:"填写基础信息"}}),t._v(" "),a("el-step",{attrs:{title:"修改商品详情"}})],1)],1),t._v(" "),a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.fullscreenLoading,expression:"fullscreenLoading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":"150px"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("div",{directives:[{name:"show",rawName:"v-show",value:0===t.currentTab,expression:"currentTab === 0"}]},[a("el-form-item",{attrs:{label:"选择商品：",prop:"image"}},[a("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:draft:update"],expression:"['merchant:pay:component:product:draft:update']"}],staticClass:"upLoadPicBox",on:{click:t.changeGood}},[t.formValidate.image?a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.formValidate.image}})]):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:1===t.currentTab,expression:"currentTab === 1"}]},[a("el-row",{attrs:{gutter:24}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品轮播图：",prop:"images"}},[a("div",{staticClass:"acea-row"},[t.isDisabled?t._l(t.formValidate.images,(function(t,e){return a("div",{key:e,staticClass:"pictrue"},[a("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t,"preview-src-list":[t]}})],1)})):t._l(t.formValidate.images,(function(e,i){return a("div",{key:i,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e,"images")},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e,"images")},dragenter:function(a){return t.handleDragEnter(a,e,"images")},dragend:function(a){return t.handleDragEnd(a,e,"images")}}},[a("img",{attrs:{src:e}}),t._v(" "),a("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(i,"images")}}})])})),t._v(" "),t.formValidate.images.length<5&&!t.isDisabled?a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("2")}}},[a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2)])],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品标题：",prop:"title"}},[a("el-input",{attrs:{disabled:t.isDisabled,maxlength:"249",placeholder:"请输入商品名称"},model:{value:t.formValidate.title,callback:function(e){t.$set(t.formValidate,"title",e)},expression:"formValidate.title"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[a("el-input",{staticClass:"selWidth",attrs:{disabled:t.isDisabled,placeholder:"请输入单位"},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[a("div",{staticClass:"acea-row"},[a("el-select",{staticClass:"selWidth",attrs:{disabled:t.isDisabled,placeholder:"请选择"},model:{value:t.formValidate.tempId,callback:function(e){t.$set(t.formValidate,"tempId",e)},expression:"formValidate.tempId"}},t._l(t.shippingList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)])],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"微信商品类目：",prop:"thirdCatIdList"}},[a("div",{staticClass:"selWidth thirdCatIdList",on:{click:t.handleChanges}}),t._v(" "),a("el-cascader",{ref:"demoCascader",staticClass:"selWidth",attrs:{disabled:t.isDisabled,filterable:"",options:t.options,props:t.propsCatId},model:{value:t.formValidate.thirdCatIdList,callback:function(e){t.$set(t.formValidate,"thirdCatIdList",e)},expression:"formValidate.thirdCatIdList"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品品牌："}},[a("el-select",{staticClass:"selWidth",attrs:{disabled:t.isDisabled,placeholder:"请选择",clearable:""},model:{value:t.formValidate.brandId,callback:function(e){t.$set(t.formValidate,"brandId",e)},expression:"formValidate.brandId"}},t._l(t.brandIdList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),t._v(" "),t.productQualificationType>0?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品资质图："}},[a("div",{staticClass:"acea-row"},[t.isDisabled?t._l(t.formValidate.qualificationPicsList,(function(t,e){return a("div",{key:e,staticClass:"pictrue"},[a("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t,"preview-src-list":[t]}})],1)})):t._l(t.formValidate.qualificationPicsList,(function(e,i){return a("div",{key:i,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e,"qualificationPics")},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e,"qualificationPics")},dragenter:function(a){return t.handleDragEnter(a,e,"qualificationPics")},dragend:function(a){return t.handleDragEnd(a,e,"qualificationPics")}}},[a("img",{attrs:{src:e}}),t._v(" "),a("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(i,"qualificationPics")}}})])})),t._v(" "),t.formValidate.qualificationPicsList.length<5&&!t.isDisabled?a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("3")}}},[a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2),t._v(" "),a("div",{staticClass:"fontColor3"},[t._v("\n                资质类型说明："+t._s(t.productQualification)+"。"+t._s(1===t.productQualificationType?"必填项！":"选填项！")+"\n              ")])])],1):t._e(),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"labeltop",attrs:{label:"商品属性：",required:""}},[a("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.ManyAttrValue,"tooltip-effect":"dark"},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{key:"1",attrs:{type:"selection",width:"55"}}),t._v(" "),t.manyTabDate&&t.formValidate.specType?t._l(t.manyTabDate,(function(e,i){return a("el-table-column",{key:i,attrs:{align:"center",label:t.manyTabTit[i].title,"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[i])}})]}}],null,!0)})})):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(a){return t.modalPicTap("1","duo",e.$index)}}},[e.row.image?a("div",{staticClass:"pictrue tabPic"},[a("img",{attrs:{src:e.row.image}})]):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}])}),t._v(" "),t._l(t.attrValue,(function(e,i){return a("el-table-column",{key:i,attrs:{label:t.formThead[i].title,align:"center","min-width":"140"},scopedSlots:t._u([{key:"default",fn:function(e){return["商品价"===t.formThead[i].title?a("el-input-number",{staticClass:"priceBox",attrs:{size:"small",disabled:t.isDisabled,min:0,precision:2,step:.1},model:{value:e.row[i],callback:function(a){t.$set(e.row,i,a)},expression:"scope.row[iii]"}}):a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[i])}})]}}],null,!0)})}))],2)],1)],1)],1)],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:2===t.currentTab&&!t.isDisabled,expression:"currentTab === 2 && !isDisabled"}]},[a("el-form-item",{attrs:{label:"商品详情："}},[a("Tinymce",{model:{value:t.formValidate.descInfo,callback:function(e){t.$set(t.formValidate,"descInfo",e)},expression:"formValidate.descInfo"}})],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:2===t.currentTab&&t.isDisabled,expression:"currentTab === 2 && isDisabled"}]},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("span",{domProps:{innerHTML:t._s(t.formValidate.descInfo||"无")}})])],1)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"30px"}},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.$route.params.id&&t.currentTab>0||t.$route.params.id&&2===t.currentTab,expression:"(!$route.params.id && currentTab > 0) || ($route.params.id && currentTab === 2)"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:t.handleSubmitUp}},[t._v("上一步")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:0===t.currentTab,expression:"currentTab === 0"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSubmitNest1("formValidate")}}},[t._v("下一步")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:1===t.currentTab,expression:"currentTab === 1"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.handleSubmitNest2("formValidate")}}},[t._v("下一步")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:(2===t.currentTab||t.$route.params.id)&&!t.isDisabled,expression:"(currentTab === 2 || $route.params.id) && !isDisabled"},{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:pay:component:product:draft:update","merchant:pay:component:product:draft:add"],expression:"['merchant:pay:component:product:draft:update', 'merchant:pay:component:product:draft:add']"}],staticClass:"submission",attrs:{loading:t.loading,type:"primary",size:"small"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1),t._v(" "),a("el-dialog",{staticClass:"modalBox",attrs:{title:"提示",visible:t.dialogVisible,width:"900px",top:"0vh","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-cascader-panel",{ref:"demoCascader",staticClass:"modalBox",staticStyle:{height:"745px"},attrs:{filterable:"",options:t.options,props:t.propsCatId,"popper-append-to-body":!1,"popper-class":"eloption"},on:{change:t.handleChangesModel},model:{value:t.formValidate.thirdCatIdList,callback:function(e){t.$set(t.formValidate,"thirdCatIdList",e)},expression:"formValidate.thirdCatIdList"}}),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("确 定")])],1)],1)],1)},r=[],n=a("8256"),o=a("c4c8"),s=a("2f2c"),l=a("b7be"),c=a("61f7");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(t){return p(t)||h(t)||m(t)||f()}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"===typeof t)return g(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?g(t,e):void 0}}function h(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function p(t){if(Array.isArray(t))return g(t)}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */v=function(){return t};var t={},e=Object.prototype,a=e.hasOwnProperty,i=Object.defineProperty||function(t,e,a){t[e]=a.value},r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",s=r.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(P){l=function(t,e,a){return t[e]=a}}function c(t,e,a,r){var n=e&&e.prototype instanceof m?e:m,o=Object.create(n.prototype),s=new x(r||[]);return i(o,"_invoke",{value:_(t,a,s)}),o}function d(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(P){return{type:"throw",arg:P}}}t.wrap=c;var f={};function m(){}function h(){}function p(){}var g={};l(g,n,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&a.call(y,n)&&(g=y);var w=p.prototype=m.prototype=Object.create(g);function V(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(i,n,o,s){var l=d(t[i],t,n);if("throw"!==l.type){var c=l.arg,f=c.value;return f&&"object"==u(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(f).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,s)}))}s(l.arg)}var n;i(this,"_invoke",{value:function(t,a){function i(){return new e((function(e,i){r(t,a,e,i)}))}return n=n?n.then(i,i):i()}})}function _(t,e,a){var i="suspendedStart";return function(r,n){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===r)throw n;return S()}for(a.method=r,a.arg=n;;){var o=a.delegate;if(o){var s=I(o,a);if(s){if(s===f)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===i)throw i="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i="executing";var l=d(t,e,a);if("normal"===l.type){if(i=a.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(i="completed",a.method="throw",a.arg=l.arg)}}}function I(t,e){var a=e.method,i=t.iterator[a];if(void 0===i)return e.delegate=null,"throw"===a&&t.iterator.return&&(e.method="return",e.arg=void 0,I(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),f;var r=d(i,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,f;var n=r.arg;return n?n.done?(e[t.resultName]=n.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function k(t){if(t){var e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,r=function e(){for(;++i<t.length;)if(a.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return h.prototype=p,i(w,"constructor",{value:p,configurable:!0}),i(p,"constructor",{value:h,configurable:!0}),h.displayName=l(p,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,l(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},V(C.prototype),l(C.prototype,o,(function(){return this})),t.AsyncIterator=C,t.async=function(e,a,i,r,n){void 0===n&&(n=Promise);var o=new C(c(e,a,i,r),n);return t.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},V(w),l(w,s,"Generator"),l(w,n,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var i in e)a.push(i);return a.reverse(),function t(){for(;a.length;){var i=a.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},t.values=k,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function i(a,i){return o.type="throw",o.arg=t,e.next=a,i&&(e.method="next",e.arg=void 0),!!i}for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r],o=n.completion;if("root"===n.tryLoc)return i("end");if(n.tryLoc<=this.prev){var s=a.call(n,"catchLoc"),l=a.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return i(n.catchLoc,!0);if(this.prev<n.finallyLoc)return i(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return i(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return i(n.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var n=r;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,f):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),L(a),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var i=a.completion;if("throw"===i.type){var r=i.arg;L(a)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:k(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),f}},t}function b(t,e,a,i,r,n,o){try{var s=t[n](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(i,r)}function y(t){return function(){var e=this,a=arguments;return new Promise((function(i,r){var n=t.apply(e,a);function o(t){b(n,i,r,o,s,"next",t)}function s(t){b(n,i,r,o,s,"throw",t)}o(void 0)}))}}var w={image:"",images:[],headImg:"",title:"",ficti:0,unitName:"",sort:0,tempId:"",attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0}],attr:[],selectRule:"",descInfo:"",specType:!1,id:0,thirdCatId:"",thirdCatIdList:[],brandId:0,qualificationPics:"",qualificationPicsList:[]},V={price:{title:"商品价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},C={name:"creatSeckill",components:{Tinymce:n["a"]},data:function(){return{props:{children:"secondCatList",label:"secondCatName",value:"secondCatId",multiple:!0,emitPath:!1},propsCatId:{emitPath:!0},options:[],pickerOptions:{disabledDate:function(t){return t.getTime()<(new Date).setTime((new Date).getTime()-864e5)}},props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},grid2:{xl:8,lg:10,md:12,sm:24,xs:24},currentTab:0,formThead:Object.assign({},V),formValidate:Object.assign({},w),loading:!1,fullscreenLoading:!1,shippingList:[],ruleValidate:{primaryProductId:[{required:!0,message:"请选择商品",trigger:"change"}],title:[{required:!0,message:"请输入商品标题",trigger:"blur"}],attrValue:[{required:!0,message:"请选择商品属相",trigger:"change",type:"array",min:"1"}],ficti:[{required:!0,message:"请输入虚拟销量",trigger:"blur"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change"}],images:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}],thirdCatIdList:[{required:!0,message:"请选择商品类目",trigger:"change"}]},manyTabDate:{},manyTabTit:{},attrInfo:{},tempRoute:{},multipleSelection:[],primaryProductId:0,productQualificationType:0,productQualification:"",ManyAttrValue:[Object.assign({},w.attrValue[0])],brandIdList:[],dialogVisible:!1,isDisabled:"1"===this.$route.params.isDisabled}},computed:{attrValue:function(){var t=Object.assign({},w.attrValue[0]);return delete t.image,t}},created:function(){this.$watch("formValidate.attr",this.watCh),this.tempRoute=Object.assign({},this.$route),JSON.parse(sessionStorage.getItem("videoCategory"))||this.getCatList(),JSON.parse(sessionStorage.getItem("draftBrand"))||this.getDraftBrandlist()},mounted:function(){this.formValidate.images=[],this.$route.params.id&&(this.currentTab=1,this.setTagsViewTitle(),this.getInfo()),this.getShippingList(),this.options=JSON.parse(sessionStorage.getItem("videoCategory")),this.brandIdList=JSON.parse(sessionStorage.getItem("draftBrand"))},methods:{handleChangesModel:function(t){var e=this.$refs["demoCascader"].getCheckedNodes();this.productQualificationType=e.length>0?e[0].data.productQualificationType:0,this.productQualification=e.length>0?e[0].data.productQualification:"",this.dialogVisible=!1},handleChanges:function(t){this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},getCatList:function(){var t=this;Object(l["a"])().then((function(e){t.options=e,sessionStorage.setItem("videoCategory",JSON.stringify(e))}))},getDraftBrandlist:function(){var t=this;Object(l["d"])().then((function(e){t.brandIdList=e,sessionStorage.setItem("draftBrand",JSON.stringify(e))}))},watCh:function(t){var e={},a={};this.formValidate.attr.forEach((function(t,i){e[t.attrName]={title:t.attrName},a[t.attrName]=""})),this.manyTabTit=e,this.manyTabDate=a,this.formThead=Object.assign({},this.formThead,e)},handleRemove:function(t,e){"images"===e?this.formValidate.images.splice(t,1):this.formValidate.qualificationPicsList.splice(t,1)},handleSelectionChange:function(t){this.multipleSelection=t},modalPicTap:function(t,e,a){var i=this;if(!i.isDisabled){if("3"===t)return this.$message.warning("正在建造");this.fullscreenLoading=!0,this.$modalUpload(function(){var r=y(v().mark((function r(n){var o,s,l;return v().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(0!=n){r.next=2;break}return r.abrupt("return",i.fullscreenLoading=!1);case 2:if("1"!==t||e||(i.formValidate.image=n[0].sattDir,i.ManyAttrValue[0].image=n[0].sattDir),"2"!==t||e){r.next=11;break}if(!(n.length>5||n.length+i.formValidate.images.length>5)){r.next=6;break}return r.abrupt("return",this.$message.warning("最多选择5张图片！"));case 6:return r.next=8,i.getShopImgUpload(n,[]);case 8:o=r.sent,o.map((function(t){i.formValidate.images.push(t.img)})),i.fullscreenLoading=!1;case 11:if("1"!==t||"duo"!==e){r.next=17;break}return r.next=14,i.getImgData(n[0].sattDir);case 14:s=r.sent,i.ManyAttrValue[a].image=s,i.fullscreenLoading=!1;case 17:if("3"!==t){r.next=25;break}if(!(n.length>5||n.length+i.formValidate.qualificationPicsList.length>5)){r.next=20;break}return r.abrupt("return",this.$message.warning("最多选择5张图片！"));case 20:return r.next=22,i.getShopImgUpload(n,[]);case 22:l=r.sent,l.map((function(t){i.formValidate.qualificationPicsList.push(t.img)})),i.fullscreenLoading=!1;case 25:case"end":return r.stop()}}),r,this)})));return function(t){return r.apply(this,arguments)}}(),t,"content")}},changeGood:function(){var t=this;this.$modalGoodList((function(e){t.formValidate.image=e.image,t.primaryProductId=e.id}))},handleSubmitNest1:function(){if(!this.formValidate.image)return this.$message.warning("请选择商品！");this.currentTab++,this.$route.params.id||this.getProdect(this.primaryProductId)},filerMerCateList:function(t){return t.map((function(t){return t.child||(t.disabled=!0),t.label=t.name,t}))},getShippingList:function(){var t=this;Object(s["f"])(this.tempData).then((function(e){t.shippingList=e.list}))},addTem:function(){this.$refs.addTemplates.dialogVisible=!0,this.$refs.addTemplates.getCityList()},getInfo:function(){this.$route.params.id?this.getDraftProdect(this.$route.params.id):this.getProdect(this.primaryProductId)},getProdect:function(t){var e=this;this.fullscreenLoading=!0,Object(o["E"])(t).then(function(){var t=y(v().mark((function t(a){var i;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=a,e.formValidate={image:e.$selfUtil.setDomain(i.image),images:JSON.parse(i.sliderImage),title:i.name,sort:i.sort,tempId:i.tempId,attr:i.attr,attrValue:i.attrValue,unitName:i.unitName,descInfo:i.content,specType:i.specType,primaryProductId:i.id,ficti:i.ficti,qualificationPicsList:[],thirdCatIdList:[],thirdCatId:0,brandId:"",id:0},i.specType?(i.attrValue.forEach((function(t){for(var e in t.attrValue=JSON.parse(t.attrValue),t.attrValue)t[e]=t.attrValue[e]})),e.$nextTick((function(){i.attrValue.forEach((function(t){t.image=e.$selfUtil.setDomain(t.image),e.$refs.multipleTable.toggleRowSelection(t,!0),e.$set(t,"checked",!0)}))})),e.ManyAttrValue=i.attrValue,e.multipleSelection=i.attrValue):(e.ManyAttrValue=i.attrValue,e.formValidate.attr=[]),e.getShopImg(i.sliderImage,i.attrValue);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1}))},getShopImg:function(){var t=y(v().mark((function t(e,a){var i,r,n,o,s=this;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=d(JSON.parse(e)),a.map((function(t){return i.push(t.image)})),r=d(new Set(i)),n=[],r.map((function(t){n.push({key:t,img:t})})),t.next=7,this.getShopImgUpload(n,[]);case 7:o=t.sent,this.formValidate.images=this.formValidate.images.map((function(t,e){if(o[e].key&&t===o[e].key)return o[e].img;s.formValidate.images.splice(e,1)})),this.ManyAttrValue.map((function(t,e){o.map((function(e,a){if(e.key&&t.image===e.key)return t.image=e.img}))})),this.fullscreenLoading=!1;case 11:case"end":return t.stop()}}),t,this)})));function e(e,a){return t.apply(this,arguments)}return e}(),getShopImgUpload:function(){var t=y(v().mark((function t(e,a){var i,r;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:t.t0=v().keys(e);case 1:if((t.t1=t.t0()).done){t.next=9;break}return i=t.t1.value,t.next=5,this.getImgData(e[i].img||e[i].sattDir);case 5:r=t.sent,a.push({key:e[i].img||e[i].imgsattDir,img:r}),t.next=1;break;case 9:return t.abrupt("return",a);case 10:case"end":return t.stop()}}),t,this)})));function e(e,a){return t.apply(this,arguments)}return e}(),getImgData:function(t){var e=this;return new Promise((function(a,i){Object(l["j"])({imgUrl:t,respType:1,uploadType:1}).then((function(t){t.errcode>0?(e.fullscreenLoading=!1,e.$message.error(t.errmsg),a("")):a(t.img_info.temp_img_url)}))}))},getDraftProdect:function(t){var e=this;this.fullscreenLoading=!0,Object(l["f"])(t).then(function(){var t=y(v().mark((function t(a){var i;return v().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=a,e.formValidate={images:JSON.parse(i.headImg),title:i.title,unitName:i.unitName,tempId:i.tempId,attr:JSON.parse(i.attr),descInfo:i.descInfo,specType:i.specType,primaryProductId:i.primaryProductId,ficti:i.ficti,thirdCatIdList:[i.catInfo.firstCatId,i.catInfo.secondCatId,i.catInfo.thirdCatId],thirdCatId:i.thirdCatId,brandId:i.brandId,qualificationPicsList:JSON.parse(i.qualificationPics)||[],id:i.id,productId:i.productId},i.specType?(e.ManyAttrValue=JSON.parse(i.attrValue),e.$nextTick((function(){e.ManyAttrValue.forEach((function(t,a){for(var i in t.attrValue=JSON.parse(t.attrValue),t.attrValue)t[i]=t.attrValue[i];t.id&&(e.$set(t,"price",t.price),e.$nextTick((function(){e.$refs.multipleTable.toggleRowSelection(t,!0)})))}))}))):(e.ManyAttrValue=JSON.parse(i.attrValue),e.formValidate.attr=[]),e.fullscreenLoading=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1}))},handleSubmitNest2:function(t){var e=this;this.$refs[t].validate((function(t){return!!t&&(e.formValidate.specType&&0===e.multipleSelection.length?e.$message.warning("请填选择至少一个商品属性！"):1===e.productQualificationType&&0===e.formValidate.qualificationPicsList.length?e.$message.warning("该类目必须上传商品资质图片，请上传！"):void e.currentTab++)}))},handleSubmit:Object(c["a"])((function(t){var e=this;this.formValidate.specType?(this.formValidate.attrValue=this.multipleSelection,this.formValidate.attrValue.forEach((function(t){t.attrValue=JSON.stringify(t.attrValue)}))):this.formValidate.attrValue=this.ManyAttrValue,this.formValidate.thirdCatId=this.formValidate.thirdCatIdList[2],this.formValidate.headImg=JSON.stringify(this.formValidate.images),this.formValidate.qualificationPics=JSON.stringify(this.formValidate.qualificationPicsList),this.$refs[t].validate((function(a){a?(e.fullscreenLoading=!0,e.loading=!0,e.$route.params.id?Object(l["i"])(e.formValidate).then(y(v().mark((function a(){return v().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.fullscreenLoading=!1,e.$message.success("编辑成功"),e.$router.push({path:"/videoChannel/draftList"}),e.$refs[t].resetFields(),e.formValidate.images=[],e.formValidate.qualificationPics=[],e.loading=!1;case 7:case"end":return a.stop()}}),a)})))).catch((function(){e.fullscreenLoading=!1,e.loading=!1})):Object(l["k"])(e.formValidate).then(function(){var a=y(v().mark((function a(i){return v().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.fullscreenLoading=!1,e.$message.success("新增成功"),e.$router.push({path:"/videoChannel/draftList"}),e.$refs[t].resetFields(),e.formValidate.images=[],e.formValidate.qualificationPics=[],e.loading=!1;case 7:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}()).catch((function(){e.fullscreenLoading=!1,e.loading=!1}))):e.formValidate.storeName&&e.formValidate.unitName&&e.formValidate.store_info&&e.formValidate.image&&e.formValidate.images||e.$message.warning("请填写完整商品信息！")}))})),handleSubmitUp:function(){this.productQualificationType=0,this.formValidate.thirdCatIdList=[],this.currentTab--<0&&(this.currentTab=0)},setTagsViewTitle:function(){var t=this.isDisabled?"查看视频号商品":"编辑视频号商品",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect="move"},handleDragEnter:function(t,e,a){var i;if(t.dataTransfer.effectAllowed="move",e!==this.dragging){var r=[];r=d("images"===a?this.formValidate.images:this.formValidate.qualificationPicsList);var n=r.indexOf(this.dragging),o=r.indexOf(e);(i=r).splice.apply(i,[o,0].concat(d(r.splice(n,1)))),"images"===a?this.formValidate.images=r:this.formValidate.qualificationPicsList=r}}}},_=C,I=(a("b7b2"),a("2877")),T=Object(I["a"])(_,i,r,!1,null,"ad262d0c",null);e["default"]=T.exports},7981:function(t,e,a){"use strict";a("f2b5")},8256:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tinymce-container editor-container",class:{fullscreen:t.fullscreen}},[a("textarea",{staticClass:"tinymce-textarea",attrs:{id:t.tinymceId}}),t._v(" "),a("div",{staticClass:"editor-custom-btn-container"},[a("editorImage",{staticClass:"editor-upload-btn",attrs:{color:"#1890ff"},on:{successCBK:t.imageSuccessCBK}})],1)])},r=[],n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"upload-container"},[a("el-button",{style:{background:t.color,borderColor:t.color},attrs:{icon:"el-icon-upload",size:"mini",type:"primary"},on:{click:function(e){return t.modalPicTap("2")}}},[t._v("\n    选择素材")])],1)},o=[],s={name:"EditorSlideUpload",props:{color:{type:String,default:"#1890ff"}},data:function(){return{dialogVisible:!1,listObj:{},fileList:[]}},methods:{modalPicTap:function(t){var e=this;this.$modalUpload((function(t){if(t){var a=[];if(t.length>10)return this.$message.warning("最多选择10张图片！");t.map((function(t){a.push(t.sattDir)})),e.$emit("successCBK",a)}}),t,"content")}}},l=s,c=(a("7981"),a("2877")),u=Object(c["a"])(l,n,o,!1,null,"7c5de02a",null),d=u.exports,f=["advlist anchor autolink autosave code codesample colorpicker colorpicker contextmenu directionality emoticons fullscreen hr image imagetools insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace spellchecker tabfocus table template textcolor textpattern visualblocks visualchars wordcount"],m=f,h=["searchreplace bold italic underline strikethrough alignleft aligncenter alignright outdent indent  blockquote undo redo removeformat subscript superscript code codesample fontsizeselect  fontselect","hr bullist numlist link image charmap preview anchor pagebreak insertdatetime media table emoticons forecolor backcolor fullscreen"],p=h,g={name:"Tinymce",components:{editorImage:d},props:{id:{type:String,default:function(){return"vue-tinymce-"+ +new Date+(1e3*Math.random()).toFixed(0)}},value:{type:String,default:""},toolbar:{type:Array,required:!1,default:function(){return[]}},menubar:{type:String,default:"file edit insert view format table"},height:{type:Number,required:!1,default:400}},data:function(){return{hasChange:!1,hasInit:!1,tinymceId:this.id,fullscreen:!1,languageTypeList:{en:"en",zh:"zh_CN"}}},computed:{language:function(){return this.languageTypeList["zh"]}},watch:{value:function(t){var e=this;!this.hasChange&&this.hasInit&&this.$nextTick((function(){return window.tinymce.get(e.tinymceId).setContent(t||"")}))},language:function(){var t=this;this.destroyTinymce(),this.$nextTick((function(){return t.initTinymce()}))}},mounted:function(){this.initTinymce()},activated:function(){this.initTinymce()},deactivated:function(){this.destroyTinymce()},destroyed:function(){this.destroyTinymce()},methods:{initTinymce:function(){var t=this,e=this;window.tinymce.init({language:this.language,selector:"#".concat(this.tinymceId),height:this.height,body_class:"panel-body ",object_resizing:!1,toolbar:this.toolbar.length>0?this.toolbar:p,menubar:this.menubar,plugins:m,end_container_on_empty_block:!0,powerpaste_word_import:"clean",code_dialog_height:450,code_dialog_width:1e3,advlist_bullet_styles:"square",advlist_number_styles:"default",imagetools_cors_hosts:["www.tinymce.com","codepen.io"],default_link_target:"_blank",link_title:!1,convert_urls:!1,nonbreaking_force_tab:!0,init_instance_callback:function(a){e.value&&a.setContent(e.value),e.hasInit=!0,a.on("NodeChange Change KeyUp SetContent",(function(){t.hasChange=!0,t.$emit("input",a.getContent())}))},setup:function(t){t.on("FullscreenStateChanged",(function(t){e.fullscreen=t.state}))}})},destroyTinymce:function(){var t=window.tinymce.get(this.tinymceId);this.fullscreen&&t.execCommand("mceFullScreen"),t&&t.destroy()},setContent:function(t){window.tinymce.get(this.tinymceId).setContent(t)},getContent:function(){window.tinymce.get(this.tinymceId).getContent()},imageSuccessCBK:function(t){var e=this,a=this;t.forEach((function(t){"video"==e.getFileType(t)?window.tinymce.get(a.tinymceId).insertContent('<video class="wscnph" src="'.concat(t,'" controls muted></video>')):window.tinymce.get(a.tinymceId).insertContent('<img class="wscnph" src="'.concat(t,'" />'))}))},getFileType:function(t){var e="",a="";try{var i=t.split(".");e=i[i.length-1]}catch(o){e=""}if(!e)return!1;e=e.toLocaleLowerCase();var r=["png","jpg","jpeg","bmp","gif"];if(a=r.find((function(t){return t===e})),a)return"image";var n=["mp4","m2v","mkv","rmvb","wmv","avi","flv","mov","m4v"];return a=n.find((function(t){return t===e})),a?"video":"other"}}},v=g,b=(a("9d5e"),Object(c["a"])(v,i,r,!1,null,"4926248e",null));e["a"]=b.exports},"9d5e":function(t,e,a){"use strict";a("ddf7")},b7b2:function(t,e,a){"use strict";a("f222")},ddf7:function(t,e,a){},f222:function(t,e,a){},f2b5:function(t,e,a){}}]);