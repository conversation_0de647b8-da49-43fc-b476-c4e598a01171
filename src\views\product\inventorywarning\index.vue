<template>
  <div class="inventory-warning">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="预警商品" name="warning"></el-tab-pane>
      <el-tab-pane label="不再提醒" name="noRemind"></el-tab-pane>
    </el-tabs>

    <el-form :inline="true" :model="searchForm" class="search-form" size="small">
      <el-form-item label="商品名称：">
        <el-input v-model="searchForm.productName" placeholder="请输入商品名称" clearable />
      </el-form-item>
      <el-form-item label="商品状态：">
        <el-select v-model="searchForm.status" placeholder="请选择">
          <el-option label="全部" value="" />
          <el-option label="缺货" value="1" />
          <el-option label="预警" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="商品分类：">
        <el-input v-model="searchForm.category" placeholder="请输入商品分类" clearable />
      </el-form-item>
    </el-form>
    <div>
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button type="primary" @click="handleExport">导出</el-button>
    </div>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px" size="small" v-loading="loading" class="mt20">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="productName" label="商品名称"></el-table-column>
      <el-table-column prop="productPrice" label="商品售价"></el-table-column>
      <el-table-column prop="productSales" label="商品销量"></el-table-column>
      <el-table-column prop="currentStock" label="商品库存"></el-table-column>
      <el-table-column prop="warningStock" label="预警库存"></el-table-column>
      <el-table-column prop="status" label="商品状态">
        <template slot-scope="scope">
          {{ scope.row.status === 1 ? '缺货' : scope.row.status === 2 ? '预警' : '' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button v-if="activeTab === 'warning'" type="text" @click="handleNoRemind(scope.row)">不再提示</el-button>
          <el-button v-else type="text" @click="handleRemindAgain(scope.row)">重新提醒</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="block">
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import {
  getInventoryWarningList,
  getNeedRemindList,
  updateInventoryWarningStatus,
  updateRemindStatus,
  checkAndUpdateWarningStatus,
  exportInventoryWarning,
} from '@/api/productInventoryWarning';
import { export_json_to_excel } from '@/vendor/Export2Excel';
import { parseTime } from '@/utils/parsing';

export default {
  name: 'ProductInventoryWarningList',
  data() {
    return {
      activeTab: 'warning',
      searchForm: {
        productName: '',
        status: '',
        category: '',
      },
      tableData: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      loading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取表格数据
    getList() {
      this.loading = true;
      const params = {
        productName: this.searchForm.productName,
        status: this.searchForm.status,
        // category: this.searchForm.category, // 如后端支持可加
        page: this.currentPage,
        limit: this.pageSize,
        isRemind: this.activeTab === 'noRemind' ? 1 : 0,
      };
      getInventoryWarningList(params).then((res) => {
        console.log('getInventoryWarningList', res);
        this.tableData = res.list;
        this.total = res.total || 0;
        this.loading = false;
      });
    },
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handleExport() {
      this.$confirm('确认要导出缺货预警商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 获取所有数据（不分页）
          const params = {
            productName: this.searchForm.productName,
            status: this.searchForm.status,
            // category: this.searchForm.category, // 如后端支持可加
            isRemind: this.activeTab === 'noRemind' ? 1 : 0,
            page: 1,
            limit: 99999, // 假设不会超过10万条
          };
          this.loading = true;
          getInventoryWarningList(params)
            .then((res) => {
              const tHeader = [
                'ID',
                '商品名称',
                '商品售价',
                '商品销量',
                '商品库存',
                '预警库存',
                '商品状态',
                '是否提醒',
              ];
              const data = (res.list || []).map((item) => {
                return [
                  item.id,
                  item.productName,
                  item.productPrice,
                  item.productSales,
                  item.currentStock,
                  item.warningStock,
                  item.status === 1 ? '缺货' : item.status === 2 ? '预警' : '',
                  item.isRemind === 0 ? '提醒' : '不再提醒',
                ];
              });
              const filename = `缺货预警商品_${new Date().getTime()}`;
              export_json_to_excel({
                header: tHeader,
                data: data,
                filename: filename,
                autoWidth: true,
                bookType: 'xlsx',
              });
              this.loading = false;
            })
            .catch(() => {
              this.$message.error('导出失败');
              this.loading = false;
            });
        })
        .catch(() => {
          this.$message.info('已取消导出');
        });
    },
    handleNoRemind(row) {
      updateRemindStatus(row.id, 1).then(() => {
        this.$message.success(`ID:${row.id} 已设置为不再提示`);
        this.getList();
      });
    },
    handleRemindAgain(row) {
      updateRemindStatus(row.id, 0).then(() => {
        this.$message.success(`ID:${row.id} 已重新提醒`);
        this.getList();
      });
    },
    handleTabClick(tab) {
      this.currentPage = 1;
      this.getList();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.getList();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.inventory-warning {
  background: #fff;
  padding: 24px;
}

.search-form {
  margin: 20px 0 0 0;
}

.mt20 {
  margin-top: 20px;
}

.block {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 覆盖el-tabs默认边框
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

// 为tabs头部添加底部边框线
::v-deep .el-tabs__header {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e7ed;
}
</style>
