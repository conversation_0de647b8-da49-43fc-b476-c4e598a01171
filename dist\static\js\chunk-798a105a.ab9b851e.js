(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-798a105a"],{"086b":function(e,t,a){},"81e5":function(e,t,a){"use strict";a("fef0")},b9c2:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{ref:"tableheader",staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{ref:"userFrom",attrs:{inline:"",size:"small",model:e.userFrom,"label-position":e.labelPosition,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[a("el-col",e._b({},"el-col",e.grid,!1),[a("el-form-item",{attrs:{label:"用户昵称："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入用户昵称",clearable:""},model:{value:e.nikename,callback:function(t){e.nikename=t},expression:"nikename"}})],1)],1)],1),e._v(" "),e.collapse?[a("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[a("el-form-item",{attrs:{label:"访问时间："}},[a("el-date-picker",{staticClass:"selWidth",attrs:{align:"right","unlink-panels":"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1)],1),e._v(" "),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[a("el-col",e._b({},"el-col",e.grid,!1),[a("el-form-item",{attrs:{label:"手机号："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入手机号，不支持模糊搜索",clearable:""},model:{value:e.userFrom.phone,callback:function(t){e.$set(e.userFrom,"phone",t)},expression:"userFrom.phone"}})],1)],1),e._v(" "),a("el-col",e._b({},"el-col",e.grid,!1),[a("el-form-item",{attrs:{label:"性别："}},[a("el-radio-group",{staticClass:"selWidth",attrs:{type:"button"},model:{value:e.userFrom.sex,callback:function(t){e.$set(e.userFrom,"sex",t)},expression:"userFrom.sex"}},[a("el-radio-button",{attrs:{label:""}},[a("span",[e._v("全部")])]),e._v(" "),a("el-radio-button",{attrs:{label:"0"}},[a("span",[e._v("未知")])]),e._v(" "),a("el-radio-button",{attrs:{label:"1"}},[a("span",[e._v("男")])]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[a("span",[e._v("女")])]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[a("span",[e._v("保密")])])],1)],1)],1),e._v(" "),a("el-col",e._b({},"el-col",e.grid,!1),[a("el-form-item",{attrs:{label:"注册类型："}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:function(t){return e.getList(1)}},model:{value:e.userFrom.registerType,callback:function(t){e.$set(e.userFrom,"registerType",t)},expression:"userFrom.registerType"}},e._l(e.registerTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)]:e._e(),e._v(" "),a("el-col",{staticClass:"text-right userFrom userbtn",attrs:{xs:24,sm:24,md:24,lg:6,xl:6}},[a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:user:page:list"],expression:"['merchant:user:page:list']"}],staticClass:"mr15",attrs:{type:"primary",icon:"ios-search",label:"default",size:"small"},on:{click:e.userSearchs}},[e._v("查询")]),e._v(" "),a("el-button",{staticClass:"ResetSearch mr10",attrs:{size:"small"},on:{click:function(t){return e.reset("userFrom")}}},[e._v("重置")]),e._v(" "),a("a",{staticClass:"ivu-ml-8",on:{click:e.onCollapse}},[e.collapse?[e._v(" 收起 "),a("i",{staticClass:"el-icon-arrow-up"})]:[e._v(" 展开 "),a("i",{staticClass:"el-icon-arrow-down"})]],2)],1)],1)],2)],1)],1)]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:e.tableData.data,size:"mini",height:e.tableHeight,"highlight-current-row":""}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"真实姓名："}},[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.realName)))])]),e._v(" "),a("el-form-item",{attrs:{label:"性别"}},[a("span",[e._v(e._s(e._f("sexFilter")(t.row.sex)))])]),e._v(" "),a("el-form-item",{attrs:{label:"首次访问："}},[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.createTime)))])]),e._v(" "),a("el-form-item",{attrs:{label:"近次访问："}},[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.lastLoginTime)))])]),e._v(" "),a("el-form-item",{attrs:{label:"备注："}},[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.mark)))])])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}])}),e._v(" "),e.checkedCities.includes("昵称")?a("el-table-column",{attrs:{label:"昵称","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:1==t.row.isLogoff?"red":""},[e._v(e._s(e._f("filterEmpty")(t.row.nickname)))]),e._v(" "),1==t.row.isLogoff?a("span",{class:1==t.row.isLogoff?"red":""},[e._v("|")]):e._e(),e._v(" "),1==t.row.isLogoff?a("span",{staticClass:"red"},[e._v("(已注销)")]):e._e()]}}],null,!1,3133054448)}):e._e(),e._v(" "),a("el-table-column",{attrs:{label:"手机号","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("filterEmpty")(t.row.phone)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"registerType",label:"注册类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{color:e.filterRegisterType(t.row.registerType)}},[e._v(e._s(e._f("registerTypeFilter")(t.row.registerType)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right",align:"center","render-header":e.renderHeader},scopedSlots:e._u([{key:"default",fn:function(t){return[e.checkPermi(["merchant:user:detail"])?a("el-button",{attrs:{type:"text"},nativeOn:{click:function(a){return e.onDetails(t.row.id)}}},[e._v("用户详情")]):e._e()]}}])})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[15,30,45,60],"page-size":e.userFrom.limit,"current-page":e.userFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.card_select_show,expression:"card_select_show"}],staticClass:"card_abs",style:{top:e.collapse?"570px":"270px"}},[[a("div",{staticClass:"cell_ht"},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.checkSave()}}},[e._v("保存")])],1),e._v(" "),a("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.checkedCities,callback:function(t){e.checkedCities=t},expression:"checkedCities"}},e._l(e.columnData,(function(t){return a("el-checkbox",{key:t,staticClass:"check_cell",attrs:{label:t}},[e._v(e._s(t))])})),1)]],2),e._v(" "),a("detail-user",{ref:"userDetailFrom"})],1)},s=[],l=a("c24f"),r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入用户名称"},model:{value:e.tableFrom.keywords,callback:function(t){e.$set(e.tableFrom,"keywords",t)},expression:"tableFrom.keywords"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.search},slot:"append"})],1)],1)],1)],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tableData.data,width:"800px",size:"small"}},[a("el-table-column",{attrs:{label:"",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.row.uid},nativeOn:{change:function(a){return e.getTemplateRow(t.$index,t.row)}},model:{value:e.templateRadio,callback:function(t){e.templateRadio=t},expression:"templateRadio"}},[e._v(" ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"uid",label:"ID","min-width":"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"nickname",label:"微信用户名称","min-width":"130"}}),e._v(" "),a("el-table-column",{attrs:{label:"用户头像","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticClass:"tabImage",attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"性别","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("saxFilter")(t.row.sex)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"地区","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.addres))])]}}])})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1)],1)},n=[],o={name:"UserList",filters:{saxFilter:function(e){var t={0:"未知",1:"男",2:"女"};return t[e]},statusFilter:function(e){var t={wechat:"微信用户",routine:"小程序用户"};return t[e]}},data:function(){return{templateRadio:0,loading:!1,tableData:{data:[],total:0},tableFrom:{page:1,limit:10,keywords:""}}},mounted:function(){this.getList()},methods:{getTemplateRow:function(e,t){this.$emit("getTemplateRow",t)},getList:function(){var e=this;this.loading=!0,Object(l["h"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.loading=!1})).catch((function(t){e.$message.error(t.message),e.loading=!1}))},search:function(){var e=this;this.loading=!0,Object(l["h"])({keywords:this.tableFrom.keywords}).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.loading=!1})).catch((function(t){e.$message.error(t.message),e.loading=!1}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()}}},c=o,d=a("2877"),v=Object(d["a"])(c,r,n,!1,null,"b902709e",null),u=(v.exports,function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-drawer",{ref:"userDetailFrom",attrs:{visible:e.dialogUserDetail,size:"1200px"},on:{"update:visible":function(t){e.dialogUserDetail=t},close:e.handleClose}},[a("div",{staticClass:"title",attrs:{slot:"title"},slot:"title"}),e._v(" "),a("div",{staticClass:"demo-drawer__content"},[e.userDetailData?a("div",{staticClass:"description"},[a("div",{staticClass:"con-head"},[a("img",{attrs:{src:e.userDetailData.avatar,alt:""}}),e._v(" "),a("span",{staticClass:"nickname"},[e._v(e._s(e.userDetailData.nickname))])]),e._v(" "),a("div",{staticClass:"acea-row info-row"},[a("div",{staticClass:"info-row-item"},[a("div",{staticClass:"info-row-item-title"},[e._v("用户余额")]),e._v(" "),a("div",[e._v(e._s(e.userDetailData.nowMoney))])]),e._v(" "),a("div",{staticClass:"info-row-item"},[a("div",{staticClass:"info-row-item-title"},[e._v("用户经验")]),e._v(" "),a("div",[e._v(e._s(e.userDetailData.experience))])]),e._v(" "),a("div",{staticClass:"info-row-item"},[a("div",{staticClass:"info-row-item-title"},[e._v("等级")]),e._v(" "),a("div",[e._v(e._s(e.userDetailData.level))])]),e._v(" "),a("div",{staticClass:"info-row-item"},[a("div",{staticClass:"info-row-item-title"},[e._v("佣金金额")]),e._v(" "),a("div",[e._v(e._s(e.userDetailData.brokeragePrice))])]),e._v(" "),a("div",{staticClass:"info-row-item"},[a("div",{staticClass:"info-row-item-title"},[e._v("用户购买次数")]),e._v(" "),a("div",[e._v(e._s(e.userDetailData.payCount))])])]),e._v(" "),a("div",{staticClass:"user-info"},[a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd"},[e._v("基本信息")]),e._v(" "),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[e._v("用户电话：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.phone))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("真实姓名：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.realName||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("用户账号：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.account||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("生日：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.birthday||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("性别：")]),e._v(" "),a("div",{staticClass:"value"},[e._v("\n                  "+e._s(0==e.userDetailData.sex?"未知":1==e.userDetailData.sex?"男":2==e.userDetailData.sex?"女":"保密")+"\n                ")])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("国家：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s("CN"==e.userDetailData.country?"中国":"其他"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("用户地址：")]),e._v(" "),a("div",{staticClass:"value"},[e._v("\n                  "+e._s(e.userDetailData.province+e.userDetailData.city+e.userDetailData.district+e.userDetailData.address||"-")+"\n                ")])])])])]),e._v(" "),a("div",{staticClass:"user-info"},[a("div",{staticClass:"section"},[a("div",{staticClass:"section-hd-other"},[e._v("其他信息")]),e._v(" "),a("div",{staticClass:"section-bd"},[a("div",{staticClass:"item"},[a("div",[e._v("创建ip：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.addIp||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("添加时间：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.createTime||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("是否关联ios：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isBindingIos?"是":"否"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("是否注销：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isLogoff?"是":"否"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("是否为推广员：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isPromoter?"是":"否"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("是否关联微信android：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatAndroid?"是":"否"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("是否关联微信ios：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatIos?"是":"否"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("是否关联公众号：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatPublic?"是":"否"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("是否关联小程序：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.isWechatRoutine?"是":"否"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("最后一次登录ip：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.lastIp||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("最后一次登录时间：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.lastLoginTime||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("注销时间：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.logoffTime||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("成为分销员时间：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.promoterTime||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("注册类型：")]),e._v(" "),a("div",{staticClass:"value"},[e._v("\n                  "+e._s("wechat"==e.userDetailData.registerType?"公众号":"routine"==e.userDetailData.registerType?"小程序":"h5"==e.userDetailData.registerType?"H5":"iosWx"==e.userDetailData.registerType?"微信ios":"androidWx"==e.userDetailData.registerType?"微信安卓":"ios")+"\n                ")])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("下级人数：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadCount||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("上级推广员昵称：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadName||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("绑定上级推广员时间：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadTime||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("上级推广员id：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.spreadUid||"-"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("状态：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.userDetailData.status?"正常":"禁止"))])]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("标签：")]),e._v(" "),a("div",{staticClass:"value",style:Array.isArray(e.userDetailData.tags)&&e.userDetailData.tags.length>0&&"margin-top: -8px"},[Array.isArray(e.userDetailData.tags)&&e.userDetailData.tags.length>0?e._l(e.userDetailData.tags,(function(t){return a("el-tag",{key:t.id,staticStyle:{"margin-right":"4px"}},[e._v(e._s(t.name))])})):[e._v(" - ")]],2)]),e._v(" "),a("div",{staticClass:"item"},[a("div",[e._v("备注：")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(e.userDetailData.mark||"-"))])])])])])]):e._e()])])],1)}),m=[],h={name:"detailUser",props:{userNo:{type:Number,default:0}},data:function(){return{dialogUserDetail:!1,userDetailData:{}}},methods:{handleClose:function(){this.dialogUserDetail=!1},getUserDetail:function(e){var t=this;Object(l["g"])(e).then((function(e){t.userDetailData=e}))}}},_=h,p=(a("81e5"),Object(d["a"])(_,u,m,!1,null,"35183c4c",null)),g=p.exports,f=a("e350"),b=a("61f7"),C={name:"UserIndex",components:{detailUser:g},filters:{sexFilter:function(e){var t={0:"未知",1:"男",2:"女",3:"保密"};return t[e]},typeFilter:function(e){var t={facebook:"Facebook",twitter:"Twitter",google:"Google",email:"Email",phone:"Phone",visitor:"游客"};return t[e]}},data:function(){return{registerTypeList:[{value:"wechat",label:"公众号"},{value:"routine",label:"小程序"},{value:"h5",label:"H5"},{value:"iosWx",label:"微信ios"},{value:"androidWx",label:"微信安卓"},{value:"ios",label:"ios"}],tableHeight:0,formExtension:{image:"",spreadUid:"",userId:""},ruleInline:{},extensionVisible:!1,userVisible:!1,levelInfo:"",pickerOptions:this.$timeOptions,loadingBtn:!1,PointValidateForm:{integralType:2,integralValue:0,moneyType:2,moneyValue:0,uid:""},loadingPoint:!1,VisiblePoint:!1,visible:!1,userIds:"",dialogVisible:!1,levelVisible:!1,levelData:[],groupData:[],labelData:[],selData:[],labelPosition:"right",collapse:!1,props:{children:"child",label:"name",value:"name",emitPath:!1},propsCity:{children:"child",label:"name",value:"name"},listLoading:!0,tableData:{data:[],total:0},nikename:"",userFrom:{registerType:"",sex:"",dateLimit:"",nikename:"",page:1,limit:15,phone:""},grid:{xl:8,lg:12,md:12,sm:24,xs:24},levelList:[],labelLists:[],groupList:[],selectedData:[],timeVal:[],dynamicValidateForm:{groupId:[]},loading:!1,groupIdFrom:[],selectionList:[],batchName:"",uid:0,Visible:!1,keyNum:0,address:[],multipleSelectionAll:[],idKey:"uid",card_select_show:!1,checkAll:!1,checkedCities:["ID","头像","昵称","用户等级","分组","推荐人","手机号","余额","积分"],columnData:["ID","头像","昵称","用户等级","分组","推荐人","手机号","余额","积分"],isIndeterminate:!0}},created:function(){document.documentElement.clientHeight||document.body.clientHeight},activated:function(){this.userFrom.nikename="",this.getList(1)},mounted:function(){var e=this;this.getList(),this.$nextTick((function(){var t=e.$refs.tableheader.offsetHeight;e.tableHeight=e.$selfUtil.getTableHeight(t+100)}))},methods:{checkPermi:f["a"],filterRegisterType:function(e){var t={wechat:"#FD5ACC",routine:"#A277FF",h5:"#E8B600",iosWx:"#1BBE6B",androidWx:"#EF9C20",ios:"#1890FF"};return t[e]},onCollapse:function(){var e=this;this.collapse=!this.collapse,this.$nextTick((function(){var t=e.$refs.tableheader.offsetHeight;e.tableHeight=e.$selfUtil.getTableHeight(t+150)}))},getTemplateRow:function(e){this.formExtension.image=e.avatar,this.formExtension.spreadUid=e.uid},handleCloseExtension:function(){this.extensionVisible=!1},modalPicTap:function(){this.userVisible=!0},resetForm:function(){this.visible=!1},reset:function(e){this.userFrom={userType:"",sex:"",phone:"",dateLimit:"",nikename:"",page:1,limit:15},this.nikename="",this.levelData=[],this.groupData=[],this.labelData=[],this.timeVal=[],this.getList()},sendNews:function(){if(0===this.selectionList.length)return this.$message.warning("请先选择用户");this.$modalArticle((function(e){}),"send")},onSend:function(){if(0===this.selectionList.length)return this.$message.warning("请选择要设置的用户");var e=this;this.$modalCoupon("send",this.keyNum+=1,[],(function(t){e.formValidate.give_coupon_ids=[],e.couponData=[],t.map((function(t){e.formValidate.give_coupon_ids.push(t.coupon_id),e.couponData.push(t.title)})),e.selectionList=[]}),this.userIds,"user")},Close:function(){this.Visible=!1,this.levelVisible=!1},onDetails:function(e){this.$refs.userDetailFrom.getUserDetail(e),this.$refs.userDetailFrom.dialogUserDetail=!0},editPoint:function(e){this.uid=e,this.VisiblePoint=!0},submitPointForm:Object(b["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.PointValidateForm.uid=t.uid,t.loadingBtn=!0,foundsApi(t.PointValidateForm).then((function(e){t.$message.success("设置成功"),t.loadingBtn=!1,t.handlePointClose(),t.getList()})).catch((function(){t.loadingBtn=!1}))}))})),handlePointClose:function(){this.VisiblePoint=!1,this.PointValidateForm={integralType:2,integralValue:0,moneyType:2,moneyValue:0,uid:""}},handleClose:function(){this.dialogVisible=!1,this.$refs["dynamicValidateForm"].resetFields()},userSearchs:function(){this.userFrom.page=1,this.getList()},changeCountry:function(){"OTHER"!==this.userFrom.country&&this.userFrom.country||(this.selectedData=[],this.userFrom.province="",this.userFrom.city="",this.address=[])},handleChange:function(e){this.userFrom.province=e[0],this.userFrom.city=e[1]},onchangeTime:function(e){this.timeVal=e,this.userFrom.dateLimit=e?this.timeVal.join(","):""},getList:function(e){var t=this;this.listLoading=!0,this.userFrom.page=e||this.userFrom.page,this.userFrom.nikename=encodeURIComponent(this.nikename),this.userFrom.groupId=this.groupData.join(","),this.userFrom.labelId=this.labelData.join(","),Object(l["h"])(this.userFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(){t.listLoading=!1})),this.checkedCities=this.$cache.local.has("user_stroge")?this.$cache.local.getJSON("user_stroge"):this.checkedCities,this.$set(this,"card_select_show",!1)},pageChange:function(e){var t=this;this.$selfUtil.changePageCoreRecordData(this.multipleSelectionAll,this.multipleSelection,this.tableData.data,(function(e){t.multipleSelectionAll=e})),this.userFrom.page=e,this.getList()},handleSizeChange:function(e){var t=this;this.$selfUtil.changePageCoreRecordData(this.multipleSelectionAll,this.multipleSelection,this.tableData.data,(function(e){t.multipleSelectionAll=e})),this.userFrom.limit=e,this.getList()},renderHeader:function(e){return e("p",[e("span",{style:"padding-right:5px;"},["操作"])])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCheckAllChange:function(e){this.checkedCities=e?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.columnData.length,this.isIndeterminate=t>0&&t<this.columnData.length},checkSave:function(){this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("user_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)}}},D=C,w=(a("c807"),Object(d["a"])(D,i,s,!1,null,"0a410c32",null));t["default"]=w.exports},c807:function(e,t,a){"use strict";a("086b")},fef0:function(e,t,a){}}]);