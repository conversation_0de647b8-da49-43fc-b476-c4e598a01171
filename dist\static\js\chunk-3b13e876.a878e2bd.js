(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3b13e876"],{"2c32":function(t,e,a){},"321f":function(t,e,a){"use strict";a("9279")},3680:function(t,e,a){},"549e":function(t,e,a){"use strict";a("2c32")},9279:function(t,e,a){},e6e2:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t.checkPermi(["merchant:product:tabs:headers"])?a("el-tabs",{on:{"tab-click":function(e){t.getList(1),t.good<PERSON>eade()}},model:{value:t.tableFrom.type,callback:function(e){t.$set(t.tableFrom,"type",e)},expression:"tableFrom.type"}},t._l(t.headeNum,(function(t,e){return a("el-tab-pane",{key:e,attrs:{label:t.name+"("+t.count+")",name:t.type.toString()}})})),1):t._e(),t._v(" "),a("div",{staticClass:"container mt-1"},[a("el-form",{attrs:{inline:"",size:"small"}},[a("el-form-item",{attrs:{label:"平台端商品分类："}},[a("el-cascader",{staticClass:"selWidth mr20",attrs:{options:t.adminProductClassify,props:t.props1,clearable:"",size:"small"},on:{change:t.seachList},model:{value:t.tableFrom.categoryId,callback:function(e){t.$set(t.tableFrom,"categoryId",e)},expression:"tableFrom.categoryId"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商户商品分类："}},[a("el-cascader",{staticClass:"selWidth mr20",attrs:{options:t.merProductClassify,props:t.props1,clearable:"",size:"small"},on:{change:t.seachList},model:{value:t.tableFrom.cateId,callback:function(e){t.$set(t.tableFrom,"cateId",e)},expression:"tableFrom.cateId"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"商品搜索："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入商品名称，关键字",size:"small",clearable:""},model:{value:t.keywords,callback:function(e){t.keywords=e},expression:"keywords"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:page:list"],expression:"['merchant:product:page:list']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)],1)],1),t._v(" "),a("router-link",{attrs:{to:{path:"/product/list/creatProduct/0/2/2"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:save"],expression:"['merchant:product:save']"}],staticClass:"mr10",attrs:{size:"small",type:"primary"}},[t._v("添加商品")])],1),t._v(" "),a("router-link",{attrs:{to:{path:"/product/list/creatProduct/0/2/1"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:import:product"],expression:"['merchant:product:import:product']"}],staticClass:"mr10",attrs:{size:"small",type:"success"}},[t._v("商品采集")])],1)],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[a("el-form-item",{attrs:{label:"收藏："}},[a("span",[t._v(t._s(e.row.collectCount))])]),t._v(" "),a("el-form-item",{attrs:{label:"虚拟销量："}},[a("span",[t._v(t._s(e.row.ficti))])]),t._v(" "),7==t.tableFrom.type?a("el-form-item",{attrs:{label:"拒绝原因："}},[a("span",[t._v(t._s(e.row.reason))])]):t._e()],1)]}}])}),t._v(" "),t.checkedCities.includes("ID")?a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}):t._e(),t._v(" "),t.checkedCities.includes("商品图")?a("el-table-column",{attrs:{label:"商品图","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.image,"preview-src-list":[t.row.image]}})],1)]}}],null,!1,2331550732)}):t._e(),t._v(" "),t.checkedCities.includes("商品名称")?a("el-table-column",{attrs:{label:"商品名称",prop:"name","min-width":"200","show-overflow-tooltip":!0}}):t._e(),t._v(" "),t.checkedCities.includes("商品售价")?a("el-table-column",{attrs:{prop:"price",label:"商品售价","min-width":"90",align:"center"}}):t._e(),t._v(" "),t.checkedCities.includes("销量")?a("el-table-column",{attrs:{prop:"sales",label:"销量","min-width":"90",align:"center"}}):t._e(),t._v(" "),t.checkedCities.includes("库存")?a("el-table-column",{attrs:{prop:"stock",label:"库存","min-width":"90",align:"center"}}):t._e(),t._v(" "),"7"===t.tableFrom.type?a("el-table-column",{attrs:{label:"失败原因","min-width":"150","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"fontColor3"},[t._v(t._s(e.row.reason))])]}}],null,!1,834877049)}):t._e(),t._v(" "),t.checkedCities.includes("状态")?a("el-table-column",{attrs:{label:"状态","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return t.checkPermi(["merchant:product:up","merchant:product:down"])?[a("el-switch",{attrs:{disabled:1!==Number(t.tableFrom.type)&&4!==Number(t.tableFrom.type)&&(1===e.row.auditStatus||3===e.row.auditStatus),"active-value":!0,"inactive-value":!1,"active-text":"上架","inactive-text":"下架"},on:{change:function(a){return t.onchangeIsShow(e.row)}},model:{value:e.row.isShow,callback:function(a){t.$set(e.row,"isShow",a)},expression:"scope.row.isShow"}})]:void 0}}],null,!0)}):t._e(),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center","render-header":t.renderHeader},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:info"],expression:"['merchant:product:info']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.goDetail(e.row.id)}}},[t._v("详情")]),t._v(" "),a("router-link",{attrs:{to:{path:"/product/list/creatProduct/"+e.row.id+"/2"}}},["5"!==t.tableFrom.type&&"1"!==t.tableFrom.type&&"6"!==t.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:update"],expression:"['merchant:product:update']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[t._v("编辑")]):t._e()],1),t._v(" "),"2"===t.tableFrom.type&&1===e.row.auditStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:submit:audit"],expression:"['merchant:product:submit:audit']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleAudit(e.row.id)}}},[t._v("提审")]):t._e(),t._v(" "),"5"===t.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:restore"],expression:"['merchant:product:restore']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleRestore(e.row.id,e.$index)}}},[t._v("恢复商品")]):t._e(),t._v(" "),1!==Number(t.tableFrom.type)?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:delete"],expression:"['merchant:product:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleDelete(e.row.id,t.tableFrom.type)}}},[t._v(t._s("5"===t.tableFrom.type?"删除":"加入回收站"))]):t._e(),t._v(" "),"1"===t.tableFrom.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:quick:stock:add"],expression:"['merchant:product:quick:stock:add']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleEdit(e.row.id,!0)}}},[t._v("编辑库存")]):t._e(),t._v(" "),"2"===t.tableFrom.type&&2==e.row.auditStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:review:free:edit"],expression:"['merchant:product:review:free:edit']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleEdit(e.row.id,!1)}}},[t._v("免审编辑")]):t._e()]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.card_select_show,expression:"card_select_show"}],staticClass:"card_abs"},[[a("div",{staticClass:"cell_ht"},[a("el-checkbox",{attrs:{indeterminate:t.isIndeterminate},on:{change:t.handleCheckAllChange},model:{value:t.checkAll,callback:function(e){t.checkAll=e},expression:"checkAll"}},[t._v("全选")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.checkSave()}}},[t._v("保存")])],1),t._v(" "),a("el-checkbox-group",{on:{change:t.handleCheckedCitiesChange},model:{value:t.checkedCities,callback:function(e){t.checkedCities=e},expression:"checkedCities"}},t._l(t.columnData,(function(e){return a("el-checkbox",{key:e,staticClass:"check_cell",attrs:{label:e}},[t._v(t._s(e))])})),1)]],2),t._v(" "),a("el-drawer",{attrs:{title:"编辑库存",visible:t.drawer,direction:t.direction,size:1280,"before-close":t.handleCloseEdit},on:{"update:visible":function(e){t.drawer=e}}},[t.drawer?a("store-edit",{attrs:{productId:t.productId,stockEdit:t.stockEdit},on:{subSuccess:t.subSuccess}}):t._e()],1),t._v(" "),a("product-info",{ref:"productInfo",attrs:{"is-show":t.isShow,productId:t.productDetailId},on:{close:t.closeProductInfo}})],1)},i=[],n=a("c4c8"),o=(a("5f87"),a("e350")),s=a("2f62"),l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"mr10"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"formValidate",staticClass:"formValidate",attrs:{model:t.formValidate,"label-width":"130px",rules:t.rules},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-row",{attrs:{gutter:24}},[t.stockEdit?t._e():a("el-col",{attrs:{span:24}}),t._v(" "),a("el-col",{staticClass:"mt10",attrs:{span:24}})],1),t._v(" "),t.stockEdit?t._e():a("el-form-item",{attrs:{label:"商户商品分类：",prop:"cateIds"}},[a("el-cascader",{staticClass:"selWidth",attrs:{options:t.merProductClassify,props:t.props2,clearable:"","show-all-levels":!1},model:{value:t.formValidate.cateIds,callback:function(e){t.$set(t.formValidate,"cateIds",e)},expression:"formValidate.cateIds"}})],1),t._v(" "),a("el-form-item",{staticClass:"labeltop",attrs:{label:"商品属性："}},[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.ManyAttrValue,border:"",size:"mini"}},[t.manyTabDate?t._l(t.manyTabDate,(function(e,r){return a("el-table-column",{key:r,attrs:{align:"center",label:t.manyTabTit[r].title},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[r])}})]}}],null,!0)})})):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"图片",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox"},[e.row.image?a("div",{staticClass:"pictrue tabPic"},[a("img",{attrs:{src:e.row.image}})]):t._e()])]}}])}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["售价（元）"!==t.formThead[r].title&&"库存"!==t.formThead[r].title||t.stockEdit?a("span",[t._v(t._s(e.row[r]))]):a("el-input",{staticClass:"priceBox",attrs:{type:"number",min:0},nativeOn:{keyup:function(a){return t.keyupEvent(r,e.row[r],e.$index)}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)})})),t._v(" "),t.stockEdit?a("el-table-column",{attrs:{align:"center",label:"增加库存","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form-item",{staticClass:"all"},[a("el-input",{staticClass:"priceBox",attrs:{type:"number",min:0},nativeOn:{keyup:function(a){return t.keyupEvent("stockAdd",e.row.stockAdd,e.$index)}},model:{value:e.row.stockAdd,callback:function(a){t.$set(e.row,"stockAdd",a)},expression:"scope.row.stockAdd"}})],1)]}}],null,!1,1205030164)}):t._e()],2)],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("formValidate")}}},[t._v(t._s(t.loadingBtn?"提交中 ...":"确 定"))])],1)],1)],1)},c=[];function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var t={},e=Object.prototype,a=e.hasOwnProperty,r=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},n=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(j){l=function(t,e,a){return t[e]=a}}function c(t,e,a,i){var n=e&&e.prototype instanceof f?e:f,o=Object.create(n.prototype),s=new S(i||[]);return r(o,"_invoke",{value:C(t,a,s)}),o}function m(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(j){return{type:"throw",arg:j}}}t.wrap=c;var h={};function f(){}function p(){}function v(){}var g={};l(g,n,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(x([])));y&&y!==e&&a.call(y,n)&&(g=y);var w=v.prototype=f.prototype=Object.create(g);function _(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function i(r,n,o,s){var l=m(t[r],t,n);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==u(d)&&a.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,o,s)}),(function(t){i("throw",t,o,s)})):e.resolve(d).then((function(t){c.value=t,o(c)}),(function(t){return i("throw",t,o,s)}))}s(l.arg)}var n;r(this,"_invoke",{value:function(t,a){function r(){return new e((function(e,r){i(t,a,e,r)}))}return n=n?n.then(r,r):r()}})}function C(t,e,a){var r="suspendedStart";return function(i,n){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw n;return O()}for(a.method=i,a.arg=n;;){var o=a.delegate;if(o){var s=V(o,a);if(s){if(s===h)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var l=m(t,e,a);if("normal"===l.type){if(r=a.done?"completed":"suspendedYield",l.arg===h)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r="completed",a.method="throw",a.arg=l.arg)}}}function V(t,e){var a=e.method,r=t.iterator[a];if(void 0===r)return e.delegate=null,"throw"===a&&t.iterator.return&&(e.method="return",e.arg=void 0,V(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),h;var i=m(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var n=i.arg;return n?n.done?(e[t.resultName]=n.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function x(t){if(t){var e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(a.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},_(k.prototype),l(k.prototype,o,(function(){return this})),t.AsyncIterator=k,t.async=function(e,a,r,i,n){void 0===n&&(n=Promise);var o=new k(c(e,a,r,i),n);return t.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},_(w),l(w,s,"Generator"),l(w,n,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=x,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(a,r){return o.type="throw",o.arg=t,e.next=a,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],o=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var s=a.call(n,"catchLoc"),l=a.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,h):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),P(a),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var i=r.arg;P(a)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:x(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),h}},t}function m(t,e,a,r,i,n,o){try{var s=t[n](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(r,i)}function h(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var n=t.apply(e,a);function o(t){m(n,r,i,o,s,"next",t)}function s(t){m(n,r,i,o,s,"throw",t)}o(void 0)}))}}function f(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function p(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?f(Object(a),!0).forEach((function(e){v(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):f(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function v(t,e,a){return e=g(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function g(t){var e=b(t,"string");return"symbol"===u(e)?e:String(e)}function b(t,e){if("object"!==u(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var y={cateIds:[],cateId:null,attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0,brokerage:0,brokerageTwo:0}],attr:[],specType:!1},w={price:{title:"售价（元）"},cost:{title:"成本价（元）"},otPrice:{title:"原价（元）"},stock:{title:"库存"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"},brokerage:{title:"一级返佣(%)"},brokerageTwo:{title:"二级返佣(%)"}},_={name:"edit",props:{productId:{type:Number||String,default:""},stockEdit:{type:Boolean,default:!1}},data:function(){return{props2:{children:"childList",label:"name",value:"id",multiple:!0,emitPath:!1,checkStrictly:!0},rules:{cateIds:[{required:!0,message:"请选择商户商品分类",trigger:"change",type:"array",min:"1"}],stockAdd:[{required:!0,message:"请输入年龄",trigger:"blur"}]},formValidate:Object.assign({},y),formThead:Object.assign({},w),isAttr:!1,OneattrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0,stockAdd:0}],ManyAttrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,weight:0,volume:0,stockAdd:0}],manyTabTit:{},manyTabDate:{},loading:!1,attrInfo:{},loadingBtn:!1}},computed:p(p({},Object(s["b"])(["merProductClassify"])),{},{attrValue:function(){var t=Object.assign({},y.attrValue[0]);return delete t.image,t}}),watch:{"formValidate.attr":{handler:function(t){this.formValidate.specType&&this.isAttr&&this.watCh(t)},immediate:!1,deep:!0}},created:function(){this.tempRoute=Object.assign({},this.$route),this.formValidate.specType&&this.$watch("formValidate.attr",this.watCh)},mounted:function(){this.getInfo()},methods:{keyupEvent:function(t,e,a){this.$forceUpdate();var r=/^\D*([0-9]\d*\.?\d{0,2})?.*$/;this.ManyAttrValue[a][t]="stock"===t||"stockAdd"===t?parseInt(e):this.$set(this.ManyAttrValue[a],t,e.replace(r,"$1"))},onSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;if(e.stockEdit){var a=[];e.formValidate.attrValue.forEach((function(t,e){a.push({id:t.id,stock:t.stockAdd?t.stockAdd:0})})),Object(n["K"])({attrValueList:a,id:e.productId}).then((function(t){e.$message.success("操作成功"),e.$emit("subSuccess"),e.loadingBtn=!1})).catch((function(t){e.loadingBtn=!1}))}else{var r=[];e.formValidate.cateId=e.formValidate.cateIds.join(","),e.formValidate.attrValue.forEach((function(t,e){r.push({id:t.id,stock:t.stock,price:t.price})})),Object(n["G"])({attrValue:r,id:e.productId,cateId:e.formValidate.cateId}).then((function(t){e.$message.success("操作成功"),e.$emit("subSuccess"),e.loadingBtn=!1})).catch((function(t){e.loadingBtn=!1}))}}))},watCh:function(t){var e=this,a={},r={};this.formValidate.attr.forEach((function(t,e){a[t.attrName]={title:t.attrName},r[t.attrName]=""})),this.ManyAttrValue=this.attrFormat(t),this.ManyAttrValue.forEach((function(t,a){var r=Object.values(t.attrValue).sort().join("/");e.attrInfo[r]&&(e.ManyAttrValue[a]=e.attrInfo[r])})),this.attrInfo=[],this.ManyAttrValue.forEach((function(t){e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t})),this.manyTabTit=a,this.manyTabDate=r,this.formThead=Object.assign({},this.formThead,a)},attrFormat:function(t){var e=[],a=[];return r(t);function r(t){if(t.length>1)t.forEach((function(r,i){0===i&&(e=t[i]["attrValue"]);var n=[];e&&(e.forEach((function(e){t[i+1]&&t[i+1]["attrValue"]&&t[i+1]["attrValue"].forEach((function(r){var o=(0!==i?"":t[i]["attrName"]+"_")+e+"$&"+t[i+1]["attrName"]+"_"+r;if(n.push(o),i===t.length-2){var s={image:"",price:0,cost:0,otPrice:0,barCode:"",stock:0,weight:0,volume:0,brokerage:0,brokerageTwo:0};for(var l in o.split("$&").forEach((function(t,e){var a=t.split("_");s["attrValue"]||(s["attrValue"]={}),s["attrValue"][a[0]]=a.length>1?a[1]:""})),s.attrValue)s[l]=s.attrValue[l];a.push(s)}}))})),e=n.length?n:[])}));else{var r=[];t.forEach((function(t,e){t["attrValue"].forEach((function(e,i){for(var n in r[i]=t["attrName"]+"_"+e,a[i]={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerageTwo:0,attrValue:v({},t["attrName"],e)},a[i].attrValue)a[i][n]=a[i].attrValue[n]}))})),e.push(r.join("$&"))}return a}},getInfo:function(){var t=this;this.loading=!0,Object(n["E"])(this.productId).then(function(){var e=h(d().mark((function e(a){var r,i,n,o;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.isAttr=!0,r=a,t.formValidate={attr:r.attr,attrValue:r.attrValue,specType:r.specType,cateIds:r.cateId.split(","),cateId:r.cateId},r.specType?(t.formValidate.attr=r.attr.map((function(t){return{attrName:t.attrName,attrValue:t.attrValues.split(",")}})),r.attrValue.forEach((function(e){e.image=t.$selfUtil.setDomain(e.image),e.attrValue=JSON.parse(e.attrValue),t.attrInfo[Object.values(e.attrValue).sort().join("/")]=e})),t.ManyAttrValue=r.attrValue,i=t.attrFormat(t.formValidate.attr),i.length!==t.ManyAttrValue.length?(t.$set(t,"showAll",!0),t.isAttr=!1):t.isAttr=!0,n={},o={},t.formValidate.attr.forEach((function(t,e){n[t.attrName]={title:t.attrName},o[t.attrName]=""})),t.formValidate.attrValue.forEach((function(t){for(var e in t.attrValue)t[e]=t.attrValue[e]})),t.manyTabTit=n,t.manyTabDate=o,t.formThead=Object.assign({},t.formThead,n)):t.ManyAttrValue=r.attrValue,t.loading=!1;case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.loading=!1}))}}},k=_,C=(a("549e"),a("2877")),V=Object(C["a"])(k,l,c,!1,null,"4ab2482c",null),I=V.exports,P=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"infoBox"},[a("el-drawer",{ref:"drawer",staticClass:"infoBox",attrs:{visible:t.dialogVisible,title:"商品详情",direction:t.direction,"append-to-body":!0,"custom-class":"demo-drawer",size:"1000px",wrapperClosable:!0,"modal-append-to-body":!1},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[a("div",{staticClass:"demo-drawer__content"},[t.formValidate?a("div",{staticClass:"divBox"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-tabs",{model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},[a("el-tab-pane",{attrs:{label:"商品信息",name:"0"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"商品详情",name:"1"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"其他设置",name:"2"}})],1)],1),t._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:"0"===t.currentTab,expression:"currentTab === '0'"}]},[a("el-row",{attrs:{gutter:24}},[a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("商品名称：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.name))])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("平台商品分类：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.adminCategory))])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("品牌：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.brandName))])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("商品关键字：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.keyword))])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("单位：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.unitName))])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("商品简介：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.intro))])]),t._v(" "),a("el-col",{staticClass:"mb10",attrs:{span:24}},[a("div",{staticClass:"title"},[t._v("商品封面图：")]),t._v(" "),t.formValidate.image?a("div",{staticClass:"acea-row"},[a("div",{staticClass:"pictrue"},[a("el-image",{attrs:{src:t.formValidate.image,"preview-src-list":[t.formValidate.image]}})],1)]):a("div",[t._v("无")])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("保障服务：")]),t._v(" "),a("div",{staticClass:"value"},t._l(t.guarantees,(function(e){return a("el-tag",{key:e.id,staticStyle:{"margin-right":"8px"}},[t._v("\n                    "+t._s(e.name)+"\n                  ")])})),1)]),t._v(" "),a("el-col",{staticClass:"mb10",staticStyle:{"margin-top":"16px"},attrs:{span:24}},[a("div",{staticClass:"title"},[t._v("商品轮播图：")]),t._v(" "),t.formValidate.sliderImages.length>0?a("div",{staticClass:"acea-row"},t._l(t.formValidate.sliderImages,(function(e,r){return a("div",{key:r,staticClass:"pictrue"},[a("el-image",{attrs:{src:e,"preview-src-list":t.formValidate.sliderImages}})],1)})),0):a("div",{staticClass:"value"},[t._v("无")])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("商品规格：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.specType?"多规格":"单规格"))])]),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("div",{staticClass:"title"},[t._v("佣金设置：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.isSub?"单独设置":"默认设置"))])]),t._v(" "),a("el-col",{staticClass:"mt10",staticStyle:{"margin-top":"16px"},attrs:{span:24}},[a("div",{staticClass:"title"},[t._v("商品属性：")]),t._v(" "),a("div",{staticClass:"value"},[!1===t.formValidate.specType?[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.OneAttrValue,border:"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"图片",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox"},[t.formValidate.image?a("div",{staticClass:"pictrue tabPic"},[a("el-image",{staticStyle:{width:"100% !important",height:"100% !important"},attrs:{src:e.row.image,fit:"contain","preview-src-list":[e.row.image]}})],1):a("div",{staticClass:"upLoad tabPic"},[t._v("无")])])]}}],null,!1,4233621924)}),t._v(" "),"eyeglass"==t.type?[a("el-table-column",{attrs:{align:"center",label:"试戴图",width:"60"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"upLoadPicBox"},[t.row.wearImage?a("div",{staticClass:"pictrue tabPic"},[a("el-image",{staticStyle:{width:"100% !important",height:"100% !important"},attrs:{src:t.row.wearImage,fit:"contain","preview-src-list":[t.row.wearImage]}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,3969641883)})]:t._e(),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(""!==e.row[r]?e.row[r]:"无"))])]}}],null,!0)})}))],2)]:t._e(),t._v(" "),t.formValidate.attr.length>0&&t.formValidate.specType?[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.ManyAttrValue,border:"",size:"mini"}},[t.moreTitles?t._l(t.moreTitles,(function(e,r){return a("el-table-column",{key:r,attrs:{align:"center",label:e.title},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[r])}})]}}],null,!0)})})):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"图片",width:"60"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"upLoadPicBox"},[t.row.image?a("div",{staticClass:"pictrue tabPic"},[a("el-image",{staticStyle:{width:"100% !important",height:"100% !important"},attrs:{src:t.row.image,fit:"contain","preview-src-list":[t.row.image]}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,1274693018)}),t._v(" "),"eyeglass"==t.type?[a("el-table-column",{attrs:{align:"center",label:"试戴图",width:"60"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"upLoadPicBox"},[t.row.wearImage?a("div",{staticClass:"pictrue tabPic"},[a("el-image",{staticStyle:{width:"100% !important",height:"100% !important"},attrs:{src:t.row.wearImage,fit:"contain","preview-src-list":[t.row.wearImage]}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,3969641883)})]:t._e(),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(""!==e.row[r]?e.row[r]:"无"))])]}}],null,!0)})}))],2)]:t._e()],2)])],1)],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.currentTab,expression:"currentTab === '1'"}]},[a("div",{staticClass:"description-item"},[a("span",{staticStyle:{"text-align":"center"},domProps:{innerHTML:t._s(t.formValidate.content||"无")}})])]),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.currentTab,expression:"currentTab === '2'"}]},[a("el-row",{attrs:{gutter:24}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"title"},[t._v("排序：")]),t._v(" "),a("div",{staticClass:"value"},[t._v(t._s(t.formValidate.sort))])]),t._v(" "),a("el-col",{attrs:{span:24}},[a("div",{staticClass:"title"},[t._v("优惠券：")]),t._v(" "),a("div",{staticClass:"value"},[t._l(t.formValidate.couponList,(function(e,r){return a("el-tag",{key:r,staticClass:"mr10 mb10",attrs:{"disable-transitions":!1}},[t._v("\n                    "+t._s(e.name)+"\n                  ")])})),t._v(" "),0===t.formValidate.couponList.length?a("span",[t._v("无")]):t._e()],2)])],1)],1)])]):t._e()])])],1)},S=[],x=a("ed08");function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function j(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function L(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?j(Object(a),!0).forEach((function(e){E(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):j(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function E(t,e,a){return e=T(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function T(t){var e=N(t,"string");return"symbol"===O(e)?e:String(e)}function N(t,e){if("object"!==O(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==O(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var $={image:"",sliderImages:[],videoLink:"",name:"",intro:"",keyword:"",couponList:[],unitName:"",sort:0,giveIntegral:0,ficti:0,isShow:!1,attrValue:[{image:"",smin:"0.00",smax:"0.00",cmin:"0.00",cmax:"0.00",price:0,cost:0,otPrice:0,spec:"",stock:0,weight:0,volume:0,brokerage:0,brokerageTwo:0}],attr:[],selectRule:"",isSub:!1,content:"",specType:!1,id:void 0,couponIds:[],coupons:[],postage:"1",categoryId:"",guaranteeIds:"",guaranteeIdsList:[],brandId:"",brandName:""},A={price:{title:"售价（元）",type:"number"},cost:{title:"成本价（元）",type:"number"},otPrice:{title:"原价（元）",type:"number"},stock:{title:"库存",type:"int"},barCode:{title:"商品编号",type:"int"},spec:{title:"镜架参数",type:"string"},weight:{title:"重量（KG）",type:"number"},volume:{title:"体积(m³)",type:"number"},brokerage:{title:"一级返佣(%)",type:"number"},brokerageTwo:{title:"二级返佣(%)",type:"number"},smin:{title:"可配最大度数（近视）",type:"int"},smax:{title:"可配最大度数（远视）",type:"int"},cmin:{title:"适用最低散光",type:"int"},cmax:{title:"适用最高散光",type:"int"}},D={name:"ProductInfo",props:{isShow:{type:Boolean,default:!1},productId:{type:[Number,String],default:function(){return null}}},data:function(){return{dialogVisible:!1,direction:"rtl",OneAttrValue:[],ManyAttrValue:[],formThead:Object.assign({},A),formValidate:Object.assign({},$),moreTitles:{},currentTab:"0",isAttr:!1,showAll:!1,videoLink:"",guaranteeList:[],brandList:[],search:{limit:10,page:1,cid:"",brandName:""},loading:!1,brand:"",category:"",grid2:{xl:12,lg:12,md:12,sm:24,xs:24},type:"others"}},computed:L(L({},Object(s["b"])(["adminProductClassify","adminEyeglassClassify","adminLensClassify","merProductClassify"])),{},{attrValue:function(){var t=Object.assign({},$.attrValue[0]);return delete t.image,"lens"!==this.type&&(delete t.smin,delete t.smax,delete t.cmin,delete t.cmax),"eyeglass"!=this.type&&delete t.spec,this.formValidate.isSub||(delete t.brokerage,delete t.brokerageTwo),t},guarantees:function(){var t=this;return this.guaranteeList.filter((function(e){return t.formValidate.guaranteeIdsList.includes(e.id)}))},adminCategory:function(){var t=this.getChild(this.adminProductClassify,this.formValidate.categoryId);return t&&t.name}}),watch:{productId:{handler:function(t){null!=t&&(this.currentTab="0",this.getInfo(t))},immediate:!1,deep:!0},isShow:{handler:function(t){this.dialogVisible=t}}},created:function(){this.currentTab="0",this.adminProductClassify.length||this.$store.dispatch("product/getAdminProductClassify")},mounted:function(){this.getProductGuarantee()},methods:{computeType:function(){var t=this,e=this.adminEyeglassClassify.find((function(e){return e.id===t.formValidate.categoryId}));if(e)this.type="eyeglass";else{var a=this.adminLensClassify.find((function(e){return e.id===t.formValidate.categoryId}));this.type=a?"lens":"others"}},close:function(){this.$emit("close")},getProductGuarantee:function(){var t=this;Object(n["H"])().then((function(e){t.guaranteeList=e}))},getbrandList:function(){var t=this;this.search.cid=this.formValidate.categoryId,Object(n["e"])(this.search).then((function(e){t.brandList=e.list;var a=t.brandList.find((function(e){return e.id==t.formValidate.brandId}));t.formValidate.brandName=a?a.name:"其他"}))},getChild:function(t,e){for(var a=0,r=t.length;a<r;a++){if(t[a].id==e)return t[a];if(Array.isArray(t[a].childList)){var i=this.getChild(t[a].childList,e);if(i)return i}}return null},getInfo:function(t){var e=this;this.loading=!0,Object(n["E"])(t).then((function(t){var a=t,r=JSON.parse(a.sliderImage);r=r.map((function(t){return e.$selfUtil.setDomain(t)}));var i="";if("video"==Object(x["d"])(r[0])&&(i=r.shift()),e.formValidate={image:e.$selfUtil.setDomain(a.image),sliderImages:r,videoLink:i,name:a.name,intro:a.intro,keyword:a.keyword,cateIds:a.cateId.split(","),cateId:a.cateId,unitName:a.unitName,sort:a.sort,isShow:a.isShow,tempId:a.tempId,attr:a.attr,attrValue:a.attrValue,selectRule:a.selectRule,isSub:a.isSub,content:e.$selfUtil.replaceImgSrcHttps(a.content),specType:a.specType,id:a.id,giveIntegral:a.giveIntegral,ficti:a.ficti,coupons:a.coupons,couponIds:a.couponIds,postage:a.postage,brandId:a.brandId,brandName:"",categoryId:a.categoryId,guaranteeIds:a.guaranteeIds,couponList:a.couponList||[],guaranteeIdsList:a.guaranteeIds.split(",").map(Number)},e.computeType(),e.getbrandList(),a.specType){e.formValidate.attr=a.attr.map((function(t){return{attrName:t.attrName,attrValue:t.attrValues.split(",")}})),e.ManyAttrValue=a.attrValue.map((function(t){return t.image=e.$selfUtil.setDomain(t.image),t.wearImage=e.$selfUtil.setDomain(t.wearImage),t.attrValue=JSON.parse(t.attrValue),t}));var n={};e.formValidate.attr.forEach((function(t,e){n[t.attrName]={title:t.attrName,type:"string"}})),e.formValidate.attrValue.forEach((function(t){for(var e in t.attrValue)t[e]=t.attrValue[e]})),e.moreTitles=n,e.formThead=Object.assign({},e.formThead,n)}else e.OneAttrValue=a.attrValue.map((function(t){return t}));e.loading=!1})).catch((function(t){e.loading=!1}))}}},F=D,z=(a("f884"),Object(C["a"])(F,P,S,!1,null,"a3367d7e",null)),B=z.exports;function M(t){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function H(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function G(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?H(Object(a),!0).forEach((function(e){U(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):H(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function U(t,e,a){return e=J(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function J(t){var e=W(t,"string");return"symbol"===M(e)?e:String(e)}function W(t,e){if("object"!==M(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==M(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var R=["出售中","仓库中","已售罄","警戒库存","回收站","待审核","审核失败"],q={name:"ProductList",directives:{selectLoadMore:{bind:function(t,e){var a=t.querySelector(".el-select-dropdown .el-select-dropdown__wrap");a.addEventListener("scroll",(function(){this.scrollHeight-this.scrollTop<this.clientHeight+1&&e.value()}))}}},components:{storeEdit:I,productInfo:B},data:function(){return{drawer:!1,direction:"rtl",props1:{children:"childList",label:"name",value:"id",multiple:!1,emitPath:!1},headeNum:[],listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,cateId:"",keywords:"",type:"1",categoryId:null},keywords:"",categoryList:[],objectUrl:"http://127.0.0.1:8080",card_select_show:!1,checkAll:!1,checkedCities:["ID","商品图","商品名称","商品售价","销量","库存","排序","状态"],columnData:["ID","商品图","商品名称","商品售价","销量","库存","排序","状态"],isIndeterminate:!0,productId:0,stockEdit:!1,isShow:!1,productDetailId:null}},computed:G({},Object(s["b"])(["adminProductClassify","merProductClassify","productBrand"])),activated:function(){this.goodHeade(),this.getList()},mounted:function(){this.goodHeade(),this.getList(),this.checkedCities=this.$cache.local.has("goods_stroge")?this.$cache.local.getJSON("goods_stroge"):this.checkedCities,this.adminProductClassify.length||this.$store.dispatch("product/getAdminProductClassify"),this.merProductClassify.length||this.$store.dispatch("product/getMerProductClassify"),this.productBrand.length||this.$store.dispatch("product/getMerProductBrand")},methods:{checkPermi:o["a"],subSuccess:function(){this.handleCloseEdit()},handleEdit:function(t,e){this.productId=t,this.drawer=!0,this.stockEdit=e},handleCloseEdit:function(){this.drawer=!1,this.seachList()},handleAudit:function(t){var e=this;this.$modalSure("提审商品吗").then((function(){Object(n["v"])(t).then((function(t){e.goodHeade(),e.getList()}))}))},handleRestore:function(t){var e=this;this.$modalSure("恢复商品").then((function(){Object(n["S"])(t).then((function(t){e.$message.success("操作成功"),e.goodHeade(),e.getList()}))}))},seachList:function(){this.tableFrom.page=1,this.getList(),this.goodHeade()},exports:function(){Object(n["F"])({cateId:this.tableFrom.cateId,keywords:this.tableFrom.keywords,type:this.tableFrom.type}).then((function(t){window.location.href=t.fileName}))},goodHeade:function(){var t=this;Object(n["I"])().then((function(e){e.map((function(t){t.name=R[Number(t.type)-1]})),t.headeNum=e})).catch((function(e){t.$message.error(e.message)}))},changeNodes:function(t){if(t.length>0)for(var e=0;e<t.length;e++)!t[e].childList||t[e].childList.length<1?t[e].childList=void 0:this.changeNodes(t[e].childList);return t},getList:function(){var t=this;this.listLoading=!0,this.tableFrom.keywords=encodeURIComponent(this.keywords),Object(n["J"])(this.tableFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e.message)}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()},handleDelete:function(t,e){var a=this;this.$modalSure(5==e?"删除 id 为 ".concat(t," 的商品"):"id为".concat(t,"的商品加入回收站吗")).then((function(){var r=5==e?"delete":"recycle";Object(n["D"])({id:t,type:r}).then((function(){a.$message.success("删除成功"),a.getList(),a.goodHeade()}))}))},onchangeIsShow:function(t){var e=this;t.isShow?Object(n["M"])(t.id).then((function(){e.$message.success("上架成功"),e.getList(),e.goodHeade()})).catch((function(){t.isShow=!t.isShow})):Object(n["t"])(t.id).then((function(){e.$message.success("下架成功"),e.getList(),e.goodHeade()})).catch((function(){t.isShow=!t.isShow}))},renderHeader:function(t){var e=this;return t("p",[t("span",{style:"padding-right:5px;"},["操作"]),t("i",{class:"el-icon-setting",on:{click:function(){return e.handleAddItem()}}})])},handleAddItem:function(){this.card_select_show?this.$set(this,"card_select_show",!1):this.card_select_show||this.$set(this,"card_select_show",!0)},handleCheckAllChange:function(t){this.checkedCities=t?this.columnData:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(t){var e=t.length;this.checkAll=e===this.columnData.length,this.isIndeterminate=e>0&&e<this.columnData.length},checkSave:function(){this.$set(this,"card_select_show",!1),this.$modal.loading("正在保存到本地，请稍候..."),this.$cache.local.setJSON("goods_stroge",this.checkedCities),setTimeout(this.$modal.closeLoading(),1e3)},goDetail:function(t){this.productDetailId=t,this.isShow=!0},closeProductInfo:function(){this.productDetailId=null,this.isShow=!1}}},K=q,Y=(a("321f"),Object(C["a"])(K,r,i,!1,null,"22b18d12",null));e["default"]=Y.exports},f884:function(t,e,a){"use strict";a("3680")}}]);