// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import Layout from '@/layout';

const MerchantStoresRouter = {
  path: '/merchantStores',
  component: Layout,
  redirect: '/merchantStores/list',
  name: 'MerchantStores',
  meta: {
    title: '商品',
    icon: 'clipboard',
  },
  children: [
    {
      path: 'list',
      component: () => import('@/views/merchantStores/index'),
      name: 'MerchantStoresList',
      meta: { title: '门店列表', icon: '' },
    },
  ],
};

export default MerchantStoresRouter;
