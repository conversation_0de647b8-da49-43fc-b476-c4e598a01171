<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :before-close="handleClose" :closeOnClickModal="false">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="demo-ruleForm"
      v-loading="loading"
      :disabled="isView"
    >
      <el-form-item label="门店名称" prop="name">
        <el-input v-model="form.name" style="width: 500px" placeholder="请输入门店名称" />
      </el-form-item>

      <el-form-item label="所属地区" prop="region">
        <el-cascader
          v-model="form.region"
          :options="cityTree"
          :props="cityProps"
          :show-all-levels="true"
          style="width: 500px"
          placeholder="请选择所属地区"
          @change="handleRegionChange"
        />
      </el-form-item>

      <el-form-item label="门店地址" prop="addressDetail">
        <el-input v-model="form.addressDetail" style="width: 500px" placeholder="请输入门店地址" />
      </el-form-item>

      <el-form-item label="经纬度" required>
        <el-row>
          <el-col :span="11">
            <el-form-item prop="longitude">
              <el-input v-model="form.longitude" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2" style="text-align: center">-</el-col>
          <el-col :span="11">
            <el-form-item prop="latitude">
              <el-input v-model="form.latitude" placeholder="请输入纬度" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="营业时间" required>
        <el-row>
          <el-col :span="11">
            <el-form-item prop="businessHoursStart">
              <el-time-picker v-model="form.businessHoursStart" placeholder="开始时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2" style="text-align: center">-</el-col>
          <el-col :span="11">
            <el-form-item prop="businessHoursEnd">
              <el-time-picker v-model="form.businessHoursEnd" placeholder="结束时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="营业状态" prop="openStatus">
        <el-radio-group v-model="form.openStatus">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ isView ? '关 闭' : '取 消' }}</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitForm('form')" v-if="!isView">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { storesAddApi, storesUpdateApi, storesDetailApi } from '@/api/merchantStores';
import { getToken } from '@/utils/auth';
import request from '@/utils/request';
import { cityListTree } from '@/api/logistics';

export default {
  name: 'StoreDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    storeId: {
      type: [String, Number],
      default: '',
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      imageUrl: '',
      loading: false,
      submitLoading: false,
      cityTree: [],
      cityProps: {
        children: 'child',
        label: 'regionName',
        value: 'regionName',
      },
      form: {
        id: '',
        merId: '',
        name: '',
        avatar: '',
        type: 4,
        region: [],
        province: '',
        city: '',
        district: '',
        addressDetail: '',
        latitude: '',
        longitude: '',
        businessHoursStart: '',
        businessHoursEnd: '',
        openStatus: 1,
      },
      rules: {
        name: [{ required: true, message: '请输入门店名称', trigger: 'blur' }],
        // type: [{ required: true, message: '请选择门店类型', trigger: 'change' }],
        region: [{ required: true, message: '请选择所属地区', trigger: 'change' }],
        addressDetail: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
        longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
        businessHoursStart: [{ required: true, message: '请选择营业开始时间', trigger: 'change' }],
        businessHoursEnd: [{ required: true, message: '请选择营业结束时间', trigger: 'change' }],
      },
      typeOptions: [
        { value: 1, label: '直营' },
        { value: 2, label: '旗舰' },
        { value: 3, label: '专卖' },
        { value: 4, label: '普通' },
      ],
    };
  },
  computed: {
    dialogTitle() {
      if (this.isView) return '门店详情';
      return this.storeId ? '编辑门店' : '新增门店';
    },
  },
  watch: {
    dialogVisible(val) {
      if (val && this.storeId) {
        this.getDetail();
      }
    },
    storeId: {
      handler(val) {
        if (val && this.dialogVisible) {
          this.getDetail();
        }
      },
      immediate: true,
    },
    'form.avatar': {
      handler(val) {
        if (val) {
          this.imageUrl = val;
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.getCityList();
  },
  methods: {
    // 获取城市列表
    getCityList() {
      let cityList = localStorage.getItem('cityList');
      if (cityList) {
        cityList = JSON.parse(cityList);
        this.cityTree = cityList;
      } else {
        cityListTree()
          .then((tree) => {
            let cityTree = this.changeNodes(tree);
            localStorage.setItem('cityList', JSON.stringify(cityTree));
            this.cityTree = cityTree;
          })
          .catch((err) => {
            this.$message.error('获取城市列表失败');
          });
      }
    },
    // 处理城市树节点
    changeNodes(data) {
      if (data.length > 0) {
        for (var i = 0; i < data.length; i++) {
          if (!data[i].child || data[i].child.length < 1) {
            data[i].child = undefined;
          } else {
            this.changeNodes(data[i].child);
          }
        }
      }
      return data;
    },
    // 处理地区选择变化
    handleRegionChange(value) {
      if (value && value.length > 0) {
        this.form.province = value[0];
        this.form.city = value[1];
        this.form.district = value[2];
      } else {
        this.form.province = '';
        this.form.city = '';
        this.form.district = '';
      }
    },
    // 设置门店详情数据
    setStoreDetail(detail) {
      console.log('设置门店详情数据', detail);
      if (!detail) {
        this.$message.error('门店详情数据无效');
        return;
      }

      // 设置地区数组
      const region = [];
      if (detail.province) region.push(detail.province);
      if (detail.city) region.push(detail.city);
      if (detail.district) region.push(detail.district);

      this.form = {
        id: detail.id || '',
        merId: detail.merId || '',
        name: detail.name || '',
        avatar: detail.avatar || '',
        type: detail.type || 4,
        region: region,
        province: detail.province || '',
        city: detail.city || '',
        district: detail.district || '',
        addressDetail: detail.addressDetail || '',
        latitude: detail.latitude || '',
        longitude: detail.longitude || '',
        openStatus: detail.openStatus !== undefined ? detail.openStatus : 1,
        merchantName: detail.merchantName || '',
        typeName: detail.typeName || '',
        openStatusName: detail.openStatusName || '',
        businessHours: detail.businessHours || '',
        createTime: detail.createTime || '',
        updateTime: detail.updateTime || '',
      };

      // 处理营业时间
      if (detail.businessHoursStart && detail.businessHoursEnd) {
        const now = new Date();
        const [startHours, startMinutes, startSeconds] = detail.businessHoursStart.split(':');
        const [endHours, endMinutes, endSeconds] = detail.businessHoursEnd.split(':');

        this.form.businessHoursStart = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          parseInt(startHours) || 0,
          parseInt(startMinutes) || 0,
          parseInt(startSeconds) || 0,
        );

        this.form.businessHoursEnd = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          parseInt(endHours) || 0,
          parseInt(endMinutes) || 0,
          parseInt(endSeconds) || 0,
        );
      } else if (detail.businessHours) {
        const times = detail.businessHours.split(' - ');
        if (times.length === 2) {
          const [startTime, endTime] = times;
          const [startHours, startMinutes] = startTime.split(':');
          const [endHours, endMinutes] = endTime.split(':');

          const now = new Date();

          this.form.businessHoursStart = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
            parseInt(startHours) || 0,
            parseInt(startMinutes) || 0,
            0,
          );

          this.form.businessHoursEnd = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
            parseInt(endHours) || 0,
            parseInt(endMinutes) || 0,
            0,
          );
        }
      }
    },
    // 获取门店详情
    getDetail() {
      this.loading = true;
      storesDetailApi(this.storeId)
        .then((res) => {
          this.setStoreDetail(res);
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 头像上传成功
    handleAvatarSuccess(res) {
      this.form.avatar = res.data.src;
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitLoading = true;

          // 处理营业时间
          const formatTime = (date) => {
            if (!date) return '';
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${hours}:${minutes}:${seconds}`;
          };

          const submitData = {
            ...this.form,
            businessHoursStart: formatTime(this.form.businessHoursStart),
            businessHoursEnd: formatTime(this.form.businessHoursEnd),
          };

          const api = this.storeId ? storesUpdateApi : storesAddApi;
          api(submitData)
            .then((res) => {
              this.$message.success(this.storeId ? '编辑门店成功' : '添加门店成功');
              this.handleClose();
              this.$emit('success');
            })
            .catch(() => {
              this.$message.error(this.storeId ? '编辑门店失败' : '添加门店失败');
            })
            .finally(() => {
              this.submitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    // 关闭弹窗
    handleClose() {
      this.$refs['form'].resetFields();
      this.form = {
        id: '',
        merId: '',
        name: '',
        avatar: '',
        type: 4,
        region: [],
        province: '',
        city: '',
        district: '',
        addressDetail: '',
        latitude: '',
        longitude: '',
        businessHoursStart: '',
        businessHoursEnd: '',
        openStatus: 1,
      };
      this.$emit('update:dialogVisible', false);
    },
    uploadImage(event) {
      const file = event.file;
      const formData = new FormData();
      formData.append('multipart', file);
      formData.append('model', 'merchant_stores');
      formData.append('pid', '8');

      const loading = this.$loading({
        lock: true,
        text: '上传中，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      request({
        url: '/admin/platform/upload/image',
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
        .then((res) => {
          loading.close();
          if (res.code === 200) {
            this.imageUrl = res.data.url;
            this.form.avatar = res.data.url;
            this.$message.success('上传成功');
          } else {
            this.$message.error(res.msg || '上传失败');
          }
        })
        .catch((err) => {
          alert(err);
          loading.close();
          this.$message.error('上传失败: ' + (err.message || '未知错误'));
        });
    },
    beforeAvatarUpload(file) {
      const isImage = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('上传头像图片只能是 JPG/PNG/GIF 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isImage && isLt2M;
    },
  },
};
</script>

<style scoped lang="scss">
.line {
  text-align: center;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
