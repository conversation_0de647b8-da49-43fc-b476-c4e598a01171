// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from '@/utils/request';

/**
 * 会员领取记录 列表
 * @param pram
 */
export function couponUserListApi(params) {
  return request({
    url: '/admin/merchant/coupon/user/list',
    method: 'get',
    params,
  });
}

/**
 * 视频号 草稿列表
 */
export function draftListApi(params) {
  return request({
    url: `/admin/merchant/pay/component/draftproduct/draft/list`,
    method: 'get',
    params,
  });
}

/**
 * 视频号 过审商品列表
 */
export function videoProductListApi(params) {
  return request({
    url: `/admin/merchant/pay/component/product/list`,
    method: 'get',
    params,
  });
}

/**
 * 视频号 类目
 */
export function catListApi(params) {
  return request({
    url: `/admin/merchant/pay/component/cat/get/list`,
    method: 'get',
  });
}

/**
 * 视频号 草稿商品添加
 */
export function videoAddApi(data) {
  return request({
    url: `/admin/merchant/pay/component/draftproduct/add`,
    method: 'post',
    data,
  });
}

/**
 * 视频号 过审商品上架
 */
export function videoUpApi(proId) {
  return request({
    url: `/admin/merchant/pay/component/product/puton/${proId}`,
    method: 'post',
  });
}

/**
 * 视频号 过审商品下架
 */
export function videoDownApi(proId) {
  return request({
    url: `/admin/merchant/pay/component/product/putdown/${proId}`,
    method: 'post',
  });
}

/**
 * 视频号 草稿商品删除
 */
export function draftDelApi(id) {
  return request({
    url: `/admin/merchant/pay/component/draftproduct/delete/${id}`,
    method: 'get',
  });
}

/**
 * 视频号 草稿商品详情
 */
export function draftInfoApi(id) {
  return request({
    url: `/admin/merchant/pay/component/draftproduct/draft/get/${id}`,
    method: 'get',
  });
}

/**
 * 视频号 草稿商品编辑
 */
export function draftUpdateApi(data) {
  return request({
    url: `/admin/merchant/pay/component/draftproduct/update`,
    method: 'post',
    data,
  });
}

/**
 * 视频号 草稿商品图片上传至微信
 */
export function shopImgUploadApi(data) {
  return request({
    url: `/admin/merchant/pay/component/shop/img/upload`,
    method: 'post',
    data,
  });
}

/**
 * 视频号 商家审核草稿商品
 */
export function draftReviewApi(data) {
  return request({
    url: `/admin/merchant/pay/component/draftproduct/review`,
    method: 'post',
    data,
  });
}

/**
 * 视频号 品牌列表
 */
export function draftBrandlistApi() {
  return request({
    url: `/admin/merchant/pay/component/shop/brand/usable/list`,
    method: 'get',
  });
}

/**
 * 视频号 过审商品删除
 */
export function payProductDeleteApi(proId) {
  return request({
    url: `/admin/merchant/pay/component/product/delete/${proId}`,
    method: 'get',
  });
}

/**
 * 视频号 过审商品详情
 */
export function payProductGetApi(id) {
  return request({
    url: `/admin/merchant/pay/component/product/get/${id}`,
    method: 'get',
  });
}

/**
 * 平台优惠券列表（用于选择优惠券）
 * @param {Object} params - 查询参数
 * @param {String} params.name - 优惠券名称
 * @param {String} params.status - 优惠券状态
 * @param {Number} params.page - 页码
 * @param {Number} params.limit - 每页数量
 */
export function couponSelectListApi(params) {
  return request({
    url: '/admin/platform/coupon/list',
    method: 'get',
    params,
  });
}
