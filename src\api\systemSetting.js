// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from '@/utils/request';

export function systemConfigCheck(pram) {
  const data = {
    name: pram.name,
  };
  return request({
    url: '/admin/system/config/check',
    method: 'GET',
    params: data,
  });
}

export function systemConfigInfo(pram) {
  const data = {
    formId: pram.id,
  };
  return request({
    url: '/admin/system/config/info',
    method: 'GET',
    params: data,
  });
}

export function systemConfigSave(pram) {
  return request({
    url: '/admin/system/config/save/form',
    method: 'POST',
    data: pram,
  });
}

/**
 * 文件上传
 * @param data
 */
export function fileFileApi(data, params) {
  return request({
    url: '/admin/merchant/upload/file',
    method: 'POST',
    params,
    data,
  });
}

/**
 * 图片上传
 * @param data
 */
export function fileImageApi(data, params) {
  return request({
    url: '/admin/merchant/upload/image',
    method: 'POST',
    params,
    data,
  });
}

/**
 * 图片列表
 * @param data
 */
export function fileListApi(params) {
  return request({
    url: '/admin/merchant/attachment/list',
    method: 'get',
    params,
  });
}

/**
 * 图片列表 删除图片
 * @param data
 */
export function fileDeleteApi(id) {
  return request({
    url: `/admin/merchant/attachment/delete`,
    method: 'post',
    data: { ids: id },
  });
}

/**
 * 图片列表 移動分類
 * @param data
 */
export function attachmentMoveApi(data) {
  return request({
    url: `/admin/merchant/attachment/move`,
    method: 'post',
    data,
  });
}

export function configGetUniq(pram) {
  const data = {
    key: pram.key,
  };
  return request({
    url: '/admin/merchant/config/getuniq',
    method: 'GET',
    params: data,
  });
}
/**
 * 获取商户配置信息
 * @param {Object} params - 查询参数，包含type
 */
export function getMerchantConfigApi(params) {
  return request({
    url: '/admin/merchant/config/getInfo',
    method: 'GET',
    params,
  });
}

/**
 * 更新商户配置
 * @param {Object} data - 配置数据
 */
export function updatePlatformConfigApi(data) {
  return request({
    url: '/admin/merchant/config/update',
    method: 'POST',
    data,
  });
}

/**
 * 广告图片上传
 * @param {FormData} data - 图片文件数据
 * @param {Object} params - 上传参数
 */
export function uploadAdsImageApi(data, params) {
  return request({
    url: 'admin/merchant/upload/image',
    method: 'POST',
    params,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 查询首页装修优惠券配置
 * @param {Object} params
 * @param {Number} params.type - 页面类型（1=首页，2=二级页）
 */
export function getFitmentCouponListApi(params) {
  return request({
    url: '/admin/platform/fitment/coupon/list',
    method: 'get',
    params,
  });
}

/**
 * 更新首页装修优惠券配置
 * @param {Object} data
 * @param {Array} data.coupons - 优惠券列表
 * @param {Number} data.coupons[].couponId - 优惠券ID
 * @param {Number} data.coupons[].type - 页面类型
 */
export function updateFitmentCouponApi(data) {
  return request({
    url: '/admin/platform/fitment/coupon/update',
    method: 'post',
    data,
  });
}
/**
 * 查询首页装修产品关系列表
 * @param {Object} params - 查询参数
 * @param {Number} params.type - 类型筛选
 */
export function fitmentProductListApi(params) {
  return request({
    url: '/admin/merchant/fitment/product/list/',
    method: 'GET',
    params,
  });
}

/**
 * 更新首页装修产品关系
 * @param {Object} data - 更新数据
 * @param {Array} data.products - 产品列表
 * @param {Number} data.products[].productId - 产品ID
 * @param {Number} data.products[].sort - 排序
 * @param {Number} data.products[].type - 类型
 */
export function fitmentProductUpdateApi(data) {
  return request({
    url: '/admin/merchant/fitment/product/update',
    method: 'POST',
    data,
  });
}

/**
 * 查询装修商户信息
 * @param {Object} params - 查询参数
 * @param {Number} params.type - 页面类型（1=首页，2=二级页）
 */
export function getFitmentMerchantListApi(params) {
  return request({
    url: '/admin/platform/fitment/merchant/list',
    method: 'GET',
    params,
  });
}

/**
 * 更新商户装修
 * @param {Object} data - 更新数据
 * @param {Array} data.merchants - 商户列表
 * @param {Number} data.merchants[].merId - 商户ID
 * @param {Number} data.merchants[].type - 页面类型
 * @param {Number} data.merchants[].sort - 排序
 */
export function updateFitmentMerchantApi(data) {
  return request({
    url: '/admin/platform/fitment/merchant/update',
    method: 'POST',
    data,
  });
}

/**
 * 查询首页装修功能列表
 * @param {Object} params - 查询参数
 * @param {Number} params.type - 页面类型
 */
export function getFitmentFunctionListApi(params) {
  return request({
    url: '/admin/merchant/fitment/function/list/',
    method: 'GET',
    params,
  });
}

/**
 * 批量更新首页装修功能
 * @param {Object} data - 更新数据
 * @param {Array} data.functions - 功能列表
 */
export function updateFitmentFunctionApi(data) {
  return request({
    url: '/admin/merchant/fitment/function/update',
    method: 'POST',
    data,
  });
}

/**
 * 获取广告轮播图列表
 * @param {Object} params - 查询参数
 * @param {Number} params.type - 类型（固定传1）
 */
export function getCarouselListApi(params) {
  return request({
    url: '/admin/merchant/fitment/carousel/list/',
    method: 'GET',
    params,
  });
}

/**
 * 更新广告轮播图配置
 * @param {Object} data - 更新数据
 * @param {Array} data.carousels - 轮播图列表
 * @param {String} data.carousels[].imageUrl - 图片URL
 * @param {String} data.carousels[].linkUrl - 链接URL
 * @param {Number} data.carousels[].adsRange - 广告范围（1=全部，2=新人）
 * @param {Number} data.carousels[].adsHeight - 广告高度
 * @param {Number} data.carousels[].type - 类型（固定为1）
 */
export function updateCarouselApi(data) {
  return request({
    url: '/admin/merchant/fitment/carousel/update',
    method: 'POST',
    data,
  });
}
