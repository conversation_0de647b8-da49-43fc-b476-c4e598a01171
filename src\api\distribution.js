// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from '@/utils/request';

/**
 * @description 分销员 -- 列表
 */
export function promoterListApi(params) {
  return request({
    url: '/admin/merchant/retail/store/people/list',
    method: 'get',
    params,
  });
}

/**
 * @description 推广人 -- 列表
 */
export function spreadListApi(params, data) {
  return request({
    url: '/admin/merchant/retail/store/sub/user/list',
    method: 'get',
    params,
    data,
  });
}

/**
 * @description 推广人订单 -- 列表
 */
export function spreadOrderListApi(params, data) {
  return request({
    url: '/admin/merchant/retail/store/promotion/order/list',
    method: 'get',
    params,
    data,
  });
}

/**
 * @description 分销统计
 */
export function spreadStatisticsApi(params) {
  return request({
    url: `/admin/merchant/store/retail/statistics`,
    method: 'get',
    params,
  });
}
