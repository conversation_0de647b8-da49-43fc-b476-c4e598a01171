(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-28b300f7"],{"1a3b":function(t,e,a){"use strict";a("b074")},4420:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[a("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(e){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(e){t.$set(t.tableFrom,"dateLimit",e)},expression:"tableFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(e,i){return a("el-radio-button",{key:i,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1),t._v(" "),a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"关键字："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入姓名、电话、UID",size:"small",clearable:""},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["platform:retail:store:people:list"],expression:"['platform:retail:store:people:list']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.seachList},slot:"append"})],1)],1)],1)],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"id",label:"ID",width:"60"}}),t._v(" "),a("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"nickname",label:"用户信息","min-width":"130"}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",prop:"spreadCount",label:"推广用户(一级)数量","sort-method":function(t,e){return t.spreadCount-e.spreadCount},"min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",label:"推广订单数量",prop:"spreadOrderNum","sort-method":function(t,e){return t.spreadOrderNum-e.spreadOrderNum},"min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",label:"推广订单金额","min-width":"120","sort-method":function(t,e){return t.spreadOrderTotalPrice-e.spreadOrderTotalPrice},prop:"spreadOrderTotalPrice"}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",label:"佣金总金额","min-width":"120","sort-method":function(t,e){return t.totalBrokeragePrice-e.totalBrokeragePrice},prop:"totalBrokeragePrice"}}),t._v(" "),a("el-table-column",{attrs:{prop:"promoterTime",label:"成为推广员时间","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"spreadNickname",label:"上级推广人","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:retail:store:sub:user:list"],expression:"['merchant:retail:store:sub:user:list']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.onSpread(e.row.id,"man","推广人")}}},[t._v("推广人")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:retail:store:promotion:order:list"],expression:"['merchant:retail:store:promotion:order:list']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.onSpreadOrder(e.row.id,"order","推广订单")}}},[t._v("推广订单")])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:t.titleName+"列表",visible:t.dialogVisible,width:"900px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[a("el-form-item",{key:"1",staticClass:"width100",attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(e){return t.selectChangeSpread(t.spreadFrom.dateLimit)}},model:{value:t.spreadFrom.dateLimit,callback:function(e){t.$set(t.spreadFrom,"dateLimit",e)},expression:"spreadFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(e,i){return a("el-radio-button",{key:i,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1),t._v(" "),a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:t.onchangeTimeSpread},model:{value:t.timeValSpread,callback:function(e){t.timeValSpread=e},expression:"timeValSpread"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"用户类型："}},[a("el-radio-group",{attrs:{size:"small"},on:{change:t.onChanges},model:{value:t.spreadFrom.type,callback:function(e){t.$set(t.spreadFrom,"type",e)},expression:"spreadFrom.type"}},[a("el-radio-button",{attrs:{label:"0"}},[t._v("全部")]),t._v(" "),a("el-radio-button",{attrs:{label:"1"}},[t._v("一级推广人")]),t._v(" "),a("el-radio-button",{attrs:{label:"2"}},[t._v("二级推广人")])],1)],1),t._v(" "),a("el-form-item",{staticClass:"width100",attrs:{label:"关键字："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"order"===t.onName?"请输入订单号":"请输入姓名、电话、UID",size:"small",clearable:""},model:{value:t.keywords,callback:function(e){t.keywords=e},expression:"keywords"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.onChanges},slot:"append"})],1)],1)],1)],1),t._v(" "),"man"===t.onName?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.spreadLoading,expression:"spreadLoading"}],key:"men",staticClass:"table",staticStyle:{width:"100%"},attrs:{data:t.spreadData.data,size:"mini","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"id",label:"ID",width:"60"}}),t._v(" "),a("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}],null,!1,4159822182)}),t._v(" "),a("el-table-column",{attrs:{prop:"nickname",label:"用户信息","min-width":"130"}}),t._v(" "),a("el-table-column",{attrs:{prop:"is_promoter",label:"是否推广员","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("filterYesOrNo")(e.row.isPromoter)))])]}}],null,!1,62589210)}),t._v(" "),a("el-table-column",{attrs:{sortable:"",label:"推广人数","min-width":"120",prop:"spreadCount"}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",label:"订单数","min-width":"120",prop:"payCount"}})],1):t._e(),t._v(" "),"order"===t.onName?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.spreadLoading,expression:"spreadLoading"}],key:"order",staticClass:"table",staticStyle:{width:"100%"},attrs:{data:t.spreadData.data,size:"mini","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"orderNo",label:"订单ID","min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{label:"用户信息","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.nickname))])]}}],null,!1,611505262)}),t._v(" "),a("el-table-column",{attrs:{prop:"updateTime",label:"时间","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",label:"返佣金额","min-width":"120",prop:"price"}})],1):t._e(),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.spreadFrom.limit,"current-page":t.spreadFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.spreadData.total},on:{"size-change":t.handleSizeChangeSpread,"current-change":t.pageChangeSpread}})],1)],1)],1)},r=[],s=a("b775");function l(t){return Object(s["a"])({url:"/admin/merchant/retail/store/people/list",method:"get",params:t})}function o(t,e){return Object(s["a"])({url:"/admin/merchant/retail/store/sub/user/list",method:"get",params:t,data:e})}function n(t,e){return Object(s["a"])({url:"/admin/merchant/retail/store/promotion/order/list",method:"get",params:t,data:e})}var d=a("e350"),m={name:"Distribution",data:function(){return{cardLists:[],timeVal:[],tableData:{data:[],total:0},listLoading:!0,tableFrom:{dateLimit:"",keywords:"",page:1,limit:20},fromList:this.$constants.fromList,dialogVisible:!1,spreadData:{data:[],total:0},spreadFrom:{page:1,limit:10,dateLimit:"",type:0,keywords:"",uid:""},keywords:"",timeValSpread:[],spreadLoading:!1,uid:"",onName:"",titleName:""}},mounted:function(){this.getList()},methods:{checkPermi:d["a"],seachList:function(){this.getList(1)},onSpread:function(t,e,a){this.onName=e,this.titleName=a,this.uid=t,this.dialogVisible=!0,this.spreadFrom={page:1,limit:10,dateLimit:"",type:0,keywords:"",uid:t},this.keywords="",this.getListSpread()},handleClose:function(){this.dialogVisible=!1},selectChangeSpread:function(t){this.timeValSpread=[],this.spreadFrom.dateLimit=t,this.spreadFrom.page=1,"man"===this.onName?this.getListSpread():this.getSpreadOrderList()},onchangeTimeSpread:function(t){this.timeValSpread=t,this.tableFrom.dateLimit=t?this.timeValSpread.join(","):"",this.spreadFrom.page=1,"man"===this.onName?this.getListSpread():this.getSpreadOrderList()},onChanges:function(){this.spreadFrom.page=1,"man"===this.onName?this.getListSpread():this.getSpreadOrderList()},getListSpread:function(){var t=this;this.spreadLoading=!0,this.spreadFrom.keywords=encodeURIComponent(this.keywords),o(this.spreadFrom).then((function(e){t.spreadData.data=e.list,t.spreadData.total=e.total,t.spreadLoading=!1})).catch((function(){t.spreadLoading=!1}))},pageChangeSpread:function(t){this.spreadFrom.page=t,"man"===this.onName?this.getListSpread(this.uid):this.getSpreadOrderList(this.uid)},handleSizeChangeSpread:function(t){this.spreadFrom.limit=t,"man"===this.onName?this.getListSpread(this.uid):this.getSpreadOrderList(this.uid)},onSpreadOrder:function(t,e,a){this.uid=t,this.onName=e,this.titleName=a,this.dialogVisible=!0,this.spreadFrom={page:1,limit:10,dateLimit:"",type:0,keywords:"",uid:t},this.keywords="",this.getSpreadOrderList()},getSpreadOrderList:function(){var t=this;this.spreadLoading=!0,this.spreadFrom.keywords=encodeURIComponent(this.keywords),n(this.spreadFrom).then((function(e){t.spreadData.data=e.list,t.spreadData.total=e.total,t.spreadLoading=!1})).catch((function(){t.spreadLoading=!1}))},selectChange:function(t){this.tableFrom.dateLimit=t,this.timeVal=[],this.getList(1)},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(","):"",this.getList(1)},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,l(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList(1)}}},p=m,c=(a("1a3b"),a("2877")),h=Object(c["a"])(p,i,r,!1,null,"52a5a895",null);e["default"]=h.exports},b074:function(t,e,a){}}]);