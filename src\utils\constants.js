// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/*
/ 全局静态变量定义
切勿随意修改数组次序，很多地方已下标方式使用
 */

export const page = {
  limit: [20, 40, 60, 80, 100],
  page: 1,
  layout: 'total, sizes, prev, pager, next, jumper',
};

/**
 * 无限极分类type
 * @type {{product: number, attachment: number, menu: number, article: number, operator: number}}
 */
export const categoryType = [
  // 1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置
  { name: '产品分类', value: 1, shortName: '产品' },
  { name: '附件分类', value: 2, shortName: '附件' },
  { name: '文章分类', value: 3, shortName: '文章' },
  { name: '设置分类', value: 4, shortName: '设置' },
  { name: '菜单分类', value: 5, shortName: '菜单' },
  { name: '配置分类', value: 6, shortName: '配置' },
  { name: '秒杀配置', value: 7, shortName: '秒杀' },
];

export const roleListStatus = [
  { label: '全部', value: '' },
  { label: '显示', value: 1 },
  { label: '不显示', value: 0 },
];

export const showHiddenStatus = [
  { label: '显示', value: '‘1’' },
  { label: '不显示', value: '‘0’' },
];

export const switchStatus = [
  { label: '开启', value: 1 },
  { label: '关闭', value: 0 },
];

export const deletedOrNormal = [
  { label: '正常', value: 0 },
  { label: '已删除', value: 1 },
];

/**
 * 暂时弃用
 * @type {*[]}
 */
export const configCategory = [
  { label: '系统', value: '0' },
  { label: '应用', value: '1' },
  { label: '支付', value: '2' },
  { label: '其他', value: '3' },
];

/**
 * 表单配置集合集中配置
 * @type {{id: number, dis: string}[]}
 */
export const formConfigIds = [
  { id: 84, dis: '微信公众号表单配置' },
  { id: 86, dis: '秒杀配置' },
];

/**
 * 时间选择器
 */
export const fromList = {
  title: '选择时间',
  custom: true,
  fromTxt: [
    { text: '全部', val: '' },
    { text: '今天', val: 'today' },
    { text: '昨天', val: 'yesterday' },
    { text: '最近7天', val: 'lately7' },
    { text: '最近30天', val: 'lately30' },
    { text: '本月', val: 'month' },
    { text: '本年', val: 'year' },
  ],
};

// 统计管理时间选择器
export const timeList = {
  title: '选择时间',
  custom: true,
  fromTxt: [
    { text: '昨天', val: `` },
    { text: '最近7天', val: 'lately7' },
    { text: '最近30天', val: 'lately30' },
  ],
};

// 眼镜度数可选范围
export const sphOptions = [
  '-25.00',
  '-24.00',
  '-23.00',
  '-22.00',
  '-21.00',
  '-20.00',
  '-19.00',
  '-18.00',
  '-17.50',
  '-17.00',
  '-16.50',
  '-16.00',
  '-15.50',
  '-15.00',
  '-14.75',
  '-14.50',
  '-14.25',
  '-14.00',
  '-13.75',
  '-13.50',
  '-13.25',
  '-13.00',
  '-12.75',
  '-12.50',
  '-12.25',
  '-12.00',
  '-11.75',
  '-11.50',
  '-11.25',
  '-11.00',
  '-10.75',
  '-10.50',
  '-10.25',
  '-10.00',
  '-9.75',
  '-9.50',
  '-9.25',
  '-9.00',
  '-8.75',
  '-8.50',
  '-8.25',
  '-8.00',
  '-7.75',
  '-7.50',
  '-7.25',
  '-7.00',
  '-6.75',
  '-6.50',
  '-6.25',
  '-6.00',
  '-5.75',
  '-5.50',
  '-5.25',
  '-5.00',
  '-4.75',
  '-4.50',
  '-4.25',
  '-4.00',
  '-3.75',
  '-3.50',
  '-3.25',
  '-3.00',
  '-2.75',
  '-2.50',
  '-2.25',
  '-2.00',
  '-1.75',
  '-1.50',
  '-1.25',
  '-1.00',
  '-0.75',
  '-0.50',
  '-0.25',
  '0.00',
  '0.25',
  '0.50',
  '0.75',
  '1.00',
  '1.25',
  '1.50',
  '1.75',
  '2.00',
  '2.25',
  '2.50',
  '2.75',
  '3.00',
  '3.25',
  '3.50',
  '3.75',
  '4.00',
  '4.25',
  '4.50',
  '4.75',
  '5.00',
  '5.25',
  '5.50',
  '5.75',
  '6.00',
  '6.25',
  '6.50',
  '6.75',
  '7.00',
  '7.25',
  '7.50',
  '7.75',
  '8.00',
  '8.25',
  '8.50',
  '8.75',
  '9.00',
  '9.25',
  '9.50',
  '9.75',
  '10.00',
  '10.25',
  '10.50',
  '10.75',
  '11.00',
  '11.25',
  '11.50',
  '11.75',
  '12.00',
];

// 散光可选范围
export const cylOptions = [
  '-7.00',
  '-6.75',
  '-6.50',
  '-6.25',
  '-6.00',
  '-5.75',
  '-5.50',
  '-5.25',
  '-5.00',
  '-4.75',
  '-4.50',
  '-4.25',
  '-4.00',
  '-3.75',
  '-3.50',
  '-3.25',
  '-3.00',
  '-2.75',
  '-2.50',
  '-2.25',
  '-2.00',
  '-1.75',
  '-1.50',
  '-1.25',
  '-1.00',
  '-0.75',
  '-0.50',
  '-0.25',
  '0.00',
];
