(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6aa4c198"],{"2eb3":function(t,e,a){"use strict";a.d(e,"c",(function(){return o})),a.d(e,"d",(function(){return n})),a.d(e,"b",(function(){return r})),a.d(e,"e",(function(){return i})),a.d(e,"h",(function(){return s})),a.d(e,"g",(function(){return u})),a.d(e,"a",(function(){return d}));var l=a("b775");function o(t){var e={id:t.id};return Object(l["a"])({url:"/admin/merchant/admin/delete",method:"GET",params:e})}function n(t){return Object(l["a"])({url:"/admin/merchant/admin/list",method:"GET",params:t})}function r(t){var e={account:t.account,level:t.level,pwd:t.pwd,realName:t.realName,roles:t.roles.join(","),status:t.status,phone:t.phone};return Object(l["a"])({url:"/admin/merchant/admin/save",method:"POST",data:e})}function i(t){var e={account:t.account,phone:t.phone,pwd:t.pwd,realName:t.realName,id:t.id,roles:t.roles,status:t.status};return Object(l["a"])({url:"/admin/merchant/admin/update",method:"POST",data:e})}function s(t){return Object(l["a"])({url:"/admin/merchant/admin/updateStatus",method:"get",params:t})}function u(t){return Object(l["a"])({url:"/admin/merchant/log/sensitive/list",method:"get",params:t})}function d(t){var e={password:t.pwd,realName:t.realName};return Object(l["a"])({url:"/admin/merchant/login/admin/update",method:"POST",data:e})}},ec61:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.list,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"adminId",label:"管理员id",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"adminAccount",label:"管理员账号",width:"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status",label:"操作状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:e.row.status?"danger":""}},[t._v(t._s(e.row.status?"异常":"正常"))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"description",label:"接口描述",width:"140","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"操作时间",width:"120","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"ip",label:"主机地址",width:"120","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"method",label:"方法名称",width:"180","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"url",label:"请求URL",width:"180","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"methodType",label:"请求类型",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"requestMethod",label:"请求方式",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"requestParam",label:"请求参数",width:"150","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"result",label:"返回参数",width:"180","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"errorMsg",label:"错误消息",width:"180","show-overflow-tooltip":!0}})],1),t._v(" "),a("el-pagination",{attrs:{"page-sizes":[20,30,40,60],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)},o=[],n=a("2eb3"),r={data:function(){return{tableData:{},tableFrom:{page:1,limit:20},listLoading:!1}},mounted:function(){this.getLogList()},methods:{getLogList:function(){var t=this;this.listLoading=!0,Object(n["g"])(this.tableFrom).then((function(e){t.tableData=e,t.listLoading=!1}))},handleSizeChange:function(t){this.tableFrom.limit=t,this.getLogList()},pageChange:function(t){this.tableFrom.page=t,this.getLogList()}}},i=r,s=a("2877"),u=Object(s["a"])(i,l,o,!1,null,null,null);e["default"]=u.exports}}]);