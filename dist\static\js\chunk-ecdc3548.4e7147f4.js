(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ecdc3548"],{"0496":function(e,t,i){},1:function(e,t){},"27c0":function(e,t,i){},"2d4e":function(e,t,i){"use strict";i("27c0")},6386:function(e,t,i){"use strict";i("bf1e")},"6b75":function(e,t,i){e.exports=i.p+"static/img/default.6b914f9c.jpg"},"7daa":function(e,t){!function(){function e(e,t,i){return e.getAttribute(t)||i}function t(e){return document.getElementsByTagName(e)}function i(){var i=t("script"),n=i.length,o=i[n-1];return{l:n,z:e(o,"zIndex",-2),o:e(o,"opacity",.8),c:e(o,"color","255,255,255"),n:e(o,"count",240)}}function n(){s=r.width=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,a=r.height=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight}function o(){if(d+=1,d<5)u(o);else{d=0,l.clearRect(0,0,s,a);var e,t,i,n,r,h,p=[f].concat(m);m.forEach((function(o){for(o.x+=o.xa,o.y+=o.ya,o.xa*=o.x>s||o.x<0?-1:1,o.ya*=o.y>a||o.y<0?-1:1,l.fillRect(o.x-.5,o.y-.5,2,2),l.fillStyle="#FFFFFF",t=0;t<p.length;t++)e=p[t],o!==e&&null!==e.x&&null!==e.y&&(n=o.x-e.x,r=o.y-e.y,h=n*n+r*r,h<e.max&&(e===f&&h>=e.max/2&&(o.x-=.03*n,o.y-=.03*r),i=(e.max-h)/e.max,l.beginPath(),l.lineWidth=i/2,l.strokeStyle="rgba("+c.c+","+(i+.2)+")",l.moveTo(o.x,o.y),l.lineTo(e.x,e.y),l.stroke()));p.splice(p.indexOf(o),1)})),u(o)}}var s,a,r=document.createElement("canvas"),c=i(),h="c_n"+c.l,l=r.getContext("2d"),d=0,u=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/45)},p=Math.random,f={x:null,y:null,max:2e4};r.id=h,r.style.cssText="position:fixed;top:0;left:0;z-index:"+c.z+";opacity:"+c.o,t("body")[0].appendChild(r),n(),window.onresize=n,window.onmousemove=function(e){e=e||window.event,f.x=e.clientX,f.y=e.clientY},window.onmouseout=function(){f.x=null,f.y=null};for(var m=[],g=0;c.n>g;g++){var v=p()*s,y=p()*a,b=2*p()-1,w=2*p()-1;m.push({x:v,y:y,xa:b,ya:w,max:6e3})}setTimeout((function(){o()}),100)}()},9449:function(e,t,i){e.exports=i.p+"static/img/laber.0bc21b94.png"},"9ed6":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"page-account",style:e.backgroundImages?{backgroundImage:"url("+e.backgroundImages+")"}:{backgroundImage:"url("+e.backgroundImageMo+")"}},[i("div",{staticClass:"container",class:[e.fullWidth>768?"containerSamll":"containerBig"]},[e.fullWidth>768?[i("div",{staticClass:"swiperPic"},[i("img",{attrs:{src:e.leftLogo}})])]:e._e(),e._v(" "),i("div",{staticClass:"index_from page-account-container"},[e._m(0),e._v(" "),i("div",{staticClass:"page-account-top"},[i("div",{staticClass:"page-account-top-logo"},[i("img",{attrs:{src:e.loginLogo,alt:"logo"}})])]),e._v(" "),i("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,autocomplete:"on","label-position":"left"},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}}},[i("el-form-item",{attrs:{prop:"account"}},[i("el-input",{ref:"account",attrs:{"prefix-icon":"el-icon-user",placeholder:"用户名",name:"username",type:"text",tabindex:"1",autocomplete:"on"},model:{value:e.loginForm.account,callback:function(t){e.$set(e.loginForm,"account",t)},expression:"loginForm.account"}})],1),e._v(" "),i("el-form-item",{attrs:{prop:"pwd"}},[i("el-input",{key:e.passwordType,ref:"pwd",attrs:{"prefix-icon":"el-icon-lock",type:e.passwordType,placeholder:"密码",name:"pwd",tabindex:"2","auto-complete":"on"},model:{value:e.loginForm.pwd,callback:function(t){e.$set(e.loginForm,"pwd",t)},expression:"loginForm.pwd"}}),e._v(" "),i("span",{staticClass:"show-pwd",on:{click:e.showPwd}},[i("svg-icon",{attrs:{"icon-class":"password"===e.passwordType?"eye":"eye-open"}})],1)],1),e._v(" "),e.captchatOn?e._e():i("el-form-item",{staticClass:"captcha",attrs:{prop:"code"}},[i("div",{staticClass:"captcha"},[i("el-input",{ref:"username",staticStyle:{width:"218px"},attrs:{"prefix-icon":"el-icon-message",placeholder:"验证码",name:"username",type:"text",tabindex:"3",autocomplete:"on"},model:{value:e.loginForm.code,callback:function(t){e.$set(e.loginForm,"code",t)},expression:"loginForm.code"}}),e._v(" "),i("div",{staticClass:"imgs",on:{click:function(t){return e.getCaptcha()}}},[i("img",{attrs:{src:e.captchatImg}}),e._v(" "),i("span",{directives:[{name:"show",rawName:"v-show",value:e.showCaptchatImg,expression:"showCaptchatImg"}]},[e._v("已失效")])])],1)]),e._v(" "),i("div",{staticClass:"acea-row"},[i("el-button",{staticStyle:{width:"100%","margin-bottom":"30px"},attrs:{loading:e.loading,type:"primary",disabled:e.disabled},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e._v("登录\n          ")])],1)],1),e._v(" "),e.captchatOn?i("Verify",{ref:"verify",attrs:{mode:"pop",captchaType:"blockPuzzle",imgSize:{width:"330px",height:"155px"}},on:{success:e.success}}):e._e()],1)],2)])},o=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"labelPic"},[n("img",{attrs:{src:i("9449")}})])}],s=(i("61f7"),i("7daa"),i("c24f")),a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"show",rawName:"v-show",value:e.showBox,expression:"showBox"}],class:"pop"==e.mode?"mask":""},[i("div",{class:"pop"==e.mode?"verifybox":"",style:{"max-width":parseInt(e.imgSize.width)+30+"px"}},["pop"==e.mode?i("div",{staticClass:"verifybox-top"},[e._v("\n      请完成安全验证\n      "),i("span",{staticClass:"verifybox-close",on:{click:e.closeBox}},[i("i",{staticClass:"iconfont icon-close"})])]):e._e(),e._v(" "),i("div",{staticClass:"verifybox-bottom",style:{padding:"pop"==e.mode?"15px":"0"}},[e.componentType?i(e.componentType,{ref:"instance",tag:"components",attrs:{"captcha-type":e.captchaType,type:e.verifyType,figure:e.figure,arith:e.arith,mode:e.mode,"v-space":e.vSpace,explain:e.explain,"img-size":e.imgSize,"block-size":e.blockSize,"bar-size":e.barSize,"default-img":e.defaultImg}}):e._e()],1)])])},r=[],c=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{position:"relative"}},["2"===e.type?i("div",{staticClass:"verify-img-out",style:{height:parseInt(e.setSize.imgHeight)+e.vSpace+"px"}},[i("div",{staticClass:"verify-img-panel",style:{width:e.setSize.imgWidth,height:e.setSize.imgHeight}},[i("img",{staticStyle:{width:"100%",height:"100%",display:"block"},attrs:{src:e.backImgBase?"data:image/png;base64,"+e.backImgBase:e.defaultImg,alt:""}}),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showRefresh,expression:"showRefresh"}],staticClass:"verify-refresh",on:{click:e.refresh}},[i("i",{staticClass:"iconfont icon-refresh"})]),e._v(" "),i("transition",{attrs:{name:"tips"}},[e.tipWords?i("span",{staticClass:"verify-tips",class:e.passFlag?"suc-bg":"err-bg"},[e._v(e._s(e.tipWords))]):e._e()])],1)]):e._e(),e._v(" "),i("div",{staticClass:"verify-bar-area",style:{width:e.setSize.imgWidth,height:e.barSize.height,"line-height":e.barSize.height}},[i("span",{staticClass:"verify-msg",domProps:{textContent:e._s(e.text)}}),e._v(" "),i("div",{staticClass:"verify-left-bar",style:{width:void 0!==e.leftBarWidth?e.leftBarWidth:e.barSize.height,height:e.barSize.height,"border-color":e.leftBarBorderColor,transaction:e.transitionWidth}},[i("span",{staticClass:"verify-msg",domProps:{textContent:e._s(e.finishText)}}),e._v(" "),i("div",{staticClass:"verify-move-block",style:{width:e.barSize.height,height:e.barSize.height,"background-color":e.moveBlockBackgroundColor,left:e.moveBlockLeft,transition:e.transitionLeft},on:{touchstart:e.start,mousedown:e.start}},[i("i",{class:["verify-icon iconfont",e.iconClass],style:{color:e.iconColor}}),e._v(" "),"2"===e.type?i("div",{staticClass:"verify-sub-block",style:{width:Math.floor(47*parseInt(e.setSize.imgWidth)/310)+"px",height:e.setSize.imgHeight,top:"-"+(parseInt(e.setSize.imgHeight)+e.vSpace)+"px","background-size":e.setSize.imgWidth+" "+e.setSize.imgHeight}},[i("img",{staticStyle:{width:"100%",height:"100%",display:"block"},attrs:{src:"data:image/png;base64,"+e.blockBackImgBase,alt:""}})]):e._e()])])])])},h=[],l=i("3452"),d=i.n(l);function u(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"XwKsGlMcdPMEhR1B",i=d.a.enc.Utf8.parse(t),n=d.a.enc.Utf8.parse(e),o=d.a.AES.encrypt(n,i,{mode:d.a.mode.ECB,padding:d.a.pad.Pkcs7});return o.toString()}function p(e){var t,i,n,o,s=e.$el.parentNode.offsetWidth||window.offsetWidth,a=e.$el.parentNode.offsetHeight||window.offsetHeight;return t=-1!=e.imgSize.width.indexOf("%")?parseInt(this.imgSize.width)/100*s+"px":this.imgSize.width,i=-1!=e.imgSize.height.indexOf("%")?parseInt(this.imgSize.height)/100*a+"px":this.imgSize.height,n=-1!=e.barSize.width.indexOf("%")?parseInt(this.barSize.width)/100*s+"px":this.barSize.width,o=-1!=e.barSize.height.indexOf("%")?parseInt(this.barSize.height)/100*a+"px":this.barSize.height,{imgWidth:t,imgHeight:i,barWidth:n,barHeight:o}}var f=i("bc3a"),m=i.n(f),g=i("27c7");m.a.defaults.baseURL=g["a"].httpUrl;var v=m.a.create({timeout:4e4,headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/json; charset=UTF-8"}});v.interceptors.request.use((function(e){return e}),(function(e){Promise.reject(e)})),v.interceptors.response.use((function(e){var t=e.data;return t}),(function(e){}));var y=v;function b(e){return y({url:"/captcha/get",method:"post",data:e})}function w(e){return y({url:"/captcha/check",method:"post",data:e})}var k={name:"VerifySlide",props:{captchaType:{type:String},type:{type:String,default:"1"},mode:{type:String,default:"fixed"},vSpace:{type:Number,default:5},explain:{type:String,default:"向右滑动完成验证"},imgSize:{type:Object,default:function(){return{width:"310px",height:"155px"}}},blockSize:{type:Object,default:function(){return{width:"50px",height:"50px"}}},barSize:{type:Object,default:function(){return{width:"310px",height:"40px"}}},defaultImg:{type:String,default:""}},data:function(){return{secretKey:"",passFlag:"",backImgBase:"",blockBackImgBase:"",backToken:"",startMoveTime:"",endMovetime:"",tipsBackColor:"",tipWords:"",text:"",finishText:"",setSize:{imgHeight:0,imgWidth:0,barHeight:0,barWidth:0},top:0,left:0,moveBlockLeft:void 0,leftBarWidth:void 0,moveBlockBackgroundColor:void 0,leftBarBorderColor:"#ddd",iconColor:void 0,iconClass:"icon-right",status:!1,isEnd:!1,showRefresh:!0,transitionLeft:"",transitionWidth:""}},computed:{barArea:function(){return this.$el.querySelector(".verify-bar-area")},resetSize:function(){return p}},watch:{type:{immediate:!0,handler:function(){this.init()}}},mounted:function(){this.$el.onselectstart=function(){return!1}},methods:{init:function(){var e=this;this.text=this.explain,this.getPictrue(),this.$nextTick((function(){var t=e.resetSize(e);for(var i in t)e.$set(e.setSize,i,t[i]);e.$parent.$emit("ready",e)}));var t=this;window.removeEventListener("touchmove",(function(e){t.move(e)})),window.removeEventListener("mousemove",(function(e){t.move(e)})),window.removeEventListener("touchend",(function(){t.end()})),window.removeEventListener("mouseup",(function(){t.end()})),window.addEventListener("touchmove",(function(e){t.move(e)})),window.addEventListener("mousemove",(function(e){t.move(e)})),window.addEventListener("touchend",(function(){t.end()})),window.addEventListener("mouseup",(function(){t.end()}))},start:function(e){if(e=e||window.event,e.touches)t=e.touches[0].pageX;else var t=e.clientX;this.startLeft=Math.floor(t-this.barArea.getBoundingClientRect().left),this.startMoveTime=+new Date,0==this.isEnd&&(this.text="",this.moveBlockBackgroundColor="#337ab7",this.leftBarBorderColor="#337AB7",this.iconColor="#fff",e.stopPropagation(),this.status=!0)},move:function(e){if(e=e||window.event,this.status&&0==this.isEnd){if(e.touches)t=e.touches[0].pageX;else var t=e.clientX;var i=this.barArea.getBoundingClientRect().left,n=t-i;n>=this.barArea.offsetWidth-parseInt(parseInt(this.blockSize.width)/2)-2&&(n=this.barArea.offsetWidth-parseInt(parseInt(this.blockSize.width)/2)-2),n<=0&&(n=parseInt(parseInt(this.blockSize.width)/2)),this.moveBlockLeft=n-this.startLeft+"px",this.leftBarWidth=n-this.startLeft+"px"}},end:function(){var e=this;this.endMovetime=+new Date;var t=this;if(this.status&&0==this.isEnd){var i=parseInt((this.moveBlockLeft||"").replace("px",""));i=310*i/parseInt(this.setSize.imgWidth);var n={captchaType:this.captchaType,pointJson:this.secretKey?u(JSON.stringify({x:i,y:5}),this.secretKey):JSON.stringify({x:i,y:5}),token:this.backToken};w(n).then((function(n){if("0000"==n.repCode){e.moveBlockBackgroundColor="#5cb85c",e.leftBarBorderColor="#5cb85c",e.iconColor="#fff",e.iconClass="icon-check",e.showRefresh=!1,e.isEnd=!0,"pop"==e.mode&&setTimeout((function(){e.$parent.clickShow=!1,e.refresh()}),1500),e.passFlag=!0,e.tipWords="".concat(((e.endMovetime-e.startMoveTime)/1e3).toFixed(2),"s验证成功");var o=e.secretKey?u(e.backToken+"---"+JSON.stringify({x:i,y:5}),e.secretKey):e.backToken+"---"+JSON.stringify({x:i,y:5});setTimeout((function(){e.tipWords="",e.$parent.closeBox(),e.$parent.$emit("success",{captchaVerification:o})}),1e3)}else e.moveBlockBackgroundColor="#d9534f",e.leftBarBorderColor="#d9534f",e.iconColor="#fff",e.iconClass="icon-close",e.passFlag=!1,setTimeout((function(){t.refresh()}),1e3),e.$parent.$emit("error",e),e.tipWords="验证失败",setTimeout((function(){e.tipWords=""}),1e3)})),this.status=!1}},refresh:function(){var e=this;this.showRefresh=!0,this.finishText="",this.transitionLeft="left .3s",this.moveBlockLeft=0,this.leftBarWidth=void 0,this.transitionWidth="width .3s",this.leftBarBorderColor="#ddd",this.moveBlockBackgroundColor="#fff",this.iconColor="#000",this.iconClass="icon-right",this.isEnd=!1,this.getPictrue(),setTimeout((function(){e.transitionWidth="",e.transitionLeft="",e.text=e.explain}),300)},getPictrue:function(){var e=this,t={captchaType:this.captchaType,clientUid:localStorage.getItem("slider"),ts:Date.now()};b(t).then((function(t){"0000"==t.repCode?(e.backImgBase=t.repData.originalImageBase64,e.blockBackImgBase=t.repData.jigsawImageBase64,e.backToken=t.repData.token,e.secretKey=t.repData.secretKey,e.$store.commit("user/SET_CAPTCHA",{captchaVerification:t.repData.captchaVerification,secretKey:t.repData.secretKey,token:t.repData.token})):e.tipWords=t.repMsg,"6201"==t.repCode&&(e.backImgBase=null,e.blockBackImgBase=null)}))}}},x=k,S=i("2877"),C=Object(S["a"])(x,c,h,!1,null,null,null),T=C.exports,B=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{position:"relative"}},[i("div",{staticClass:"verify-img-out"},[i("div",{staticClass:"verify-img-panel",style:{width:e.setSize.imgWidth,height:e.setSize.imgHeight,"background-size":e.setSize.imgWidth+" "+e.setSize.imgHeight,"margin-bottom":e.vSpace+"px"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.showRefresh,expression:"showRefresh"}],staticClass:"verify-refresh",staticStyle:{"z-index":"3"},on:{click:e.refresh}},[i("i",{staticClass:"iconfont icon-refresh"})]),e._v(" "),i("img",{ref:"canvas",staticStyle:{width:"100%",height:"100%",display:"block"},attrs:{src:e.pointBackImgBase?"data:image/png;base64,"+e.pointBackImgBase:e.defaultImg,alt:""},on:{click:function(t){e.bindingClick&&e.canvasClick(t)}}}),e._v(" "),e._l(e.tempPoints,(function(t,n){return i("div",{key:n,staticClass:"point-area",style:{"background-color":"#1abd6c",color:"#fff","z-index":9999,width:"20px",height:"20px","text-align":"center","line-height":"20px","border-radius":"50%",position:"absolute",top:parseInt(t.y-10)+"px",left:parseInt(t.x-10)+"px"}},[e._v("\n        "+e._s(n+1)+"\n      ")])}))],2)]),e._v(" "),i("div",{staticClass:"verify-bar-area",style:{width:e.setSize.imgWidth,color:this.barAreaColor,"border-color":this.barAreaBorderColor,"line-height":this.barSize.height}},[i("span",{staticClass:"verify-msg"},[e._v(e._s(e.text))])])])},z=[],I={name:"VerifyPoints",props:{mode:{type:String,default:"fixed"},captchaType:{type:String},vSpace:{type:Number,default:5},imgSize:{type:Object,default:function(){return{width:"310px",height:"155px"}}},barSize:{type:Object,default:function(){return{width:"310px",height:"40px"}}},defaultImg:{type:String,default:""}},data:function(){return{secretKey:"",checkNum:3,fontPos:[],checkPosArr:[],num:1,pointBackImgBase:"",poinTextList:[],backToken:"",setSize:{imgHeight:0,imgWidth:0,barHeight:0,barWidth:0},tempPoints:[],text:"",barAreaColor:void 0,barAreaBorderColor:void 0,showRefresh:!0,bindingClick:!0}},computed:{resetSize:function(){return p}},watch:{type:{immediate:!0,handler:function(){this.init()}}},mounted:function(){this.$el.onselectstart=function(){return!1}},methods:{init:function(){var e=this;this.fontPos.splice(0,this.fontPos.length),this.checkPosArr.splice(0,this.checkPosArr.length),this.num=1,this.getPictrue(),this.$nextTick((function(){e.setSize=e.resetSize(e),e.$parent.$emit("ready",e)}))},canvasClick:function(e){var t=this;this.checkPosArr.push(this.getMousePos(this.$refs.canvas,e)),this.num==this.checkNum&&(this.num=this.createPoint(this.getMousePos(this.$refs.canvas,e)),this.checkPosArr=this.pointTransfrom(this.checkPosArr,this.setSize),setTimeout((function(){var e=t.secretKey?u(t.backToken+"---"+JSON.stringify(t.checkPosArr),t.secretKey):t.backToken+"---"+JSON.stringify(t.checkPosArr),i={captchaType:t.captchaType,pointJson:t.secretKey?u(JSON.stringify(t.checkPosArr),t.secretKey):JSON.stringify(t.checkPosArr),token:t.backToken};w(i).then((function(i){"0000"==i.repCode?(t.barAreaColor="#4cae4c",t.barAreaBorderColor="#5cb85c",t.text="验证成功",t.bindingClick=!1,"pop"==t.mode&&setTimeout((function(){t.$parent.clickShow=!1,t.refresh()}),1500),t.$parent.$emit("success",{captchaVerification:e})):(t.$parent.$emit("error",t),t.barAreaColor="#d9534f",t.barAreaBorderColor="#d9534f",t.text="验证失败",setTimeout((function(){t.refresh()}),700))}))}),400)),this.num<this.checkNum&&(this.num=this.createPoint(this.getMousePos(this.$refs.canvas,e)))},getMousePos:function(e,t){var i=t.offsetX,n=t.offsetY;return{x:i,y:n}},createPoint:function(e){return this.tempPoints.push(Object.assign({},e)),++this.num},refresh:function(){this.tempPoints.splice(0,this.tempPoints.length),this.barAreaColor="#000",this.barAreaBorderColor="#ddd",this.bindingClick=!0,this.fontPos.splice(0,this.fontPos.length),this.checkPosArr.splice(0,this.checkPosArr.length),this.num=1,this.getPictrue(),this.text="验证失败",this.showRefresh=!0},getPictrue:function(){var e=this,t={captchaType:this.captchaType,clientUid:localStorage.getItem("point"),ts:Date.now()};b(t).then((function(t){"0000"==t.repCode?(e.pointBackImgBase=t.repData.originalImageBase64,e.backToken=t.repData.token,e.secretKey=t.repData.secretKey,e.poinTextList=t.repData.wordList,e.text="请依次点击【"+e.poinTextList.join(",")+"】"):e.text=t.repMsg,"6201"==t.repCode&&(e.pointBackImgBase=null)}))},pointTransfrom:function(e,t){var i=e.map((function(e){var i=Math.round(310*e.x/parseInt(t.imgWidth)),n=Math.round(155*e.y/parseInt(t.imgHeight));return{x:i,y:n}}));return i}}},_=I,$=Object(S["a"])(_,B,z,!1,null,null,null),P=$.exports,W={name:"Vue2Verify",components:{VerifySlide:T,VerifyPoints:P},props:{locale:{require:!1,type:String,default:function(){if(navigator.language)var e=navigator.language;else e=navigator.browserLanguage;return e}},captchaType:{type:String,required:!0},figure:{type:Number},arith:{type:Number},mode:{type:String,default:"pop"},vSpace:{type:Number},explain:{type:String},imgSize:{type:Object,default:function(){return{width:"310px",height:"155px"}}},blockSize:{type:Object},barSize:{type:Object}},data:function(){return{clickShow:!1,verifyType:void 0,componentType:void 0,defaultImg:i("6b75")}},computed:{instance:function(){return this.$refs.instance||{}},showBox:function(){return"pop"!=this.mode||this.clickShow}},watch:{captchaType:{immediate:!0,handler:function(e){switch(e.toString()){case"blockPuzzle":this.verifyType="2",this.componentType="VerifySlide";break;case"clickWord":this.verifyType="",this.componentType="VerifyPoints";break}}}},mounted:function(){this.uuid()},methods:{uuid:function(){for(var e=[],t="0123456789abcdef",i=0;i<36;i++)e[i]=t.substr(Math.floor(16*Math.random()),1);e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-";var n="slider-"+e.join(""),o="point-"+e.join("");localStorage.getItem("slider")||localStorage.setItem("slider",n),localStorage.getItem("point")||localStorage.setItem("point",o)},i18n:function(e){if(this.$t)return this.$t(e);var t=this.$options.i18n.messages[this.locale]||this.$options.i18n.messages["en-US"];return t[e]},refresh:function(){this.instance.refresh&&this.instance.refresh()},closeBox:function(){this.clickShow=!1,this.refresh()},show:function(){"pop"==this.mode&&(this.clickShow=!0)}}},A=W,E=(i("e32a"),Object(S["a"])(A,a,r,!1,null,null,null)),O=E.exports,L={name:"Login",components:{Verify:O},data:function(){return{showCaptchatImg:!1,captchatImg:"",captchatOn:!0,leftLogo:"",loginLogo:"",backgroundImages:"",backgroundImageMo:i("e6df3"),fullWidth:document.body.clientWidth,swiperOption:{pagination:{el:".pagination"},autoplay:{enabled:!0,disableOnInteraction:!1,delay:3e3}},loginForm:{account:"",pwd:"",key:"",code:"",captcha:{captchaVerification:"",secretKey:"",token:""}},loginRules:{account:[{required:!0,trigger:"blur",message:"请输入用户名"}],pwd:[{required:!0,trigger:"blur",message:"请输入密码"}],code:[{required:!0,message:"请输入正确的验证码",trigger:"blur"}]},passwordType:"password",capsTooltip:!1,loading:!1,showDialog:!1,redirect:void 0,otherQuery:{},disabled:!1}},watch:{fullWidth:function(e){if(!this.timer){this.screenWidth=e,this.timer=!0;var t=this;setTimeout((function(){t.timer=!1}),400)}},$route:{handler:function(e){var t=e.query;t&&(this.redirect=t.redirect,this.otherQuery=this.getOtherQuery(t))},immediate:!0}},created:function(){var e=this;Object(s["b"])().then((function(t){e.captchatOn=t;var i=e;document.onkeydown=function(e){if(-1!==i.$route.path.indexOf("login")){var t=window.event.keyCode;13===t&&i.handleLogin()}},window.addEventListener("resize",e.handleResize)})).then((function(){e.getCaptcha()}))},mounted:function(){var e=this;this.getInfo(),this.$nextTick((function(){e.screenWidth<768?document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0].className="index_bg"})),""===this.loginForm.account?this.$refs.account.focus():""===this.loginForm.pwd&&this.$refs.pwd.focus()},beforeCreate:function(){this.fullWidth<768?document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0].className="index_bg"},destroyed:function(){},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize),document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg")},methods:{handleResize:function(e){this.fullWidth=document.body.clientWidth,this.fullWidth<768?document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0].className="index_bg"},getInfo:function(){var e=this;Object(s["d"])().then((function(t){e.leftLogo=t.leftLogo,e.loginLogo=t.loginLogo,e.backgroundImages=t.backgroundImage}))},checkCapslock:function(e){var t=e.key;this.capsTooltip=t&&1===t.length&&t>="A"&&t<="Z"},showPwd:function(){var e=this;"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick((function(){e.$refs.pwd.focus()}))},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){if(!t)return!1;e.captchatOn?e.$refs.verify.show():e.success(null)}))},success:function(e){var t=this;this.loginForm.captcha=this.$store.state.user.captcha,this.loginForm.captcha.captchaVerification=e?e.captchaVerification:"";var i=this.$loading({lock:!0,text:"正在登录中."});this.$store.dispatch("user/login",this.loginForm).then((function(){t.$store.commit("product/SET_AdminProductClassify",[]),t.$store.commit("product/SET_MerProductClassify",[]),t.$store.commit("merchant/SET_MerchantClassify",[]),t.$store.commit("merchant/SET_MerchantType",[]),t.$store.commit("product/SET_ShippingTemplates",[]),t.$router.push({path:t.redirect||"/",query:t.otherQuery}),i.close(),t.disabled=!0})).catch((function(e){i.close(),t.disabled=!1,t.getCaptcha()}))},getCaptcha:function(){var e=this;this.captchatOn||Object(s["a"])().then((function(t){e.captchatImg=t.code,e.loginForm.key=t.key,e.showCaptchatImg=!1,setTimeout((function(){e.showCaptchatImg=!0}),27e4)})).catch((function(t){var i=t.message;e.$message.error(i)}))},getOtherQuery:function(e){return Object.keys(e).reduce((function(t,i){return"redirect"!==i&&(t[i]=e[i]),t}),{})}}},F=L,N=(i("2d4e"),i("6386"),Object(S["a"])(F,n,o,!1,null,"3f81daac",null));t["default"]=N.exports},bf1e:function(e,t,i){},e32a:function(e,t,i){"use strict";i("0496")},e6df3:function(e,t,i){e.exports=i.p+"static/img/bg.590046d3.jpg"}}]);