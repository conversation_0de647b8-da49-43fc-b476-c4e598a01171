(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-389cb932"],{"1fbf":function(t,e,a){},5480:function(t,e,a){"use strict";a("1fbf")},"6f69":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[t.isCopy?a("el-card",[a("div",{staticClass:"line-ht"},[t._v("\n      生成的商品默认是没有上架的，请手动上架商品！\n      "),t.copyConfig.copyType&&1==t.copyConfig.copyType?a("span",[t._v("您当前剩余"+t._s(t.merInfo.copyProductNum)+"条采集次数\n        ")]):t._e()]),t._v(" "),t.copyConfig.copyType&&1!=t.copyConfig.copyType?a("div",[a("el-radio-group",{model:{value:t.form,callback:function(e){t.form=e},expression:"form"}},[a("el-radio",{attrs:{label:1}},[t._v("淘宝")]),t._v(" "),a("el-radio",{attrs:{label:2}},[t._v("京东")]),t._v(" "),a("el-radio",{attrs:{label:3}},[t._v("苏宁")]),t._v(" "),a("el-radio",{attrs:{label:4}},[t._v("拼多多")]),t._v(" "),a("el-radio",{attrs:{label:5}},[t._v("天猫")])],1)],1):t._e(),t._v(" "),t.copyConfig.copyType?a("div",{attrs:{span:24}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入链接地址",size:"small"},model:{value:t.url,callback:function(e){t.url=e},expression:"url"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:product:copy:product","admin:product:import:product"],expression:"['admin:product:copy:product', 'admin:product:import:product']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.add},slot:"append"})],1)],1):t._e()]):t._e(),t._v(" "),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-steps",{attrs:{active:t.currentTab,"align-center":"","finish-status":"success"}},[a("el-step",{attrs:{title:"商品信息"}}),t._v(" "),a("el-step",{attrs:{title:"商品详情"}}),t._v(" "),a("el-step",{attrs:{title:"其他设置"}})],1)],1),t._v(" "),a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.fullscreenLoading,expression:"fullscreenLoading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-row",{directives:[{name:"show",rawName:"v-show",value:0===t.currentTab,expression:"currentTab === 0"}],attrs:{gutter:24}},[a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品名称：",prop:"name"}},[a("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品名称",disabled:t.isDisabled},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商户商品分类：",prop:"cateIds"}},[a("el-cascader",{staticClass:"selWidth",attrs:{options:t.merProductClassify,props:t.props2,clearable:"","show-all-levels":!0,disabled:t.isDisabled},model:{value:t.formValidate.cateIds,callback:function(e){t.$set(t.formValidate,"cateIds",e)},expression:"formValidate.cateIds"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"平台商品分类：",prop:"categoryId"}},[a("el-cascader",{staticClass:"selWidth",attrs:{options:t.adminProductClassify,props:t.props1,filterable:"",clearable:"","show-all-levels":!0,disabled:t.isDisabled},on:{change:t.onChangeCategory},model:{value:t.formValidate.categoryId,callback:function(e){t.$set(t.formValidate,"categoryId",e)},expression:"formValidate.categoryId"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"品牌：",prop:"brandId"}},[a("el-select",{directives:[{name:"selectLoadMore",rawName:"v-selectLoadMore",value:t.selectLoadMore,expression:"selectLoadMore"}],staticClass:"selWidth",attrs:{clearable:"",filterable:"",loading:t.loading,remote:"",disabled:t.isDisabled,"remote-method":t.remoteMethod,placeholder:"请选择品牌"},model:{value:t.formValidate.brandId,callback:function(e){t.$set(t.formValidate,"brandId",e)},expression:"formValidate.brandId"}},t._l(t.brandList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[a("el-input",{attrs:{placeholder:"请输入单位",disabled:t.isDisabled},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品简介：",prop:"intro"}},[a("el-input",{attrs:{type:"textarea",maxlength:"250",rows:3,placeholder:"请输入商品简介",disabled:t.isDisabled},model:{value:t.formValidate.intro,callback:function(e){t.$set(t.formValidate,"intro",e)},expression:"formValidate.intro"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品封面图：",prop:"image"}},[a("div",{staticClass:"upLoadPicBox acea-row",attrs:{disabled:t.isDisabled},on:{click:function(e){return t.modalPicTap("1","IMAGE")}}},[t.formValidate.image?a("div",{staticClass:"pictrue"},[t.isDisabled?a("el-image",{attrs:{src:t.formValidate.image,"preview-src-list":[t.formValidate.image]}}):a("el-image",{attrs:{src:t.formValidate.image}})],1):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})]),t._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:!t.isDisabled,expression:"!isDisabled"}]},[t._v("请上传小于500kb的图片")])])])],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"保障服务："}},[a("div",{staticClass:"acea-row row-middle mb5"},[a("el-select",{staticStyle:{width:"76%"},attrs:{placeholder:"请选择保障服务",clearable:"",filterable:"",multiple:t.multiples,disabled:t.isDisabled},on:{change:t.changeGuarantee},model:{value:t.formValidate.guaranteeIdsList,callback:function(e){t.$set(t.formValidate,"guaranteeIdsList",e)},expression:"formValidate.guaranteeIdsList"}},t._l(t.guaranteeNew,(function(t,e){return a("el-option",{key:e,attrs:{value:t.id,label:t.name}})})),1),t._v(" "),a("el-switch",{staticClass:"ml10",attrs:{"active-value":!0,"inactive-value":!1,"active-text":"多选","inactive-text":"单选"},on:{change:t.onchangeIsShow},model:{value:t.isShowGroup,callback:function(e){t.isShowGroup=e},expression:"isShowGroup"}})],1),t._v(" "),t._l(t.guaranteeName,(function(e,r){return a("el-tag",{key:r,staticClass:"mr10"},[t._v(t._s(e))])}))],2)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品关键字：",prop:"labelarr"}},[a("keyword",{attrs:{labelarr:t.labelarr,isDisabled:t.isDisabled},on:{getLabelarr:t.getLabelarr}})],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品轮播图：",prop:"sliderImages"}},[a("div",{staticClass:"acea-row"},[t._l(t.formValidate.sliderImages,(function(e,r){return a("div",{key:r,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnter(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[a("el-image",{attrs:{src:e,"preview-src-list":t.formValidate.sliderImages}}),t._v(" "),a("i",{directives:[{name:"show",rawName:"v-show",value:!t.isDisabled,expression:"!isDisabled"}],staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(r)}}})],1)})),t._v(" "),t.formValidate.sliderImages.length<10&&!t.isDisabled?a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("2","SLIDER_IMAGE")}}},[a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2)])],1),t._v(" "),a("el-col",{attrs:{xs:18,sm:18,md:18,lg:12,xl:12}},[a("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[a("el-select",{staticClass:"mr20",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:t.isDisabled},model:{value:t.formValidate.tempId,callback:function(e){t.$set(t.formValidate,"tempId",e)},expression:"formValidate.tempId"}},t._l(t.shippingTemplates,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品规格：",props:"specType"}},[a("el-radio-group",{attrs:{disabled:t.isDisabled},on:{change:function(e){return t.onChangeSpec(t.formValidate.specType)}},model:{value:t.formValidate.specType,callback:function(e){t.$set(t.formValidate,"specType",e)},expression:"formValidate.specType"}},[a("el-radio",{staticClass:"radio",attrs:{label:!1}},[t._v("单规格")]),t._v(" "),a("el-radio",{attrs:{label:!0}},[t._v("多规格")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"佣金设置：",props:"isSub"}},[a("el-radio-group",{attrs:{disabled:t.isDisabled},on:{change:function(e){return t.onChangetype(t.formValidate.isSub)}},model:{value:t.formValidate.isSub,callback:function(e){t.$set(t.formValidate,"isSub",e)},expression:"formValidate.isSub"}},[a("el-radio",{staticClass:"radio",attrs:{label:!0}},[t._v("单独设置")]),t._v(" "),a("el-radio",{attrs:{label:!1}},[t._v("默认设置")])],1)],1)],1),t._v(" "),t.formValidate.specType&&!t.isDisabled?a("el-col",{staticClass:"noForm",attrs:{span:24}},[a("el-form-item",{attrs:{label:"选择规格：",prop:""}},[a("div",{staticClass:"acea-row"},[a("el-select",{model:{value:t.formValidate.selectRule,callback:function(e){t.$set(t.formValidate,"selectRule",e)},expression:"formValidate.selectRule"}},t._l(t.ruleList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.ruleName,value:t.id}})})),1),t._v(" "),a("el-button",{staticClass:"mr20",attrs:{type:"primary",disabled:!t.formValidate.selectRule},on:{click:t.confirm}},[t._v("确认")]),t._v(" "),a("el-button",{staticClass:"mr15",on:{click:t.addRule}},[t._v("添加规格")])],1)]),t._v(" "),a("el-form-item",t._l(t.formValidate.attr,(function(e,r){return a("div",{key:r},[a("div",{staticClass:"acea-row row-middle"},[a("span",{staticClass:"mr5"},[t._v(t._s(e.attrName))]),a("i",{staticClass:"el-icon-circle-close",on:{click:function(e){return t.handleRemoveAttr(r)}}})]),t._v(" "),a("div",{staticClass:"rulesBox"},[t._l(e.attrValue,(function(r,i){return a("el-tag",{key:i,staticClass:"mb5 mr10",attrs:{closable:"",size:"medium","disable-transitions":!1},on:{close:function(a){return t.handleClose(e.attrValue,i)}}},[t._v("\n                  "+t._s(r)+"\n                ")])})),t._v(" "),e.inputVisible?a("el-input",{ref:"saveTagInput",refInFor:!0,staticClass:"input-new-tag",attrs:{size:"small"},on:{blur:function(a){return t.createAttr(e.attrValue.attrsVal,r)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.createAttr(e.attrValue.attrsVal,r)}},model:{value:e.attrValue.attrsVal,callback:function(a){t.$set(e.attrValue,"attrsVal",a)},expression:"item.attrValue.attrsVal"}}):a("el-button",{staticClass:"button-new-tag",attrs:{size:"small"},on:{click:function(a){return t.showInput(e)}}},[t._v("+ 添加")])],2)])})),0),t._v(" "),a("el-row",[a("el-col",{attrs:{xl:8,lg:8,md:12,sm:24,xs:24}},[a("el-form-item",{attrs:{label:"规格："}},[a("el-input",{attrs:{placeholder:"请输入规格"},model:{value:t.formDynamic.attrsName,callback:function(e){t.$set(t.formDynamic,"attrsName",e)},expression:"formDynamic.attrsName"}})],1)],1),t._v(" "),a("el-col",{attrs:{xl:8,lg:8,md:12,sm:24,xs:24}},[a("el-form-item",{attrs:{label:"规格值："}},[a("el-input",{attrs:{placeholder:"请输入规格值"},model:{value:t.formDynamic.attrsVal,callback:function(e){t.$set(t.formDynamic,"attrsVal",e)},expression:"formDynamic.attrsVal"}})],1)],1),t._v(" "),a("el-col",{attrs:{xl:8,lg:8,md:12,sm:24,xs:24}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.createAttrName}},[t._v("确定")]),t._v(" "),a("el-button",{on:{click:t.offAttrName}},[t._v("取消")])],1)],1)],1),t._v(" "),t.isBtn?t._e():a("el-form-item",[a("el-button",{staticClass:"mr15",attrs:{type:"primary",icon:"md-add"},on:{click:t.addBtn}},[t._v("添加新规格")])],1)],1):t._e(),t._v(" "),t.formValidate.attr.length>0&&t.formValidate.specType&&!t.isDisabled?a("el-col",{staticClass:"noForm",attrs:{span:24}},[a("el-form-item",{attrs:{label:"批量设置："}},[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.oneFormBatch,border:"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1","BATCH_IMAGE")}}},[e.row.image?a("div",{staticClass:"pictrue tabPic"},[a("img",{attrs:{src:e.row.image}})]):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,1720607395)}),t._v(" "),"eyeglass"==t.type?[a("el-table-column",{attrs:{align:"center",label:"试戴图","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(a){return t.modalPicTap("1","BATCH_WEAR",e.$index)}}},[e.row.wearImage?a("div",{staticClass:"pictrue tabPic"},[t.isDisabled?a("el-image",{attrs:{src:e.row.wearImage,"preview-src-list":[e.row.wearImage]}}):a("el-image",{attrs:{src:e.row.wearImage}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,220476910)})]:t._e(),t._v(" "),t.formValidate.isSub?[a("el-table-column",{attrs:{align:"center",label:"一级返佣(%)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{type:"number",min:0,max:e.row.price},model:{value:e.row.brokerage,callback:function(a){t.$set(e.row,"brokerage",a)},expression:"scope.row.brokerage"}})]}}],null,!1,99765219)}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"二级返佣(%)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{type:"number",min:0,max:e.row.price},model:{value:e.row.brokerageTwo,callback:function(a){t.$set(e.row,"brokerageTwo",a)},expression:"scope.row.brokerageTwo"}})]}}],null,!1,1573915567)})]:t._e(),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":t.formThead[r].width?t.formThead[r].width:120},scopedSlots:t._u([{key:"default",fn:function(e){return[["可配最大度数（近视）","可配最大度数（远视）"].includes(t.formThead[r].title)?a("el-select",{model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}},t._l(t.sphOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1):["适用最低散光","适用最高散光"].includes(t.formThead[r].title)?a("el-select",{model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}},t._l(t.cylOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1):"镜架参数"===t.formThead[r].title?a("el-input",{staticClass:"priceBox",attrs:{type:"text"},nativeOn:{keyup:function(a){return t.keyupEvent(r,e.row[r],e.$index,"BATCH")}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}}):a("el-input",{staticClass:"priceBox",attrs:{type:"int"===t.formThead[r].type?"number":"text",min:0},nativeOn:{keyup:function(a){return t.keyupEvent(r,e.row[r],e.$index,"BATCH")}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)},["镜架参数"===t.formThead[r].title?a("template",{slot:"header"},[a("el-popover",{attrs:{placement:"top-start",title:"镜架参数输入参考",width:"260",trigger:"hover"}},[a("p",[t._v("格式如下：")]),t._v(" "),a("p",[t._v("镜框宽-鼻梁宽-镜腿长-镜架宽-镜框高")]),t._v(" "),a("p",[t._v("参考示例：")]),t._v(" "),a("p",[t._v("52-18-138-143-48")]),t._v(" "),a("div",{staticClass:"title-reference",attrs:{slot:"reference"},slot:"reference"},[a("span",[t._v(t._s(t.formThead[r].title))]),a("i",{staticClass:"el-icon-warning-outline",staticStyle:{"margin-left":"4px","font-size":"14px","line-height":"inherit"}})])])],1):t._e()],2)})),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"}},[[a("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:t.batchAdd}},[t._v("批量添加")])]],2)],2)],1)],1):t._e(),t._v(" "),a("el-col",{attrs:{xl:24,lg:24,md:24,sm:24,xs:24}},[!1===t.formValidate.specType?a("el-form-item",[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.OneAttrValue,border:"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1","SINGLE_IMAGE")}}},[e.row.image?a("div",{staticClass:"pictrue tabPic"},[t.isDisabled?a("el-image",{attrs:{src:e.row.image,"preview-src-list":[e.row.image]}}):a("el-image",{attrs:{src:e.row.image}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,2947046108)}),t._v(" "),"eyeglass"==t.type?[a("el-table-column",{attrs:{align:"center",label:"试戴图","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1","SINGLE_WEAR")}}},[e.row.wearImage?a("div",{staticClass:"pictrue tabPic"},[t.isDisabled?a("el-image",{attrs:{src:e.row.wearImage,"preview-src-list":[e.row.wearImage]}}):a("el-image",{attrs:{src:e.row.wearImage}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,1247529562)})]:t._e(),t._v(" "),t.formValidate.isSub?[a("el-table-column",{attrs:{align:"center",label:"一级返佣(%)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0},model:{value:e.row.brokerage,callback:function(a){t.$set(e.row,"brokerage",a)},expression:"scope.row.brokerage"}})]}}],null,!1,3549059968)}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"二级返佣(%)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0},model:{value:e.row.brokerageTwo,callback:function(a){t.$set(e.row,"brokerageTwo",a)},expression:"scope.row.brokerageTwo"}})]}}],null,!1,342384460)})]:t._e(),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":t.formThead[r].width?t.formThead[r].width:120},scopedSlots:t._u([{key:"default",fn:function(e){return[["可配最大度数（近视）","可配最大度数（远视）"].includes(t.formThead[r].title)?a("el-select",{attrs:{disabled:t.isDisabled},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}},t._l(t.sphOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1):["适用最低散光","适用最高散光"].includes(t.formThead[r].title)?a("el-select",{attrs:{disabled:t.isDisabled},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}},t._l(t.cylOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1):"镜架参数"===t.formThead[r].title?a("el-input",{staticClass:"priceBox",attrs:{type:"text"},nativeOn:{keyup:function(a){return t.keyupEvent(r,e.row[r],e.$index,"SINGLE")}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}}):a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"int"===t.formThead[r].type?"number":"text",min:0},nativeOn:{keyup:function(a){return t.keyupEvent(r,e.row[r],e.$index,"SINGLE")}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)},["镜架参数"===t.formThead[r].title?a("template",{slot:"header"},[a("el-popover",{attrs:{placement:"top-start",title:"镜架参数输入参考",width:"260",trigger:"hover"}},[a("p",[t._v("格式如下：")]),t._v(" "),a("p",[t._v("镜框宽-鼻梁宽-镜腿长-镜架宽-镜框高")]),t._v(" "),a("p",[t._v("参考示例：")]),t._v(" "),a("p",[t._v("52-18-138-143-48")]),t._v(" "),a("div",{staticClass:"title-reference",attrs:{slot:"reference"},slot:"reference"},[a("span",[t._v(t._s(t.formThead[r].title))]),a("i",{staticClass:"el-icon-warning-outline",staticStyle:{"margin-left":"4px","font-size":"14px","line-height":"inherit"}})])])],1):t._e()],2)}))],2)],1):t._e(),t._v(" "),t.$route.params.id&&t.showAll?a("el-form-item",{attrs:{label:"全部sku："}},[a("el-button",{attrs:{type:"default",disabled:t.isDisabled},on:{click:function(e){return t.showAllSku()}}},[t._v("展示")])],1):t._e(),t._v(" "),t.formValidate.attr.length>0&&t.formValidate.specType?a("el-form-item",{staticClass:"labeltop",class:t.isDisabled?"disLabel":"disLabelmoren",attrs:{label:"商品属性："}},[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.ManyAttrValue,border:"",size:"mini"}},[t.manyTabTit?t._l(t.manyTabTit,(function(e,r){return a("el-table-column",{key:r,attrs:{align:"center",label:r,"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[r])}})]}}],null,!0)})})):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(a){return t.modalPicTap("1","MULTI_IMAGE",e.$index)}}},[e.row.image?a("div",{staticClass:"pictrue tabPic"},[t.isDisabled?a("el-image",{attrs:{src:e.row.image,"preview-src-list":[e.row.image]}}):a("el-image",{attrs:{src:e.row.image}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,1355990845)}),t._v(" "),"eyeglass"==t.type?[a("el-table-column",{attrs:{align:"center",label:"试戴图","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(a){return t.modalPicTap("1","MULTI_WEAR",e.$index)}}},[e.row.wearImage?a("div",{staticClass:"pictrue tabPic"},[t.isDisabled?a("el-image",{attrs:{src:e.row.wearImage,"preview-src-list":[e.row.wearImage]}}):a("el-image",{attrs:{src:e.row.wearImage}})],1):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,2740924347)})]:t._e(),t._v(" "),t.formValidate.isSub?[a("el-table-column",{attrs:{align:"center",label:"一级返佣(%)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0},model:{value:e.row.brokerage,callback:function(a){t.$set(e.row,"brokerage",a)},expression:"scope.row.brokerage"}})]}}],null,!1,3549059968)}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"二级返佣(%)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0},model:{value:e.row.brokerageTwo,callback:function(a){t.$set(e.row,"brokerageTwo",a)},expression:"scope.row.brokerageTwo"}})]}}],null,!1,342384460)})]:t._e(),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":t.formThead[r].width?t.formThead[r].width:120},scopedSlots:t._u([{key:"default",fn:function(e){return[["可配最大度数（近视）","可配最大度数（远视）"].includes(t.formThead[r].title)?a("el-select",{attrs:{disabled:t.isDisabled},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}},t._l(t.sphOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1):["适用最低散光","适用最高散光"].includes(t.formThead[r].title)?a("el-select",{attrs:{disabled:t.isDisabled},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}},t._l(t.cylOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1):"镜架参数"===t.formThead[r].title?a("el-input",{staticClass:"priceBox",attrs:{type:"text"},nativeOn:{keyup:function(a){return t.keyupEvent(r,e.row[r],e.$index,"MULTI")}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}}):a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"int"===t.formThead[r].type?"number":"text"},nativeOn:{keyup:function(a){return t.keyupEvent(r,e.row[r],e.$index,"MULTI")}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)},["镜架参数"===t.formThead[r].title?a("template",{slot:"header"},[a("el-popover",{attrs:{placement:"top-start",title:"镜架参数输入参考",width:"260",trigger:"hover"}},[a("p",[t._v("格式如下：")]),t._v(" "),a("p",[t._v("镜框宽-鼻梁宽-镜腿长-镜架宽-镜框高")]),t._v(" "),a("p",[t._v("参考示例：")]),t._v(" "),a("p",[t._v("52-18-138-143-48")]),t._v(" "),a("div",{staticClass:"title-reference",attrs:{slot:"reference"},slot:"reference"},[a("span",[t._v(t._s(t.formThead[r].title))]),a("i",{staticClass:"el-icon-warning-outline",staticStyle:{"margin-left":"4px","font-size":"14px","line-height":"inherit"}})])])],1):t._e()],2)})),t._v(" "),t.isDisabled?t._e():a("el-table-column",{key:"3",attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:function(a){return t.delAttrTable(e.$index)}}},[t._v("删除")])]}}],null,!1,2803824461)})],2)],1):t._e()],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:1===t.currentTab&&!t.isDisabled,expression:"currentTab === 1 && !isDisabled"}]},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("Tinymce",{model:{value:t.formValidate.content,callback:function(e){t.$set(t.formValidate,"content",e)},expression:"formValidate.content"}})],1)],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:1===t.currentTab&&t.isDisabled,expression:"currentTab === 1 && isDisabled"}]},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("span",{domProps:{innerHTML:t._s(t.formValidate.content||"无")}})])],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:2===t.currentTab,expression:"currentTab === 2"}]},[a("el-col",{attrs:{span:24}},[a("el-col",t._b({},"el-col",t.grid,!1),[a("el-form-item",{attrs:{label:"排序："}},[a("el-input-number",{attrs:{min:1,max:9999,placeholder:"请输入排序",disabled:t.isDisabled},nativeOn:{keyup:function(e){return t.proving1(e)}},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"用户购买商品后赠送的优惠券",placement:"top-start"}},[a("i",{staticClass:"el-icon-warning"})]),t._v(" "),a("el-form-item",{staticClass:"proCoupon",attrs:{label:"赠送优惠券："}},[a("div",{staticClass:"acea-row"},[t._l(t.formValidate.coupons,(function(e,r){return a("el-tag",{key:r,staticClass:"mr10 mb10",attrs:{closable:!t.isDisabled,"disable-transitions":!1},on:{close:function(a){return t.handleCloseCoupon(e)}}},[t._v("\n                "+t._s(e.name)+"\n              ")])})),t._v(" "),null==t.formValidate.couponIds?a("span",{staticClass:"mr15"},[t._v("无")]):t._e(),t._v(" "),t.isDisabled?t._e():a("el-button",{staticClass:"mr15",attrs:{size:"mini"},on:{click:t.addCoupon}},[t._v("选择优惠券")])],2)])],1)],1),t._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.currentTab>0,expression:"currentTab > 0"}],staticClass:"submission priamry_border",on:{click:t.handleSubmitUp}},[t._v("上一步")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.currentTab<2,expression:"currentTab < 2"}],staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmitNest("formValidate")}}},[t._v("下一步")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:(2===t.currentTab||t.$route.params.id)&&!t.isDisabled,expression:"(currentTab === 2 || $route.params.id) && !isDisabled"}],staticClass:"submission",attrs:{type:"primary",loading:t.loadingBtn},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1)],1)},i=[],n=a("8256"),s=a("c4c8"),o=a("2f62"),l=a("8492"),c=a("c9d9");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var t={},e=Object.prototype,a=e.hasOwnProperty,r=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},n=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",o=i.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(S){l=function(t,e,a){return t[e]=a}}function c(t,e,a,i){var n=e&&e.prototype instanceof p?e:p,s=Object.create(n.prototype),o=new T(i||[]);return r(s,"_invoke",{value:k(t,a,o)}),s}function m(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(S){return{type:"throw",arg:S}}}t.wrap=c;var f={};function p(){}function h(){}function g(){}var b={};l(b,n,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v($([])));y&&y!==e&&a.call(y,n)&&(b=y);var w=g.prototype=p.prototype=Object.create(b);function V(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function i(r,n,s,o){var l=m(t[r],t,n);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==u(d)&&a.call(d,"__await")?e.resolve(d.__await).then((function(t){i("next",t,s,o)}),(function(t){i("throw",t,s,o)})):e.resolve(d).then((function(t){c.value=t,s(c)}),(function(t){return i("throw",t,s,o)}))}o(l.arg)}var n;r(this,"_invoke",{value:function(t,a){function r(){return new e((function(e,r){i(t,a,e,r)}))}return n=n?n.then(r,r):r()}})}function k(t,e,a){var r="suspendedStart";return function(i,n){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw n;return L()}for(a.method=i,a.arg=n;;){var s=a.delegate;if(s){var o=C(s,a);if(o){if(o===f)continue;return o}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var l=m(t,e,a);if("normal"===l.type){if(r=a.done?"completed":"suspendedYield",l.arg===f)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r="completed",a.method="throw",a.arg=l.arg)}}}function C(t,e){var a=e.method,r=t.iterator[a];if(void 0===r)return e.delegate=null,"throw"===a&&t.iterator.return&&(e.method="return",e.arg=void 0,C(t,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),f;var i=m(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,f;var n=i.arg;return n?n.done?(e[t.resultName]=n.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function $(t){if(t){var e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(a.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:L}}function L(){return{value:void 0,done:!0}}return h.prototype=g,r(w,"constructor",{value:g,configurable:!0}),r(g,"constructor",{value:h,configurable:!0}),h.displayName=l(g,o,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,l(t,o,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},V(_.prototype),l(_.prototype,s,(function(){return this})),t.AsyncIterator=_,t.async=function(e,a,r,i,n){void 0===n&&(n=Promise);var s=new _(c(e,a,r,i),n);return t.isGeneratorFunction(a)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},V(w),l(w,o,"Generator"),l(w,n,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=$,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(a,r){return s.type="throw",s.arg=t,e.next=a,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],s=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var o=a.call(n,"catchLoc"),l=a.call(n,"finallyLoc");if(o&&l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(o){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var s=n?n.completion:{};return s.type=t,s.arg=e,n?(this.method="next",this.next=n.finallyLoc,f):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),x(a),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var i=r.arg;x(a)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,a){return this.delegate={iterator:$(t),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=void 0),f}},t}function m(t,e,a,r,i,n,s){try{var o=t[n](s),l=o.value}catch(c){return void a(c)}o.done?e(l):Promise.resolve(l).then(r,i)}function f(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var n=t.apply(e,a);function s(t){m(n,r,i,s,o,"next",t)}function o(t){m(n,r,i,s,o,"throw",t)}s(void 0)}))}}function p(t,e){var a="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=b(t))||e&&t&&"number"===typeof t.length){a&&(t=a);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,s=!0,o=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return s=t.done,t},e:function(t){o=!0,n=t},f:function(){try{s||null==a.return||a.return()}finally{if(o)throw n}}}}function h(t){return y(t)||v(t)||b(t)||g()}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t,e){if(t){if("string"===typeof t)return w(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?w(t,e):void 0}}function v(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function y(t){if(Array.isArray(t))return w(t)}function w(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function V(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function _(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?V(Object(a),!0).forEach((function(e){k(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):V(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function k(t,e,a){return e=C(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function C(t){var e=I(t,"string");return"symbol"===u(e)?e:String(e)}function I(t,e){if("object"!==u(t)||null===t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var x={image:"",sliderImages:[],sliderImage:"",name:"",intro:"",keyword:"",cateIds:[],cateId:null,unitName:"",sort:0,isShow:!1,tempId:"",attrValue:[{image:"",wearImage:"",smin:"0.00",smax:"0.00",cmin:"0.00",cmax:"0.00",price:0,cost:0,otPrice:0,stock:0,spec:"",barCode:"",weight:0,volume:0,brokerage:0,brokerageTwo:0}],attr:[],selectRule:"",isSub:!1,content:"",specType:!1,id:void 0,couponIds:[],coupons:[],categoryId:0,guaranteeIds:"",guaranteeIdsList:[]||!1,brandId:""},T={price:{title:"售价（元）",type:"number"},cost:{title:"成本价（元）",type:"number"},otPrice:{title:"原价（元）",type:"number"},stock:{title:"库存",type:"int"},barCode:{title:"商品编号",type:"int"},spec:{title:"镜架参数",type:"string",width:170},weight:{title:"重量（KG）",type:"number"},volume:{title:"体积(m³)",type:"number"},brokerage:{title:"一级返佣(%)",type:"number"},brokerageTwo:{title:"二级返佣(%)",type:"number"},smin:{title:"可配最大度数（近视）",type:"int"},smax:{title:"可配最大度数（远视）",type:"int"},cmin:{title:"适用最低散光",type:"int"},cmax:{title:"适用最高散光",type:"int"}},$={string:function(t){return t},number:function(t){return"string"==typeof t?t.replace(/^\D*([0-9]\d*\.?\d{0,3})?.*$/,"$1"):t},int:function(t){if(""!==t)try{t=parseInt(t)}catch(e){t=""}return t}},L={name:"ProductProductAdd",directives:{selectLoadMore:{bind:function(t,e){var a=t.querySelector(".el-select-dropdown .el-select-dropdown__wrap");a.addEventListener("scroll",(function(){this.scrollHeight-this.scrollTop<this.clientHeight+1&&e.value()}))}}},components:{Tinymce:n["a"]},data:function(){var t=this,e=function(e,a,r){0===t.labelarr.length?r(new Error("请输入后回车")):r()};return{url:"",merInfo:{},copyConfig:{},labelarr:[],isDisabled:"1"===this.$route.params.isDisabled,isCopy:"1"===this.$route.params.isCopy,props2:{children:"childList",label:"name",value:"id",multiple:!0,emitPath:!0,checkStrictly:!1},props1:{children:"childList",label:"name",value:"id",multiple:!1,emitPath:!1},tabs:[],fullscreenLoading:!1,props:{multiple:!0},active:0,OneAttrValue:[Object.assign({},x.attrValue[0])],ManyAttrValue:[Object.assign({},x.attrValue[0])],ruleList:[],merCateList:[],shippingList:[],formThead:Object.assign({},T),formValidate:Object.assign({},x),formDynamics:{ruleName:"",ruleValue:[]},tempData:{page:1,limit:9999},manyTabTit:{},grid2:{xl:12,lg:12,md:12,sm:24,xs:24},formDynamic:{attrsName:"",attrsVal:""},isBtn:!1,manyFormValidate:[],currentTab:0,isChoice:"",grid:{xl:8,lg:8,md:12,sm:24,xs:24},ruleValidate:{name:[{required:!0,message:"请输入商品名称",trigger:"blur"}],categoryId:[{required:!0,message:"请选择平台商品分类",trigger:"change"}],cateIds:[{required:!0,message:"请选择商户商品分类",trigger:"change",type:"array",min:"1"}],labelarr:[{required:!0,validator:e,trigger:"blur"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],intro:[{required:!0,message:"请输入商品简介",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change"}],image:[{required:!0,message:"请上传商品图",trigger:"change"}],sliderImages:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}],brandId:[{required:!0,message:"请选择商品品牌",trigger:"change"}]},attrInfo:{},tableFrom:{page:1,limit:9999,keywords:""},tempRoute:{},keyNum:0,isAttr:!1,showAll:!1,guaranteeList:[],brandList:[],search:{limit:10,page:1,cid:"",brandName:""},totalPage:0,total:0,loading:!1,loadingBtn:!1,isShowGroup:!1,guaranteeGroupList:[],guaranteeNew:[],guaranteeName:[],multiples:!0,isLens:!1,sphOptions:c["sphOptions"],cylOptions:c["cylOptions"],type:"others"}},computed:_(_({visitedViews:function(){return this.$store.state.tagsView.visitedViews}},Object(o["b"])(["adminProductClassify","adminLensClassify","adminEyeglassClassify","merProductClassify","productBrand","shippingTemplates"])),{},{attrValue:function(){var t=Object.assign({},x.attrValue[0]);return delete t.image,delete t.brokerage,delete t.brokerageTwo,delete t.wearImage,"eyeglass"!=this.type&&delete t.spec,"lens"!=this.type&&(delete t.smin,delete t.smax,delete t.cmin,delete t.cmax),t},oneFormBatch:function(){var t=[Object.assign({},x.attrValue[0])];return delete t[0].barCode,t}}),watch:{"formValidate.attr":{handler:function(t){this.formValidate.specType&&this.isAttr&&this.watchAttr(t)},immediate:!1,deep:!0}},created:function(){this.tempRoute=Object.assign({},this.$route),parseFloat(this.$route.params.id)>0&&this.formValidate.specType&&this.$watch("formValidate.attr",this.watchAttr),this.getCopyConfig(),this.getMerInfo()},mounted:function(){this.formValidate.sliderImages=[],this.adminProductClassify.length||this.$store.dispatch("product/getAdminProductClassify"),this.merProductClassify.length||this.$store.dispatch("product/getMerProductClassify"),this.productBrand.length||this.$store.dispatch("product/getMerProductBrand"),this.shippingTemplates.length||this.$store.dispatch("product/getShippingTemplates"),this.getProductGuarantee(),this.getGuaranteeGroupList(),this.setTagsViewTitle(),this.$route.params.id&&0!=this.$route.params.id&&this.getInfo()},methods:{add:function(){var t=this;this.url?(this.loading=!0,this.isAttr=!0,Object(s["h"])({url:this.url}).then((function(e){t.getData(e.info),t.loading=!1})).catch((function(){t.loading=!1}))):this.$message.warning("请输入链接地址！")},getCopyConfig:function(){var t=this;Object(s["g"])().then((function(e){t.copyConfig=e}))},getMerInfo:function(){var t=this;Object(l["a"])().then((function(e){t.merInfo=e}))},changeGuarantee:function(t){var e=this;if(t){if(this.guaranteeName=[],this.isShowGroup){var a=[];this.guaranteeGroupList.filter((function(e){return e.id===t}))[0].guaranteeList.map((function(t){return t.isShow&&a.push(t.gid)})),this.formValidate.guaranteeIds=a.toString(),this.guaranteeList.map((function(t){a.map((function(a){t.id===a&&e.guaranteeName.push(t.name)}))}))}}else this.guaranteeName=[]},onchangeIsShow:function(){this.multiples=!this.multiples,this.guaranteeName=[],this.formValidate.guaranteeIdsList=[]||!1,this.isShowGroup?this.guaranteeNew=this.guaranteeGroupList:(this.guaranteeNew=this.guaranteeList,this.formValidate.guaranteeIdsList=[]||!1)},getGuaranteeGroupList:function(){var t=this;Object(s["q"])().then((function(e){t.guaranteeGroupList=e}))},getLabelarr:function(t){this.labelarr=t},changeNodes:function(t){if(t.length>0)for(var e=0;e<t.length;e++)!t[e].childList||t[e].childList.length<1?t[e].childList=void 0:this.changeNodes(t[e].childList);return t},getProductGuarantee:function(){var t=this;Object(s["H"])().then((function(e){t.guaranteeList=e.filter((function(t){return t.isShow})),t.guaranteeNew=t.guaranteeList}))},computeType:function(){var t=this,e=this.adminEyeglassClassify.find((function(e){return e.id===t.formValidate.categoryId}));if(e)this.type="eyeglass";else{var a=this.adminLensClassify.find((function(e){return e.id===t.formValidate.categoryId}));this.type=a?"lens":"others"}},onChangeCategory:function(){this.formValidate.brandId="",this.computeType(),this.getbrandList()},selectLoadMore:function(){this.search.limit=this.search.limit+1,this.search.limit>this.totalPage||this.getbrandList()},remoteMethod:function(t){var e=this;this.loading=!0,this.search.keywords=t,this.search.limit=1,this.brandList=[],setTimeout((function(){e.loading=!1,e.getbrandList()}),200)},getbrandList:function(){var t=this;this.search.cid=this.formValidate.categoryId,Object(s["e"])(this.search).then((function(e){e.list.length?(e.list.push({name:"其他",id:0}),t.brandList=e.list):(t.$message.warning("此分类下面无品牌，请联系管理员添加此分类下的品牌！"),t.brandList=[{name:"此分类下面无品牌，请联系管理员添加此分类下的品牌！",id:void 0}])}))},keyupEvent:function(t,e,a,r){var i=this.formThead[t].type;switch(e=$[i](e),r){case"BATCH":this.$set(this.oneFormBatch[a],t,e);break;case"SINGLE":this.$set(this.OneAttrValue[a],t,e);break;case"MULTI":this.$set(this.ManyAttrValue[a],t,e);break;default:break}},proving1:function(t){this.formValidate.sort=t.target.value.replace(/[^\.\d]/g,""),this.formValidate.sort=t.target.value.replace(".","")},handleCloseCoupon:function(t){this.isAttr=!0,this.formValidate.coupons.splice(this.formValidate.coupons.indexOf(t),1),this.formValidate.couponIds.splice(this.formValidate.couponIds.indexOf(t.id),1)},addCoupon:function(){var t=this;this.$modalCoupon("wu",this.keyNum+=1,this.formValidate.coupons,(function(e){t.formValidate.couponIds=[],t.formValidate.coupons=e,e.map((function(e){t.formValidate.couponIds.push(e.id)}))}),"")},setTagsViewTitle:function(){if(this.$route.params.id&&0!=this.$route.params.id){var t=this.isDisabled?"商品详情":"编辑商品",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)}else if(this.isCopy){var a="采集商品",r=Object.assign({},this.tempRoute,{title:"".concat(a)});this.$store.dispatch("tagsView/updateVisitedView",r)}},watchAttr:function(t){var e=this,a={};this.formValidate.attr.forEach((function(t,e){a[t.attrName]={title:t.attrName,type:"string"}})),this.ManyAttrValue=this.attrFormat(t),this.formValidate.attrValue&&(this.ManyAttrValue.forEach((function(t,a){var r=Object.values(t.attrValue).sort().join("/");e.attrInfo[r]&&(e.ManyAttrValue[a]=e.attrInfo[r])})),this.attrInfo={},this.ManyAttrValue.forEach((function(t){e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t}))),this.manyTabTit=a,this.formThead=Object.assign({},this.formThead,a)},attrFormat:function(t){var e=[],a=[];t.forEach((function(t){e.push(t.attrName),a.push(t.attrValue)}));var r=this.computeSKU.apply(this,a);return r.map((function(t){var a=t.split("$&"),r={};return a.forEach((function(t,a){var i=e[a];r[i]=t})),_({image:"",wearImage:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",spec:"",weight:0,volume:0,brokerage:0,brokerageTwo:0,attrValue:r},r)}))},computeSKU:function(){var t=Array.prototype.slice.apply(arguments),e=t.length;if(0==e)return[];if(1==e)return t.shift();var a=t.shift(),r=t.shift(),i=[];return a.forEach((function(t){r.forEach((function(e){i.push(t+"$&"+e)}))})),this.computeSKU.apply(this,[i].concat(h(t)))},addRule:function(){var t=this;this.$modalAttr(this.formDynamics,(function(){t.productGetRule()}))},onChangeSpec:function(t){this.isAttr=!0,t&&(this.formValidate.attr=[],this.productGetRule())},confirm:function(){var t=this;if(this.isAttr=!0,!this.formValidate.selectRule)return this.$message.warning("请选择属性");var e=this.ruleList.find((function(e){return e.id===t.formValidate.selectRule}));e&&(this.formValidate.attr=e.ruleValue.map((function(t){var e=t.value,a=t.detail;return{attrName:e,attrValue:a}})))},productGetRule:function(){var t=this;Object(s["U"])(this.tableFrom).then((function(e){for(var a=e.list,r=0;r<a.length;r++)a[r].ruleValue=JSON.parse(a[r].ruleValue);t.ruleList=a}))},showInput:function(t){this.$set(t,"inputVisible",!0)},onChangetype:function(t){},delAttrTable:function(t){this.ManyAttrValue.splice(t,1)},batchAdd:function(){var t,e=p(this.ManyAttrValue);try{for(e.s();!(t=e.n()).done;){var a=t.value;this.$set(a,"image",this.oneFormBatch[0].image),this.$set(a,"wearImage",this.oneFormBatch[0].wearImage),this.$set(a,"price",this.oneFormBatch[0].price),this.$set(a,"cost",this.oneFormBatch[0].cost),this.$set(a,"otPrice",this.oneFormBatch[0].otPrice),this.$set(a,"barCode",this.oneFormBatch[0].barCode),this.$set(a,"spec",this.oneFormBatch[0].spec),this.$set(a,"stock",this.oneFormBatch[0].stock),this.$set(a,"weight",this.oneFormBatch[0].weight),this.$set(a,"volume",this.oneFormBatch[0].volume),this.$set(a,"brokerage",this.oneFormBatch[0].brokerage),this.$set(a,"brokerageTwo",this.oneFormBatch[0].brokerageTwo),this.$set(a,"smin",this.oneFormBatch[0].smin),this.$set(a,"smax",this.oneFormBatch[0].smax),this.$set(a,"cmin",this.oneFormBatch[0].cmin),this.$set(a,"cmax",this.oneFormBatch[0].cmax)}}catch(r){e.e(r)}finally{e.f()}},addBtn:function(){this.clearAttr(),this.isBtn=!0},offAttrName:function(){this.isBtn=!1},clearAttr:function(){this.isAttr=!0,this.formDynamic.attrsName="",this.formDynamic.attrsVal=""},handleRemoveAttr:function(t){this.isAttr=!0,this.formValidate.attr.splice(t,1),this.manyFormValidate.splice(t,1)},handleClose:function(t,e){t.splice(e,1)},createAttrName:function(){if(this.isAttr=!0,this.formDynamic.attrsName&&this.formDynamic.attrsVal){var t={attrName:this.formDynamic.attrsName,attrValue:[this.formDynamic.attrsVal]};this.formValidate.attr.push(t);var e={};this.formValidate.attr=this.formValidate.attr.reduce((function(t,a){return!e[a.attrName]&&(e[a.attrName]=t.push(a)),t}),[]),this.clearAttr(),this.isBtn=!1}else this.$Message.warning("请添加完整的规格！")},createAttr:function(t,e){if(this.isAttr=!0,t){this.formValidate.attr[e].attrValue.push(t);var a={};this.formValidate.attr[e].attrValue=this.formValidate.attr[e].attrValue.reduce((function(t,e){return!a[e]&&(a[e]=t.push(e)),t}),[]),this.formValidate.attr[e].inputVisible=!1}else this.$message.warning("请添加属性")},showAllSku:function(){0==this.isAttr?(this.isAttr=!0,this.formValidate.specType&&this.isAttr&&this.watchAttr(this.formValidate.attr)):1==this.isAttr&&(this.isAttr=!1,this.getInfo())},listPath:function(t,e,a){if(0==t.pid&&(a=[],e=[]),Array.isArray(t.childList)&&t.childList.length>0){e.push(t.id+"");for(var r=0,i=t.childList.length;r<i;r++){var n=t.childList[r];this.listPath(n,e.slice(),a)}}else e.push(t.id+""),a.push(e);return a},formatCates:function(t){for(var e=[],a=0,r=t.length;a<r;a++)e=e.concat(this.listPath(t[a]));return e},getData:function(t){var e=this,a=t,r=a.cateId?a.cateId.split(","):[];if(this.props2.emitPath){var i=this.formatCates(this.merProductClassify),n=i.filter((function(t){return t.every((function(t){return r.includes(t)}))}));r=n}var o=a.attr?a.attr:[],l=a.attrValue?a.attrValue:[];l.forEach((function(t){t.attrValue=JSON.parse(t.attrValue),Object.assign(t,t.attrValue)})),a.attrValue=l;var c=a.sliderImage,u=JSON.parse(c);if(u=u.map((function(t){return e.$selfUtil.setDomain(t)})),this.formValidate={image:this.$selfUtil.setDomain(a.image),sliderImage:c,sliderImages:u,name:a.name,intro:a.intro,keyword:a.keyword,cateIds:r,cateId:a.cateId,unitName:a.unitName,sort:a.sort?a.sort:0,isShow:a.isShow,tempId:a.tempId,attr:o,attrValue:l,selectRule:a.selectRule,isSub:!!a.isSub&&a.isSub,content:this.$selfUtil.replaceImgSrcHttps(a.content),specType:a.specType,id:a.id?a.id:0,coupons:a.coupons?a.coupons:[],couponIds:a.couponIds?a.couponIds:[],brandId:a.brandId,categoryId:a.categoryId,guaranteeIds:a.guaranteeIds,guaranteeIdsList:a.guaranteeIds?a.guaranteeIds.split(",").map(Number):[]},this.computeType(),this.labelarr=a.keyword.split(",")||[],this.formValidate.categoryId&&this.getbrandList(),this.formValidate.couponIds&&Object(s["B"])().then((function(t){var a=e.formValidate.couponIds.toString(),r=t,i={};for(var n in r)i[r[n].id]=r[n];var s,o=a.split(","),l=[],c=p(o);try{for(c.s();!(s=c.n()).done;){var u=s.value;i[u]&&l.push(i[u])}}catch(d){c.e(d)}finally{c.f()}e.$set(e.formValidate,"coupons",l)})),a.specType){if(l){this.formValidate.attr=a.attr.map((function(t){return{attrName:t.attrName,attrValue:t.attrValues.split(",")}})),this.ManyAttrValue=a.attrValue.map((function(t){return t.image=e.$selfUtil.setDomain(t.image),t.wearImage=e.$selfUtil.setDomain(t.wearImage),t.brokerage=t.brokerage||0,t.brokerageTwo=t.brokerageTwo||0,e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t,t}));var d=this.attrFormat(this.formValidate.attr);d.length!==this.ManyAttrValue.length?(this.$set(this,"showAll",!0),this.isAttr=!1):this.isAttr=!0;var m={};this.formValidate.attr.forEach((function(t,e){m[t.attrName]={title:t.attrName,type:"string"}})),this.formValidate.attrValue.forEach((function(t){for(var e in t.attrValue)t[e]=t.attrValue[e]})),this.manyTabTit=m,this.formThead=Object.assign({},this.formThead,m)}else if(this.formValidate.attr.length){this.oneFormBatch[0].image=this.$selfUtil.setDomain(a.image);for(var f=0;f<this.formValidate.attr.length;f++)this.formValidate.attr[f].attrValue=JSON.parse(this.formValidate.attr[f].attrValues)}this.productGetRule()}else this.OneAttrValue=a.attrValue},getInfo:function(){var t=this;this.fullscreenLoading=!0,Object(s["E"])(this.$route.params.id).then(function(){var e=f(d().mark((function e(a){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.getData(a),t.fullscreenLoading=!1;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.fullscreenLoading=!1}))},handleRemove:function(t){this.formValidate.sliderImages.splice(t,1)},modalPicTap:function(t,e,a){var r=this;this.isDisabled||this.$modalUpload((function(i){var n;if(i)switch(n="1"==t?i[0].sattDir:i,e){case"IMAGE":r.formValidate.image=n,r.OneAttrValue[0].image||(r.OneAttrValue[0].image=n);break;case"SLIDER_IMAGE":if(i.length+r.formValidate.sliderImages.length>10)return r.$message.warning("最多选择10张图片！");n.forEach((function(t){r.formValidate.sliderImages.push(t.sattDir)}));break;case"BATCH_IMAGE":r.oneFormBatch[0].image=n;break;case"SINGLE_IMAGE":r.OneAttrValue[0].image=n,r.formValidate.image||(r.formValidate.image=n);break;case"MULTI_IMAGE":r.ManyAttrValue[a].image=n;break;case"BATCH_WEAR":r.oneFormBatch[0].wearImage=n;break;case"SINGLE_WEAR":r.OneAttrValue[0].wearImage=n;break;case"MULTI_WEAR":r.ManyAttrValue[a].wearImage=n;break;default:break}}),t,"content")},handleSubmitUp:function(){this.currentTab--<0&&(this.currentTab=0)},handleSubmitNest:function(t){var e=this;this.$refs[t].validate((function(t){t?e.currentTab++>2&&(e.currentTab=0):e.formValidate.store_name&&e.formValidate.cate_id&&e.formValidate.keyword&&e.formValidate.unit_name&&e.formValidate.store_info&&e.formValidate.image&&e.formValidate.slider_image||e.$message.warning("请填写完整商品信息！")}))},handleSubmit:function(t){var e=this;if(this.formValidate.specType&&this.formValidate.attr.length<1)return this.$message.warning("请填写多规格属性！");this.$refs[t].validate((function(t){if(t){if(e.props2.emitPath){var a=new Set;e.formValidate.cateIds.forEach((function(t){t.forEach((function(t){a.add(t)}))})),e.formValidate.cateId=Array.from(a).join(",")}else e.formValidate.cateId=e.formValidate.cateIds.join(",");if(e.formValidate.keyword=e.labelarr.join(","),e.formValidate.sliderImage=JSON.stringify(e.formValidate.sliderImages),e.isShowGroup||(e.formValidate.guaranteeIds=e.formValidate.guaranteeIdsList.join(",")),e.formValidate.specType){e.formValidate.attrValue=e.ManyAttrValue,e.formValidate.attr=e.formValidate.attr.map((function(t){return{attrName:t.attrName,id:t.id,attrValue:t.attrValue,attrValues:t.attrValue.join(",")}}));for(var r=0;r<e.formValidate.attrValue.length;r++)e.$set(e.formValidate.attrValue[r],"id",0),e.$set(e.formValidate.attrValue[r],"productId",0),e.$set(e.formValidate.attrValue[r],"attrValue",JSON.stringify(e.formValidate.attrValue[r].attrValue)),delete e.formValidate.attrValue[r].value0}else e.formValidate.attr=[{attrName:"规格",attrValues:"默认",id:parseFloat(e.$route.params.id)>0?e.formValidate.attr[0].id:0}],e.OneAttrValue.forEach((function(t){e.$set(t,"attrValue",JSON.stringify({"规格":"默认"})),e.$set(t,"productId",0)})),e.formValidate.attrValue=e.OneAttrValue;e.loadingBtn=!0,parseFloat(e.$route.params.id)>0?Object(s["L"])(e.formValidate).then(function(){var t=f(d().mark((function t(a){return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$message.success("编辑成功"),setTimeout((function(){e.$router.push({path:"/product/list"})}),500),e.closeSelectedTag(),e.loadingBtn=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loadingBtn=!1})):Object(s["C"])(e.formValidate).then(function(){var t=f(d().mark((function t(a){return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$message.success("新增成功"),setTimeout((function(){e.$router.push({path:"/product/list"})}),500),e.closeSelectedTag(),e.loadingBtn=!1;case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.loadingBtn=!1}))}else e.formValidate.name&&e.formValidate.cateId&&e.formValidate.keyword&&e.formValidate.unitName&&e.formValidate.intro&&e.formValidate.image&&e.formValidate.sliderImages||e.$message.warning("请填写完整商品信息！")}))},isActive:function(t){return t.path===this.$route.path},closeSelectedTag:function(){var t=this;t.$store.dispatch("tagsView/delView",t.$route).then((function(e){var a=e.visitedViews;t.isActive(t.$route)&&t.toLastView(a,t.$route)}))},toLastView:function(t,e){var a=t.slice(-1)[0];a?this.$router.push(a.fullPath):"Dashboard"===e.name?this.$router.replace({path:"/redirect"+e.fullPath}):this.$router.push("/")},validate:function(t,e,a){!1===e&&this.$message.warning(a)},handleDragStart:function(t,e){this.isDisabled||(this.dragging=e)},handleDragEnd:function(t,e){this.isDisabled||(this.dragging=null)},handleDragOver:function(t){this.isDisabled||(t.dataTransfer.dropEffect="move")},handleDragEnter:function(t,e){if(!this.isDisabled){if(t.dataTransfer.effectAllowed="move",e===this.dragging)return;var a=h(this.formValidate.sliderImages),r=a.indexOf(this.dragging),i=a.indexOf(e);a.splice.apply(a,[i,0].concat(h(a.splice(r,1)))),this.formValidate.sliderImages=a}},getFileType:function(t){var e="",a="";try{var r=t.split(".");e=r[r.length-1]}catch(s){e=""}if(!e)return!1;e=e.toLocaleLowerCase();var i=["png","jpg","jpeg","bmp","gif"];if(a=i.find((function(t){return t===e})),a)return"image";var n=["mp4","m2v","mkv","rmvb","wmv","avi","flv","mov","m4v"];return a=n.find((function(t){return t===e})),a?"video":"other"}}},S=L,A=(a("5480"),a("2877")),O=Object(A["a"])(S,r,i,!1,null,"b7d51f3e",null);e["default"]=O.exports},7981:function(t,e,a){"use strict";a("f2b5")},8256:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tinymce-container editor-container",class:{fullscreen:t.fullscreen}},[a("textarea",{staticClass:"tinymce-textarea",attrs:{id:t.tinymceId}}),t._v(" "),a("div",{staticClass:"editor-custom-btn-container"},[a("editorImage",{staticClass:"editor-upload-btn",attrs:{color:"#1890ff"},on:{successCBK:t.imageSuccessCBK}})],1)])},i=[],n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"upload-container"},[a("el-button",{style:{background:t.color,borderColor:t.color},attrs:{icon:"el-icon-upload",size:"mini",type:"primary"},on:{click:function(e){return t.modalPicTap("2")}}},[t._v("\n    选择素材")])],1)},s=[],o={name:"EditorSlideUpload",props:{color:{type:String,default:"#1890ff"}},data:function(){return{dialogVisible:!1,listObj:{},fileList:[]}},methods:{modalPicTap:function(t){var e=this;this.$modalUpload((function(t){if(t){var a=[];if(t.length>10)return this.$message.warning("最多选择10张图片！");t.map((function(t){a.push(t.sattDir)})),e.$emit("successCBK",a)}}),t,"content")}}},l=o,c=(a("7981"),a("2877")),u=Object(c["a"])(l,n,s,!1,null,"7c5de02a",null),d=u.exports,m=["advlist anchor autolink autosave code codesample colorpicker colorpicker contextmenu directionality emoticons fullscreen hr image imagetools insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace spellchecker tabfocus table template textcolor textpattern visualblocks visualchars wordcount"],f=m,p=["searchreplace bold italic underline strikethrough alignleft aligncenter alignright outdent indent  blockquote undo redo removeformat subscript superscript code codesample fontsizeselect  fontselect","hr bullist numlist link image charmap preview anchor pagebreak insertdatetime media table emoticons forecolor backcolor fullscreen"],h=p,g={name:"Tinymce",components:{editorImage:d},props:{id:{type:String,default:function(){return"vue-tinymce-"+ +new Date+(1e3*Math.random()).toFixed(0)}},value:{type:String,default:""},toolbar:{type:Array,required:!1,default:function(){return[]}},menubar:{type:String,default:"file edit insert view format table"},height:{type:Number,required:!1,default:400}},data:function(){return{hasChange:!1,hasInit:!1,tinymceId:this.id,fullscreen:!1,languageTypeList:{en:"en",zh:"zh_CN"}}},computed:{language:function(){return this.languageTypeList["zh"]}},watch:{value:function(t){var e=this;!this.hasChange&&this.hasInit&&this.$nextTick((function(){return window.tinymce.get(e.tinymceId).setContent(t||"")}))},language:function(){var t=this;this.destroyTinymce(),this.$nextTick((function(){return t.initTinymce()}))}},mounted:function(){this.initTinymce()},activated:function(){this.initTinymce()},deactivated:function(){this.destroyTinymce()},destroyed:function(){this.destroyTinymce()},methods:{initTinymce:function(){var t=this,e=this;window.tinymce.init({language:this.language,selector:"#".concat(this.tinymceId),height:this.height,body_class:"panel-body ",object_resizing:!1,toolbar:this.toolbar.length>0?this.toolbar:h,menubar:this.menubar,plugins:f,end_container_on_empty_block:!0,powerpaste_word_import:"clean",code_dialog_height:450,code_dialog_width:1e3,advlist_bullet_styles:"square",advlist_number_styles:"default",imagetools_cors_hosts:["www.tinymce.com","codepen.io"],default_link_target:"_blank",link_title:!1,convert_urls:!1,nonbreaking_force_tab:!0,init_instance_callback:function(a){e.value&&a.setContent(e.value),e.hasInit=!0,a.on("NodeChange Change KeyUp SetContent",(function(){t.hasChange=!0,t.$emit("input",a.getContent())}))},setup:function(t){t.on("FullscreenStateChanged",(function(t){e.fullscreen=t.state}))}})},destroyTinymce:function(){var t=window.tinymce.get(this.tinymceId);this.fullscreen&&t.execCommand("mceFullScreen"),t&&t.destroy()},setContent:function(t){window.tinymce.get(this.tinymceId).setContent(t)},getContent:function(){window.tinymce.get(this.tinymceId).getContent()},imageSuccessCBK:function(t){var e=this,a=this;t.forEach((function(t){"video"==e.getFileType(t)?window.tinymce.get(a.tinymceId).insertContent('<video class="wscnph" src="'.concat(t,'" controls muted></video>')):window.tinymce.get(a.tinymceId).insertContent('<img class="wscnph" src="'.concat(t,'" />'))}))},getFileType:function(t){var e="",a="";try{var r=t.split(".");e=r[r.length-1]}catch(s){e=""}if(!e)return!1;e=e.toLocaleLowerCase();var i=["png","jpg","jpeg","bmp","gif"];if(a=i.find((function(t){return t===e})),a)return"image";var n=["mp4","m2v","mkv","rmvb","wmv","avi","flv","mov","m4v"];return a=n.find((function(t){return t===e})),a?"video":"other"}}},b=g,v=(a("9d5e"),Object(c["a"])(b,r,i,!1,null,"4926248e",null));e["a"]=v.exports},"9d5e":function(t,e,a){"use strict";a("ddf7")},ddf7:function(t,e,a){},f2b5:function(t,e,a){}}]);