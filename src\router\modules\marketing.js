// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout';

const marketingRouter = {
  path: '/marketing',
  component: Layout,
  redirect: '/marketing',
  name: 'Marketing',
  meta: {
    title: '营销',
    icon: 'clipboard',
  },
  children: [
    {
      path: 'discount',
      component: () => import('@/views/marketing/discount/index'),
      name: 'Discount',
      meta: { title: '折扣活动', icon: '' },
    },
    {
      path: 'discount/add',
      component: () => import('@/views/marketing/discount/add'),
      name: 'DiscountAdd',
      hidden: true,
      meta: {
        title: '新建折扣活动',
        icon: '',
        activeMenu: '/marketing/discount',
      },
    },
    {
      path: 'discount/detail/:id',
      component: () => import('@/views/marketing/discount/detail'),
      name: 'DiscountDetail',
      hidden: true,
      meta: {
        title: '活动详情',
        icon: '',
        activeMenu: '/marketing/discount',
      },
    },
    {
      path: 'discount/edit/:id',
      component: () => import('@/views/marketing/discount/edit'),
      name: 'DiscountEdit',
      hidden: true,
      meta: {
        title: '编辑折扣活动',
        icon: '',
        activeMenu: '/marketing/discount',
      },
    },
    {
      path: 'seckill',
      component: () => import('@/views/marketing/seckill/index'),
      name: 'Seckill',
      meta: { title: '秒杀活动', icon: '' },
    },
    {
      path: 'seckill/add',
      component: () => import('@/views/marketing/seckill/add'),
      name: 'SeckillAdd',
      hidden: true,
      meta: {
        title: '新建秒杀活动',
        icon: '',
        activeMenu: '/marketing/seckill',
      },
    },
    {
      path: 'seckill/detail/:id',
      component: () => import('@/views/marketing/seckill/detail'),
      name: 'SeckillDetail',
      hidden: true,
      meta: {
        title: '秒杀活动详情',
        icon: '',
        activeMenu: '/marketing/seckill',
      },
    },
    {
      path: 'seckill/edit/:id',
      component: () => import('@/views/marketing/seckill/edit'),
      name: 'SeckillEdit',
      hidden: true,
      meta: {
        title: '编辑秒杀活动',
        icon: '',
        activeMenu: '/marketing/seckill',
      },
    },
    {
      path: 'newuser',
      component: () => import('@/views/marketing/newuser/index'),
      name: 'Newuser',
      meta: { title: '新人活动', icon: '' },
    },
    {
      path: 'newuser/add',
      component: () => import('@/views/marketing/newuser/add'),
      name: 'NewuserAdd',
      hidden: true,
      meta: {
        title: '新建新人活动',
        icon: '',
        activeMenu: '/marketing/newuser',
      },
    },
    {
      path: 'newuser/detail/:id',
      component: () => import('@/views/marketing/newuser/detail'),
      name: 'NewuserDetail',
      hidden: true,
      meta: {
        title: '新人活动详情',
        icon: '',
        activeMenu: '/marketing/newuser',
      },
    },
    {
      path: 'newuser/edit/:id',
      component: () => import('@/views/marketing/newuser/edit'),
      name: 'NewuserEdit',
      hidden: true,
      meta: {
        title: '编辑新人活动',
        icon: '',
        activeMenu: '/marketing/newuser',
      },
    },
  ],
};

export default marketingRouter;
