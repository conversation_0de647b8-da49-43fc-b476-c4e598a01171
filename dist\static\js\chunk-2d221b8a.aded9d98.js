(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d221b8a"],{cc0d:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container mt-1"},[a("el-form",{attrs:{inline:"",size:"small"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:guarantee:group:add"],expression:"['merchant:product:guarantee:group:add']"}],attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handlerOpenEdit(0)}}},[e._v("添加保障服务")])],1)],1)]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData.data,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"组合名称"}}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.createTime))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:guarantee:group:edit"],expression:"['merchant:product:guarantee:group:edit']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return e.handlerOpenEdit(1,t.row)}}},[e._v("编辑")]),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:guarantee:group:edit"],expression:"['merchant:product:guarantee:group:edit']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return e.handlerOpenEdit(1,t.row,"info")}}},[e._v("详情")]),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:product:guarantee:group:delete"],expression:"['merchant:product:guarantee:group:delete']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return e.handlerOpenDel(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"提示",visible:e.dialogVisible,width:"1000px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"guaranteeForm",staticClass:"demo-ruleForm",attrs:{model:e.guaranteeForm,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"组合名称",prop:"name"}},[a("el-input",{attrs:{disabled:e.isDisabled},model:{value:e.guaranteeForm.name,callback:function(t){e.$set(e.guaranteeForm,"name",t)},expression:"guaranteeForm.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"服务列表",prop:"guaranteeList"}},[a("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{border:"",data:e.guaranteeListNew,"tooltip-effect":"dark"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",selectable:e.selectable,width:"55"}}),e._v(" "),a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"服务条款","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.name))]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.row.isShow,expression:"!scope.row.isShow"}],staticClass:"color-red"},[e._v("该数据已无效")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"content",label:"服务内容描述","min-width":"200"}})],1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:e.isDisabled},on:{click:function(t){return e.submitForm("guaranteeForm")}}},[e._v("确定")])],1)],1)],1)],1)},r=[],i=a("c4c8"),s=a("e350");function o(e){return d(e)||c(e)||u(e)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function c(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return m(e)}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}var h={data:function(){var e=this,t=function(t,a,n){0===e.guaranteeChanged.length?n(new Error("请至少选择一个服务选项")):n()};return{listLoading:!1,keyNum:0,dialogVisible:!1,guaranteeForm:{name:"",id:0,gids:""},guaranteeList:[],guaranteeListNew:[],tableData:{data:[]},rules:{name:[{required:!0,message:"请填写组合名称",trigger:"blur"}],guaranteeList:[{required:!0,validator:t,trigger:"change"}]},guaranteeChanged:[],isDisabled:!1}},mounted:function(){this.getList(),this.getProductGuarantee()},methods:{checkPermi:s["a"],selectable:function(e,t){return!this.isDisabled&&!!e.isShow},handleSelectionChange:function(e){this.guaranteeChanged=e},getProductGuarantee:function(){var e=this;i["H"]().then((function(t){e.guaranteeList=t}))},handleClose:function(){this.dialogVisible=!1,this.guaranteeForm={name:"",id:0,gids:""},this.guaranteeChanged=[],this.$refs.multipleTable.clearSelection(),this.$refs["guaranteeForm"].resetFields()},getList:function(){var e=this;this.listLoading=!0,i["q"]().then((function(t){e.tableData.data=t,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},handlerOpenEdit:function(e,t,a){var n=this,r=[];this.guaranteeListNew=o(this.guaranteeList),this.isDisabled=!!a,1===e?(this.guaranteeForm=Object.assign({},t),this.guaranteeListNew.map((function(e){t.guaranteeList.map((function(t){t.gid===e.id&&t.isShow&&r.push(e)}))})),r&&r.forEach((function(e){n.$nextTick((function(){n.$refs.multipleTable.toggleRowSelection(e,!0)}))}))):(r=this.guaranteeListNew.filter((function(e){return e.isShow})),this.guaranteeListNew=r),this.dialogVisible=!0},close:function(){this.dialogVisible=!1,this.guaranteeForm.name="",this.getList()},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;var a=[];t.guaranteeChanged.map((function(e){a.push(e.id)})),t.$delete(t.guaranteeForm,"guaranteeList"),t.$delete(t.guaranteeForm,"createTime"),t.guaranteeForm.gids=a.toString(),t.guaranteeForm.id?i["r"](t.guaranteeForm).then((function(e){t.$message.success("操作成功"),t.close()})).catch((function(){t.loading=!1})):i["o"](t.guaranteeForm).then((function(e){t.$message.success("操作成功"),t.close()})).catch((function(){t.loading=!1}))}))},handlerOpenDel:function(e){var t=this;this.$modalSure("删除当前保障服务吗").then((function(){i["p"](e.id).then((function(e){t.$message.success("删除成功"),t.getList()}))}))}}},g=h,f=a("2877"),p=Object(f["a"])(g,n,r,!1,null,null,null);t["default"]=p.exports}}]);