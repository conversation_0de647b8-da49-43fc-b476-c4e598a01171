<template>
  <div class="preview-area">
    <div class="phone-preview">
      <div class="phone-content">
        <!-- 广告模块 -->
        <div class="preview-module ad-module" v-if="adsImg || activeTab === 'ad'">
          <div class="ad-banner" v-if="adsImg" :style="{ height: (adsHeight || 592) / 4 + 'px' }">
            <img :src="adsImg" alt="广告图片" />
          </div>
          <div class="ad-placeholder" v-else :style="{ height: (adsHeight || 592) / 4 + 'px' }">
            <i class="el-icon-picture"></i>
            <span>广告位</span>
          </div>
        </div>

        <!-- 间距 -->
        <div
          class="spacing-divider"
          v-if="
            (adsImg || activeTab === 'ad') &&
            (functionIconRow !== 'none' ||
              activeTab === 'quickIcon' ||
              selectedMerchantList.length > 0 ||
              activeTab === 'merchantRecommend' ||
              selectedCouponList.length > 0 ||
              activeTab === 'coupon' ||
              selectedProductList.length > 0 ||
              activeTab === 'product')
          "
          :class="spacingColor"
          :style="{ height: spacingHeight + 'px' }"
        ></div>

        <div class="function-icons" v-if="functionIconRow !== 'none'">
          <div class="icons-grid" :class="`icons-${functionIconNum}`">
            <div
              v-for="(icon, index) in previewIcons"
              :key="icon.id || index"
              class="icon-item"
              :draggable="!icon.isEmpty"
              @click="handleIconClick(icon, index)"
              @dragstart="handleDragStart(icon, index, $event)"
              @dragover="handleDragOver(index, $event)"
              @drop="handleDrop(index, $event)"
              @dragenter="handleDragEnter(index, $event)"
              @dragleave="handleDragLeave(index, $event)"
              :class="{
                'drag-over': dragOverIndex === index,
                dragging: draggedIcon && draggedIcon.id === icon.id,
                'drop-target': isValidDropTarget(index),
                clickable: !icon.isEmpty || icon.isAddButton,
              }"
            >
              <!-- 删除按钮 -->
              <div
                v-if="!icon.isEmpty && !icon.isAddButton"
                class="delete-btn"
                @click.stop="handleDeleteIcon(icon, index)"
              >
                <i class="el-icon-close"></i>
              </div>

              <div class="icon-image">
                <!-- 加号图标 -->
                <i v-if="icon.isAddButton" class="el-icon-plus add-icon"></i>
                <!-- 普通图标 -->
                <img
                  v-else-if="icon.iconUrl && !icon.isEmpty"
                  :src="icon.iconUrl"
                  :alt="icon.name"
                  style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px"
                />
                <!-- 占位符 -->
                <div v-else-if="icon.isPlaceholder" class="placeholder-icon">
                  <i class="el-icon-picture"></i>
                </div>
                <!-- 空槽位 -->
                <i v-else class="el-icon-picture"></i>
              </div>
              <div class="icon-text">
                <div class="icon-title">{{ icon.isAddButton ? '添加图标' : icon.name || '图标' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 间距 -->
        <div
          class="spacing-divider"
          v-if="
            (functionIconRow !== 'none' || activeTab === 'quickIcon') &&
            (selectedMerchantList.length > 0 ||
              activeTab === 'merchantRecommend' ||
              selectedCouponList.length > 0 ||
              activeTab === 'coupon' ||
              selectedProductList.length > 0 ||
              activeTab === 'product')
          "
          :class="spacingColor"
          :style="{ height: spacingHeight + 'px' }"
        ></div>

        <!-- 商户推荐模块 -->
        <div
          class="preview-module merchant-module"
          v-if="selectedMerchantList.length > 0 || activeTab === 'merchantRecommend'"
        >
          <div style="display: flex; gap: 2px">
            <div class="module-title">|商户推荐</div>
            <div class="module-title2">好物任您选</div>
          </div>
          <div class="merchant-list">
            <div class="merchant-item" v-for="merchant in selectedMerchantList.slice(0, 3)" :key="merchant.id">
              <div class="merchant-avatar">
                <img v-if="merchant.avatar" :src="merchant.avatar" alt="商户头像" />
                <i v-else class="el-icon-user"></i>
              </div>
              <div class="merchant-info">
                <div class="merchant-name">{{ merchant.name || merchant.merchantName }}</div>
                <div class="merchant-desc">{{ merchant.addressDetail }}</div>
              </div>
            </div>
            <div class="merchant-item placeholder" v-if="selectedMerchantList.length === 0">
              <div class="merchant-avatar">
                <i class="el-icon-user"></i>
              </div>
              <div class="merchant-info">
                <div class="merchant-name">示例商户</div>
                <div class="merchant-desc">优质商户</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 间距 -->
        <div
          class="spacing-divider"
          v-if="
            (selectedMerchantList.length > 0 || activeTab === 'merchantRecommend') &&
            (selectedCouponList.length > 0 ||
              activeTab === 'coupon' ||
              selectedProductList.length > 0 ||
              activeTab === 'product')
          "
          :class="spacingColor"
          :style="{ height: spacingHeight + 'px' }"
        ></div>

        <!-- 优惠券模块 -->
        <div class="preview-module coupon-module" v-if="selectedCouponList.length > 0 || activeTab === 'coupon'">
          <div class="coupon-list">
            <div class="coupon-item-new" v-for="coupon in selectedCouponList" :key="coupon.id">
              <div class="coupon-bg">
                <div class="coupon-content">
                  <div class="coupon-block coupon-block-amount">
                    <template v-if="coupon.moneyStr">
                      <template v-if="!coupon.moneyStr.endsWith('折')">￥</template>{{ coupon.moneyStr }}
                    </template>
                    <template v-else>￥50</template>
                  </div>
                  <div class="coupon-block coupon-block-tag1">{{ coupon.couponTypeName || '通用券' }}</div>
                  <div class="coupon-block coupon-block-tag2">满{{ coupon.minPrice }}元可用</div>
                </div>
              </div>
            </div>
            <div class="coupon-item-new placeholder" v-if="selectedCouponList.length === 0">
              <div class="coupon-bg">
                <div class="coupon-content">
                  <div class="coupon-amount">¥50</div>
                  <div class="coupon-category">新人专享券</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 间距 -->
        <div
          class="spacing-divider"
          v-if="
            (selectedCouponList.length > 0 || activeTab === 'coupon') &&
            (selectedProductList.length > 0 || activeTab === 'product')
          "
          :class="spacingColor"
          :style="{ height: spacingHeight + 'px' }"
        ></div>

        <!-- 商品模块 -->
        <div class="preview-module product-module" v-if="selectedProductList.length > 0 || activeTab === 'product'">
          <div style="display: flex; gap: 2px">
            <div class="module-title">|热门推荐</div>
            <div class="module-title2">精选好物任您选</div>
          </div>

          <div class="product-grid">
            <div class="product-item" v-for="product in selectedProductList" :key="product.id">
              <div class="product-image">
                <img v-if="product.image" :src="product.image" alt="商品图片" />
                <i v-else class="el-icon-goods"></i>
              </div>
              <div class="product-info">
                <div class="product-name">{{ product.name || '商品名称' }}</div>
                <div style="display: flex; gap: 2px">
                  <div class="product-price">¥{{ product.price || '99.00' }}</div>
                  <div class="product-price2">¥{{ product.price || '199.00' }}</div>
                </div>
              </div>
            </div>
            <div class="product-item placeholder" v-if="selectedProductList.length === 0">
              <div class="product-image">
                <i class="el-icon-goods"></i>
              </div>
              <div class="product-info">
                <div class="product-name">示例商品</div>
                <div class="product-price">¥99.00</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DesignPreview',
  props: {
    // 当前激活的标签
    activeTab: {
      type: String,
      default: 'ad',
    },
    // 广告相关
    adsImg: {
      type: String,
      default: '',
    },
    adsHeight: {
      type: [String, Number],
      default: 592,
    },
    // 间距相关
    spacingColor: {
      type: String,
      default: 'white',
    },
    spacingHeight: {
      type: [String, Number],
      default: 20,
    },
    // 快捷图标相关
    functionIconNum: {
      type: Number,
      default: 4,
    },
    functionIconRow: {
      type: [String, Number],
      default: 'none',
    },
    iconTitle: {
      type: String,
      default: '',
    },
    iconSubtitle: {
      type: String,
      default: '',
    },
    // 商户推荐
    selectedMerchantList: {
      type: Array,
      default: () => [],
    },
    // 优惠券
    selectedCouponList: {
      type: Array,
      default: () => [],
    },
    // 商品
    selectedProductList: {
      type: Array,
      default: () => [],
    },
    functionIcons: {
      type: Array,
      default: () => [],
    },
    isLocalDebug: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    /**
     * 计算预览图标数量
     * @returns {Array} 图标数组
     */
    previewIcons() {
      if (this.functionIconRow === 'none') {
        console.log('functionIconRow为none，返回空数组');
        return [];
      }

      // 按position分组图标
      const iconsByPosition = {};
      this.functionIcons.forEach((icon) => {
        const pos = icon.position || 1;
        if (!iconsByPosition[pos]) {
          iconsByPosition[pos] = [];
        }
        iconsByPosition[pos].push(icon);
      });

      // 对每个position内的图标按sort排序
      Object.keys(iconsByPosition).forEach((pos) => {
        iconsByPosition[pos].sort((a, b) => (a.sort || 0) - (b.sort || 0));
      });

      const finalIcons = [];
      const maxPosition = Math.max(...Object.keys(iconsByPosition).map(Number), 1);

      // 为每个position生成图标和占位符
      for (let row = 1; row <= Math.max(maxPosition, 2); row++) {
        const rowIcons = iconsByPosition[row] || [];

        // 添加该行的真实图标
        rowIcons.forEach((icon) => {
          finalIcons.push(icon);
        });

        // 添加占位符到functionIconNum
        const currentRowIconCount = rowIcons.length;
        for (let i = currentRowIconCount; i < this.functionIconNum; i++) {
          finalIcons.push({
            id: `placeholder-${row}-${i}`,
            name: '',
            iconUrl: '',
            isEmpty: true,
            isPlaceholder: true,
            position: row,
          });
        }
      }

      // 添加加号按钮逻辑（如果需要）
      const realIconsCount = this.functionIcons.filter((icon) => !icon.isEmpty).length;
      if (realIconsCount < this.functionIconNum * (Number(this.functionIconRow) || 1)) {
        const firstEmptyIndex = finalIcons.findIndex((icon) => icon.isEmpty);
        if (firstEmptyIndex !== -1) {
          const emptyIcon = finalIcons[firstEmptyIndex];
          finalIcons[firstEmptyIndex] = {
            id: 'add-button',
            name: '添加图标',
            iconUrl: '',
            isEmpty: false,
            isAddButton: true,
            position: emptyIcon.position,
          };
        }
      }

      // 限制总数不超过 functionIconNum * 行数
      const maxCount = this.functionIconNum * (Number(this.functionIconRow) || 1);
      return finalIcons.slice(0, maxCount);
    },
  },
  data() {
    return {
      draggedIcon: null,
      draggedIndex: null,
      dragOverIndex: null,
      dropTargetIndex: null,
    };
  },
  methods: {
    editIcon(icon) {
      if (!icon.isEmpty) {
        this.$emit('edit-icon', icon);
      }
    },

    deleteIcon(icon) {
      this.$emit('delete-icon', icon);
    },

    /**
     * 开始拖拽
     * @param {Object} icon - 被拖拽的图标
     * @param {Number} index - 图标索引
     * @param {Event} event - 拖拽事件
     */
    handleDragStart(icon, index, event) {
      if (!icon.isEmpty) {
        this.draggedIcon = icon;
        this.draggedIndex = index;
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/html', event.target);
      }
    },

    /**
     * 处理图标拖拽释放
     * @param {Number} targetIndex - 目标索引
     * @param {Event} event - 拖拽事件
     */
    handleDrop(targetIndex, event) {
      event.preventDefault();
      if (this.draggedIcon && this.draggedIndex !== null && this.draggedIndex !== targetIndex) {
        const realIcons = [...this.functionIcons];
        const draggedRealIndex = realIcons.findIndex((icon) => icon.id === this.draggedIcon.id);
        if (draggedRealIndex === -1) return;
        const targetIcon = this.previewIcons[targetIndex];
        const targetPosition = targetIcon ? targetIcon.position : 1;
        // 删除原位置
        const [draggedItem] = realIcons.splice(draggedRealIndex, 1);
        // 赋值新 position
        draggedItem.position = targetPosition;
        // 找到目标行的所有图标
        let iconsInTargetRow = realIcons.filter((icon) => icon.position === targetPosition);
        // 计算插入到目标行的第几个
        let insertIndex = 0;
        for (let i = 0; i < targetIndex; i++) {
          if (
            this.previewIcons[i].position === targetPosition &&
            !this.previewIcons[i].isEmpty &&
            !this.previewIcons[i].isAddButton
          ) {
            insertIndex++;
          }
        }
        // 插入到目标行指定位置
        iconsInTargetRow.splice(insertIndex, 0, draggedItem);
        // realIcons 移除目标行的所有图标
        const restIcons = realIcons.filter((icon) => icon.position !== targetPosition);
        // 合并所有行
        const newIcons = [];
        // 先插入目标行（已排序）
        iconsInTargetRow.forEach((icon, idx) => {
          icon.sort = idx + 1;
          newIcons.push(icon);
        });
        // 再插入其他行（按 position 升序、每行内部按 sort）
        const positions = [...new Set(restIcons.map((icon) => icon.position))].sort((a, b) => a - b);
        positions.forEach((pos) => {
          const rowIcons = restIcons.filter((icon) => icon.position === pos).sort((a, b) => a.sort - b.sort);
          rowIcons.forEach((icon, idx) => {
            icon.sort = idx + 1;
            newIcons.push(icon);
          });
        });
        this.$emit('icon-drag-sort', newIcons);
      }
      this.clearDragState();
    },

    /**
     * 处理删除图标
     * @param {Object} icon - 图标对象
     * @param {Number} index - 图标索引
     */
    handleDeleteIcon(icon, index) {
      if (!icon.isEmpty && !icon.isAddButton) {
        this.$emit('delete-icon', icon, index);
      }
    },

    /**
     * 清理拖拽状态
     */
    clearDragState() {
      this.draggedIcon = null;
      this.draggedIndex = null;
      this.dragOverIndex = null;
    },

    /**
     * 重新计算图标位置和排序
     * @param {Array} icons - 图标数组
     * @returns {Array} 更新后的图标数组
     */
    calculateIconPositions(icons) {
      return icons.map((icon, index) => {
        return {
          ...icon,
          sort: index + 1, // 只使用全局连续的sort值
        };
      });
    },
    /**
     * 处理拖拽悬停
     * @param {Number} index - 目标索引
     * @param {Event} event - 拖拽事件
     */
    handleDragOver(index, event) {
      event.preventDefault();
      event.dataTransfer.dropEffect = 'move';

      if (this.draggedIndex !== null && this.draggedIndex !== index) {
        this.dragOverIndex = index;
      }
    },

    /**
     * 处理拖拽进入
     * @param {Number} index - 目标索引
     * @param {Event} event - 拖拽事件
     */
    handleDragEnter(index, event) {
      event.preventDefault();
      if (this.draggedIndex !== null && this.draggedIndex !== index) {
        this.dragOverIndex = index;
      }
    },

    /**
     * 处理拖拽离开
     * @param {Number} index - 目标索引
     * @param {Event} event - 拖拽事件
     */
    handleDragLeave(index, event) {
      event.preventDefault();
      // 只有当真正离开元素时才清除dragOverIndex
      if (!event.currentTarget.contains(event.relatedTarget)) {
        this.dragOverIndex = null;
      }
    },

    /**
     * 判断是否为有效的放置目标
     * @param {Number} index - 目标索引
     * @returns {Boolean} 是否为有效目标
     */
    isValidDropTarget(index) {
      return this.draggedIndex !== null && this.draggedIndex !== index;
    },

    /**
     * 处理图标点击事件
     * @param {Object} icon - 图标对象
     * @param {Number} index - 图标索引
     */
    handleIconClick(icon, index) {
      if (icon.isAddButton) {
        // 点击加号图标，跳转到添加界面
        this.$emit('add-icon');
      } else if (!icon.isEmpty) {
        // 点击普通图标，跳转到编辑界面
        this.$emit('edit-icon', icon);
      }
    },

    /**
     * 处理删除图标
     * @param {Object} icon - 图标对象
     * @param {Number} index - 图标索引
     */
    handleDeleteIcon(icon, index) {
      if (!icon.isEmpty && !icon.isAddButton) {
        this.$emit('delete-icon', icon, index);
      }
    },
  },
};
</script>

<style scoped>
/* 预览区域样式 */
.preview-area {
  width: 300px;
  height: 540px;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 10px;
}

.phone-preview {
  width: 280px;
  height: 520px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.phone-content {
  background: #fff;
  height: 100%;
  overflow-y: auto;
}

/* 间距分隔器样式 */
.spacing-divider.white {
  background: #fff;
}

.spacing-divider.gray {
  background: #f5f5f5;
}

/* 广告模块 */
.ad-module .ad-banner {
  width: 100%;
  overflow: hidden;
}

.ad-module .ad-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ad-module .ad-placeholder {
  background: #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}

.ad-module .ad-placeholder i {
  font-size: 32px;
  margin-bottom: 8px;
}

/* 间距模块 */
.spacing-module.white {
  background: #fff;
}

.spacing-module.gray {
  background: #f5f5f5;
}

/* 快捷图标模块 */
.quick-icon-module {
  padding: 15px;
  background: #fff;
}

.icons-grid {
  display: grid;
  gap: 15px;
}

.icons-grid.icons-3 {
  grid-template-columns: repeat(3, 1fr);
}

.icons-grid.icons-4 {
  grid-template-columns: repeat(4, 1fr);
}

.icons-grid.icons-5 {
  grid-template-columns: repeat(5, 1fr);
}

/* 拖拽相关样式 */
.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.icon-item[draggable='true'] {
  cursor: move;
}

.icon-item.dragging {
  opacity: 0.5;
  transform: scale(0.95);
}

.icon-item.drag-over {
  background-color: #f0f9ff;
  border: 2px dashed #409eff;
  border-radius: 8px;
}

.icon-item:hover {
  background-color: #f5f7fa;
  border-radius: 8px;
}

.icon-image {
  width: 40px;
  height: 40px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.icon-image i {
  font-size: 20px;
  color: #409eff;
}

.icon-text {
  font-size: 11px;
}

.icon-title {
  color: #333;
  margin-bottom: 2px;
}

/* 商户推荐模块 */
.merchant-module {
  padding: 15px;
  background: #fff;
}

.module-title {
  font-weight: bold;
  font-size: 12px;
  font-weight: 1500;
  color: #333;
  margin-bottom: 12px;
}

.module-title2 {
  font-size: 9px;
  margin-top: 3px;
  color: #9f9f9f;
}

.merchant-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.merchant-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 6px;
}

.merchant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  overflow: hidden;
}

.merchant-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.merchant-avatar i {
  font-size: 20px;
  color: #999;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.merchant-desc {
  font-size: 10px;
  color: #999;
}
/* 优惠券模块 - 新样式 */
.coupon-module {
  padding: 15px;
  background: #fff;
}

.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.coupon-item-new {
  width: 100%;
  height: 60px;
  position: relative;
}

.coupon-bg {
  width: 100%;
  height: 70px;
  background-image: url('/preview-coupon.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}
.coupon-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.coupon-block {
  position: absolute;
  white-space: nowrap;
}
.coupon-block-amount {
  left: 15px; /* 可调 */
  top: 33px; /* 可调 */
  font-size: 16px;
  font-weight: bold;
  color: #ff2525;
}
.coupon-block-tag1 {
  left: 58px; /* 可调 */
  top: 25px; /* 可调 */
  font-size: 12px;
  color: #ff2525;
  border-radius: 4px;
  padding: 2px 8px;
}
.coupon-block-tag2 {
  left: 57px; /* 可调 */
  top: 43px; /* 可调 */
  font-size: 9px;
  color: #ff2525;
  border-radius: 4px;
  padding: 2px 8px;
}

.coupon-amount {
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.coupon-category {
  font-size: 12px;
  opacity: 0.9;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.coupon-item-new.placeholder {
  opacity: 0.6;
}

/* 商品模块 */
.product-module {
  padding: 15px;
  background: #fff;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.product-item {
  background: #f9f9f9;
  border-radius: 6px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 80px;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-image i {
  font-size: 24px;
  color: #999;
}

.product-info {
  padding: 8px;
}

.product-name {
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 10px;
  font-weight: fold;
}

.product-price2 {
  font-size: 10px;
  color: #9f9f9f;
  text-decoration: line-through;
}

.placeholder {
  opacity: 0.6;
}

.icon-item {
  position: relative;
  cursor: default;
}

.icon-item.clickable {
  cursor: pointer;
}

.icon-item:hover .delete-btn {
  opacity: 1;
}

.delete-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 10;
}

.delete-btn:hover {
  background: #ff3838;
}

.add-icon {
  font-size: 24px;
  color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.icon-item.clickable:hover {
  transform: scale(1.05);
  transition: transform 0.2s;
}

/* 占位符样式 */
.placeholder-icon {
  width: 100%;
  height: 100%;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.placeholder-icon:hover {
  border-color: #c0c4cc;
  background-color: #f5f7fa;
}

.placeholder-icon i {
  font-size: 24px;
  color: #c0c4cc;
}

.icon-item.clickable:hover .placeholder-icon {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.icon-item.clickable:hover .placeholder-icon i {
  color: #409eff;
}
</style>
