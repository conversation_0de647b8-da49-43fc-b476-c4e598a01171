<template>
  <div class="products-content">
    <el-table
      v-loading="loading"
      :data="productList"
      size="mini"
      class="table"
      highlight-current-row
      :header-cell-style="{ fontWeight: 'bold' }"
    >
      <el-table-column prop="id" label="商品ID" min-width="80" />
      <el-table-column label="商品图片" min-width="80" align="center">
        <template slot-scope="scope">
          <div class="product-image-cell">
            <img :src="scope.row.image" alt="商品图片" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="商品名称" min-width="200" show-overflow-tooltip />
      <el-table-column label="采购价(元)" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.cost }}
        </template>
      </el-table-column>
      <el-table-column label="商品原价(元)" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.otPrice }}
        </template>
      </el-table-column>
      <el-table-column label="折扣" min-width="80">
        <template slot-scope="scope">
          {{ scope.row.discount }}
        </template>
      </el-table-column>
      <el-table-column label="活动售价(元)" min-width="120">
        <template slot-scope="scope">
          {{ calculateActivityPrice(scope.row.otPrice, scope.row.discount) }}
        </template>
      </el-table-column>
      <el-table-column label="库存" min-width="80">
        <template slot-scope="scope">
          {{ scope.row.stock }}
        </template>
      </el-table-column>
      <el-table-column label="商品状态" min-width="100">
        <template slot-scope="scope">
          <span :class="getStatusClass(scope.row.status)">{{ getStatusText(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="每人限购数量" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.astrictNumber }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="block">
      <el-pagination
        :page-sizes="[20, 40, 60, 80]"
        :page-size="pagination.limit"
        :current-page="pagination.page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
import { productQueryListApi } from '@/api/product';
import { getActivityInfoApi } from '@/api/discount';

export default {
  name: 'ProductList',
  props: {
    activityId: {
      type: [Number, String],
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      productList: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
      },
      internalLoading: false,
    };
  },
  computed: {
    /**
     * 获取加载状态
     */
    isLoading() {
      return this.loading || this.internalLoading;
    },
  },
  mounted() {
    this.fetchProductList();
  },
  watch: {
    activityId: {
      handler() {
        this.pagination.page = 1;
        this.fetchProductList();
      },
      immediate: false,
    },
  },
  methods: {
    /**
     * 获取商品列表
     */
    async fetchProductList() {
      if (!this.activityId) {
        this.productList = [];
        this.pagination.total = 0;
        return;
      }

      this.internalLoading = true;
      try {
        const params = {
          // source: 1, // 这里是查找参与活动的平台/商户产品
          activityId: this.activityId,
          page: this.pagination.page,
          limit: this.pagination.limit,
        };
        // 并发请求活动详情和商品列表
        const [activityInfo, response] = await Promise.all([
          getActivityInfoApi(this.activityId),
          productQueryListApi(params),
        ]);
        const discount = activityInfo.discount;
        // 将 discount 填入每个商品
        this.productList = (response.list || []).map((item) => ({
          ...item,
          discount,
        }));
        this.pagination.total = response.total || 0;
      } catch (error) {
        console.error('获取折扣活动商品列表失败:', error);
        this.$message.error('获取商品列表失败');
        this.productList = [];
        this.pagination.total = 0;
      } finally {
        this.internalLoading = false;
      }
    },

    /**
     * 计算活动售价
     * @param {String|Number} otPrice - 商品原价
     * @param {String|Number} discount - 折扣
     * @returns {String} 活动售价
     */
    calculateActivityPrice(otPrice, discount) {
      const price = parseFloat(otPrice) || 0;
      const discountRate = parseFloat(discount) || 0;
      const activityPrice = price * discountRate;
      return activityPrice.toFixed(2);
    },

    /**
     * 获取商品状态文本
     * @param {Number} status - 状态值
     * @returns {String} 状态文本
     */
    getStatusText(status) {
      const statusMap = {
        0: '下架',
        1: '上架',
        2: '待审核',
      };
      return statusMap[status] || '未知';
    },

    /**
     * 获取状态样式类名
     * @param {Number} status - 状态值
     * @returns {String} 样式类名
     */
    getStatusClass(status) {
      const classMap = {
        0: 'status-offline',
        1: 'status-online',
        2: 'status-pending',
      };
      return classMap[status] || '';
    },

    /**
     * 处理每页显示数量变化
     * @param {Number} val - 新的每页显示数量
     */
    handleSizeChange(val) {
      this.pagination.limit = val;
      this.pagination.page = 1;
      this.fetchProductList();
    },

    /**
     * 处理页码变化
     * @param {Number} val - 新的页码
     */
    handlePageChange(val) {
      this.pagination.page = val;
      this.fetchProductList();
    },
  },
};
</script>

<style scoped>
.products-content {
  .product-image-cell {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    border-radius: 4px;
    margin: 0 auto;

    img {
      width: 32px;
      height: 32px;
      object-fit: cover;
      border-radius: 4px;
    }
  }

  .block {
    margin-top: 20px;
    text-align: right;
  }

  .status-online {
    color: #67c23a;
  }

  .status-offline {
    color: #f56c6c;
  }

  .status-pending {
    color: #e6a23c;
  }
}
</style>
