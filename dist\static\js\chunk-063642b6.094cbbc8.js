(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-063642b6"],{4843:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container mt-1"},[a("el-form",{attrs:{inline:"",size:"small"}},[a("el-form-item",{attrs:{label:"优惠券状态："}},[a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:e.seachList},model:{value:e.tableFrom.status,callback:function(t){e.$set(e.tableFrom,"status",t)},expression:"tableFrom.status"}},[a("el-option",{attrs:{label:"未开启",value:0}}),e._v(" "),a("el-option",{attrs:{label:"开启",value:1}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"领取方式："}},[a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:e.seachList},model:{value:e.tableFrom.receiveType,callback:function(t){e.$set(e.tableFrom,"receiveType",t)},expression:"tableFrom.receiveType"}},[a("el-option",{attrs:{label:"手动领取",value:1}}),e._v(" "),a("el-option",{attrs:{label:"赠送券",value:2}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"优惠券类别："}},[a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:e.seachList},model:{value:e.tableFrom.category,callback:function(t){e.$set(e.tableFrom,"category",t)},expression:"tableFrom.category"}},[a("el-option",{attrs:{label:"商家券",value:1}}),e._v(" "),a("el-option",{attrs:{label:"商品券",value:2}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"优惠券名称："}},[a("el-input",{staticStyle:{width:"350px"},attrs:{placeholder:"请输入优惠券名称",clearable:""},model:{value:e.tableFrom.name,callback:function(t){e.$set(e.tableFrom,"name",t)},expression:"tableFrom.name"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:coupon:page:list"],expression:"['merchant:coupon:page:list']"}],attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:e.seachList},slot:"append"})],1)],1)],1)],1),e._v(" "),a("router-link",{attrs:{to:{path:"/coupon/list/save"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:coupon:save"],expression:"['merchant:coupon:save']"}],attrs:{size:"small",type:"primary"}},[e._v("添加优惠劵")])],1)],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"类别","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("span",[e._v(e._s(e._f("couponCategoryFilter")(s.category)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"money",label:"面值","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"领取方式","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("span",[e._v(e._s(e._f("couponUserTypeFilter")(s.receiveType)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{"min-width":"260",label:"领取日期"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[s.receiveEndTime?a("div",[e._v(e._s(s.receiveStartTime)+" - "+e._s(s.receiveEndTime))]):a("span",[e._v("不限时")])]}}])}),e._v(" "),a("el-table-column",{attrs:{"min-width":"260",label:"使用时间"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[s.day?a("div",[e._v(e._s(s.day)+"天")]):a("span",[e._v(" "+e._s(s.useStartTime)+" - "+e._s(s.useEndTime)+" ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{"min-width":"100",label:"发布数量"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[s.isLimited?a("div",[a("span",{staticClass:"fa"},[e._v("发布："+e._s(s.total))]),e._v(" "),a("span",{staticClass:"sheng"},[e._v("剩余："+e._s(s.lastTotal))])]):a("span",[e._v("不限量")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"是否开启","min-width":"100",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["merchant:coupon:update:status"])?[a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":"开启","inactive-text":"关闭"},nativeOn:{click:function(a){return e.onchangeIsShow(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"100",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("router-link",{attrs:{to:{path:"/coupon/list/save/"+t.row.id}}},[t.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:coupon:info"],expression:"['merchant:coupon:info']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[e._v("复制")]):e._e()],1),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:coupon:delete"],expression:"['merchant:coupon:delete']"}],staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleDelMenu(t.row)}}},[e._v("删除")]),e._v(" "),a("router-link",{directives:[{name:"show",rawName:"v-show",value:2===t.row.category,expression:"scope.row.category === 2"}],attrs:{to:{path:"/coupon/list/save/"+t.row.id+"/edit"}}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:coupon:product:join:edit"],expression:"['merchant:coupon:product:join:edit']"}],staticClass:"mr10",attrs:{type:"text",size:"small"}},[e._v("编辑")])],1)]}}])})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"领取记录",visible:e.dialogVisible,width:"500px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.Loading,expression:"Loading"}],staticStyle:{width:"100%"},attrs:{data:e.issueData.data}},[a("el-table-column",{attrs:{prop:"nickname",label:"用户名","min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{label:"用户头像","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"领取时间","min-width":"180"}})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":e.tableFromIssue.limit,"current-page":e.tableFromIssue.page,layout:"total, sizes, prev, pager, next, jumper",total:e.issueData.total},on:{"size-change":e.handleSizeChangeIssue,"current-change":e.pageChangeIssue}})],1)],1)],1)},i=[],l=a("c4c8"),n=a("83d6"),o=a("e350"),r=a("2f62");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,s)}return a}function m(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return t=d(t),t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e){var t=h(e,"string");return"symbol"===c(t)?t:String(t)}function h(e,t){if("object"!==c(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!==c(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var b={name:"CouponList",data:function(){return{Loading:!1,dialogVisible:!1,roterPre:n["roterPre"],listLoading:!0,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,status:"",name:"",type:"",useType:""},tableFromIssue:{page:1,limit:10,couponId:""},issueData:{data:[],total:0}}},mounted:function(){this.merProductClassify.length||this.$store.dispatch("product/getMerProductClassify"),this.getList()},computed:m({},Object(r["b"])(["merProductClassify"])),methods:{checkPermi:o["a"],seachList:function(){this.tableFrom.page=1,this.getList()},handleClose:function(){this.dialogVisible=!1},receive:function(e){this.dialogVisible=!0,this.tableFromIssue.couponId=e.id,this.getIssueList()},getIssueList:function(){var e=this;this.Loading=!0,Object(l["couponUserListApi"])(this.tableFromIssue).then((function(t){e.issueData.data=t.list,e.issueData.total=t.total,e.Loading=!1})).catch((function(t){e.Loading=!1,e.$message.error(t.message)}))},pageChangeIssue:function(e){this.tableFromIssue.page=e,this.getIssueList()},handleSizeChangeIssue:function(e){this.tableFromIssue.limit=e,this.getIssueList()},getList:function(){var e=this;this.listLoading=!0,Object(l["s"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()},onchangeIsShow:function(e){var t=this;Object(l["k"])(e.id).then((function(){t.$message.success("修改成功"),t.getList()})).catch((function(){e.status=!e.status}))},handleDelMenu:function(e){var t=this;this.$modalSure("删除当前数据?").then((function(){Object(l["i"])(e.id).then((function(e){t.$message.success("删除成功"),t.getList()}))}))}}},v=b,f=(a("9ea5"),a("2877")),g=Object(f["a"])(v,s,i,!1,null,"337108e9",null);t["default"]=g.exports},"9ea5":function(e,t,a){"use strict";a("b4c0")},b4c0:function(e,t,a){}}]);