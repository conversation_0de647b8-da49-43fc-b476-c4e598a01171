<template>
  <div class="app-container">
    <div class="content-wrapper">
      <el-form ref="editForm" :model="formData" :rules="formRules" label-width="120px" size="small">
        <!-- 基础信息 -->
        <div class="section-header">
          <span>基础信息</span>
        </div>
        <div class="form-section">
          <el-form-item label="活动名称：" prop="activityName" required>
            <el-input v-model="formData.activityName" placeholder="请输入活动名称" style="width: 400px" />
          </el-form-item>

          <el-form-item label="后台活动名称：" prop="backendName" required>
            <el-input v-model="formData.backendName" placeholder="请输入后台活动名称" style="width: 400px" />
          </el-form-item>

          <el-form-item label="活动描述：" prop="description" required>
            <el-input v-model="formData.description" placeholder="请输入活动描述" style="width: 400px" />
          </el-form-item>

          <el-form-item label="活动标签：" prop="activityType" required>
            <el-radio-group v-model="formData.activityType">
              <el-radio :label="3">新人价</el-radio>
            </el-radio-group>
            <el-input
              v-model="formData.newPriceValue"
              placeholder="取值范围0-1，精确到两位小数"
              style="width: 200px; margin-left: 10px"
              @input="handlenewPriceValueChange"
            />
          </el-form-item>

          <el-form-item label="活动时间：" prop="activityTime" required>
            <el-date-picker
              v-model="formData.activityTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 400px"
            />
          </el-form-item>
        </div>

        <!-- 限制条件 -->
        <div class="section-header">
          <span>限制条件</span>
        </div>
        <div class="form-section">
          <el-form-item label="活动对象：" prop="activityTarget" required>
            <el-radio-group v-model="formData.activityTarget">
              <el-radio :label="1">首次下单</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="是否可与其他优惠券叠加使用：" prop="ifCoupon" required>
            <el-radio-group v-model="formData.ifCoupon">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="限购数量：" prop="limitType" required>
            <el-radio-group v-model="formData.limitType" @change="handleLimitTypeChange">
              <el-radio :label="false">不限</el-radio>
              <el-radio :label="true">限</el-radio>
            </el-radio-group>
            <el-input
              v-model="formData.limitQuantity"
              placeholder="1-999"
              style="width: 150px; margin-left: 10px"
              @focus="handleLimitQuantityFocus"
              @input="handleLimitQuantityChange"
            />
            <span style="margin-left: 5px">次</span>
          </el-form-item>

          <el-form-item label="购买限制：" prop="buyLimit" required>
            <span style="margin-right: 10px">每单购买的商品需要满</span>
            <el-input v-model="formData.buyLimit" placeholder="精确到两位小数" style="width: 100px" />
            <span style="margin-left: 5px">元</span>
          </el-form-item>

          <el-form-item label="活动商品：" prop="productType" required>
            <el-radio-group v-model="formData.productType">
              <el-radio :label="2">商户商品</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-button class="add-product-btn" size="small" @click="addProduct">添加商品</el-button>
            <span style="margin-left: 10px; color: #999">已选{{ selectedProductCount }}件</span>
          </el-form-item>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-wrapper">
          <el-button type="primary" size="medium" @click="submitForm" :loading="submitLoading">更新</el-button>
        </div>
      </el-form>
    </div>

    <!-- 添加商品弹窗 -->
    <el-dialog title="添加商品" :visible.sync="addProductDialogVisible" width="80%" :close-on-click-modal="false">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 已选商品选项卡 -->
        <el-tab-pane label="已选商品" name="selected">
          <!-- 已选商品搜索区域 -->
          <div class="search-form">
            <el-form :inline="true" size="mini">
              <el-form-item label="商品名称">
                <el-input
                  v-model="selectedProductSearch.name"
                  placeholder="请输入商品名称"
                  style="width: 200px"
                  @keyup.enter="searchSelectedProducts"
                />
              </el-form-item>
              <el-form-item label="商品状态">
                <el-select
                  v-model="selectedProductSearch.status"
                  placeholder="请选择状态"
                  style="width: 120px"
                  @change="searchSelectedProducts"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option label="上架" value="1"></el-option>
                  <el-option label="下架" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchSelectedProducts">搜索</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 商品表格 -->
          <el-table
            v-loading="selectedProductLoading"
            :data="selectedProductList"
            size="mini"
            class="table"
            highlight-current-row
            :header-cell-style="{ fontWeight: 'bold' }"
            @selection-change="handleSelectedProductSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="商品ID" min-width="80"></el-table-column>
            <el-table-column label="商品图片" min-width="100">
              <template slot-scope="scope">
                <img :src="scope.row.image" style="width: 60px; height: 60px; object-fit: cover" />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" min-width="150"></el-table-column>
            <el-table-column label="采购价(元)" min-width="120">
              <template slot-scope="scope">
                {{ scope.row.cost }}
              </template>
            </el-table-column>
            <el-table-column label="商品原价(元)" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.originalPrice"
                  placeholder="商品原价"
                  size="mini"
                  style="width: 100px"
                  disabled
                />
              </template>
            </el-table-column>
            <el-table-column label="活动价(元)" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.activityPrice"
                  placeholder="请输入活动价"
                  size="mini"
                  style="width: 100px"
                  @input="handleActivityPriceChange(scope.row, $event)"
                />
              </template>
            </el-table-column>
            <el-table-column label="奖励(%)" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.award"
                  placeholder="请输入奖励百分比"
                  size="mini"
                  style="width: 100px"
                  @input="handleAwardChange(scope.row, $event)"
                />
              </template>
            </el-table-column>
            <el-table-column label="库存" min-width="100">
              <template slot-scope="scope">
                {{ scope.row.stock }}
              </template>
            </el-table-column>
            <el-table-column label="商品状态" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.status === 1 ? '上架' : '下架' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="每人限购数量" min-width="120">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.limitQuantity"
                  placeholder="限购数量"
                  size="mini"
                  style="width: 100px"
                  :value="formData.limitType ? formData.limitQuantity || '0' : '0'"
                  @input="scope.row.limitQuantity = $event"
                  disabled
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="80" fixed="right">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  style="color: #f56c6c"
                  @click="deleteProduct(scope.row, scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 底部操作区域 -->
          <div class="table-footer">
            <!-- 左下角删除按钮 -->
            <div class="footer-left">
              <el-button
                size="small"
                class="delete-selected-btn"
                :disabled="selectedProductIds.length === 0"
                @click="batchDeleteProducts"
              >
                删除选中商品
              </el-button>
            </div>

            <!-- 右侧分页 -->
            <div class="footer-right">
              <!-- <el-pagination
                @size-change="handleSelectedProductSizeChange"
                @current-change="handleSelectedProductPageChange"
                :current-page="selectedProductPagination.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="selectedProductPagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="selectedProductPagination.total"
              >
              </el-pagination> -->
            </div>
          </div>
        </el-tab-pane>

        <!-- 未选商品选项卡 -->
        <el-tab-pane label="未选商品" name="unselected">
          <!-- 搜索区域 -->
          <div class="search-form">
            <el-form :inline="true" size="mini">
              <el-form-item label="商品名称">
                <el-input
                  v-model="unselectedProductSearch.name"
                  placeholder="请输入商品名称"
                  style="width: 200px"
                  @keyup.enter="searchUnselectedProducts"
                />
              </el-form-item>
              <el-form-item label="商品状态">
                <el-select
                  v-model="unselectedProductSearch.status"
                  placeholder="请选择状态"
                  style="width: 120px"
                  @change="searchUnselectedProducts"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option label="上架" value="1"></el-option>
                  <el-option label="下架" value="0"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchUnselectedProducts">搜索</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 商品表格 -->
          <el-table
            v-loading="unselectedProductLoading"
            :data="filteredUnselectedProductList"
            size="mini"
            class="table"
            highlight-current-row
            :header-cell-style="{ fontWeight: 'bold' }"
            @selection-change="handleUnselectedProductSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="商品ID" min-width="80"></el-table-column>
            <el-table-column label="商品图片" min-width="100">
              <template slot-scope="scope">
                <img :src="scope.row.image" style="width: 60px; height: 60px; object-fit: cover" />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" min-width="150"></el-table-column>
            <el-table-column label="库存" min-width="100">
              <template slot-scope="scope">
                {{ scope.row.stock }}
              </template>
            </el-table-column>
            <el-table-column label="商品状态" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.status === 1 ? '上架' : '下架' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="80" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color: #409eff" @click="addToSelected(scope.row)">
                  添加
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 底部操作区域 -->
          <div class="table-footer">
            <!-- 左下角批量添加按钮 -->
            <div class="footer-left">
              <el-button
                size="small"
                class="add-selected-btn"
                :disabled="unselectedProductIds.length === 0"
                @click="batchAddToSelected"
              >
                批量添加选中商品
              </el-button>
            </div>
            <!-- 右侧分页 -->
            <div class="footer-right">
              <!-- <el-pagination
                @size-change="handleUnselectedProductSizeChange"
                @current-change="handleUnselectedProductPageChange"
                :current-page="unselectedProductPagination.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="unselectedProductPagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="filteredUnselectedProductTotal"
              >
              </el-pagination> -->
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmAddProducts">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { productQueryListApi } from '@/api/product';
import { getActivityInfoApi, discountUpdateApi, activityProductBatchAddApi } from '@/api/discount';

export default {
  name: 'DiscountEdit',
  data() {
    return {
      activityId: null, // 活动ID
      submitLoading: false,
      formData: {
        activityName: '',
        backendName: '',
        description: '',
        activityType: 3,
        newPriceValue: '1.00',
        activityTime: [],
        activityTarget: 1,
        ifCoupon: true,
        limitType: false,
        limitQuantity: '',
        buyLimit: '0',
        productType: 2,
      },
      formRules: {
        activityName: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        backendName: [{ required: true, message: '请输入后台活动名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入活动描述', trigger: 'blur' }],
        activityTime: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        activityTarget: [{ required: true, message: '请选择活动对象', trigger: 'change' }],
        ifCoupon: [{ required: true, message: '请选择是否可叠加使用', trigger: 'change' }],
        limitType: [{ required: true, message: '请选择限购类型', trigger: 'change' }],
        buyLimit: [
          { required: true, message: '请输入购买限制', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const numValue = parseFloat(value);
              if (isNaN(numValue) || numValue < 0 || numValue > 999999.99) {
                callback(new Error('购买限制金额必须在0.00-999999.99元之间'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        limitQuantity: [
          {
            validator: (rule, value, callback) => {
              // 如果启用了限购，则验证数量
              if (this.formData.limitType) {
                const numValue = parseInt(value);
                if (isNaN(numValue) || numValue < 1 || numValue > 999) {
                  callback(new Error('限购数量必须在1-999之间'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        productType: [{ required: true, message: '请选择活动商品类型', trigger: 'change' }],
      },

      // 添加商品弹窗相关
      addProductDialogVisible: false,
      activeTab: 'selected',
      selectedProductIds: [],
      selectedProductLoading: false,

      // 已选商品搜索
      selectedProductSearch: {
        name: '',
        status: '',
      },

      // 已选商品分页
      selectedProductPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 未选商品相关数据
      unselectedProductIds: [],
      unselectedProductLoading: false,
      unselectedProductSearch: {
        name: '',
        status: '',
      },
      unselectedProductList: [],
      unselectedProductPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 商品暂存数据
      tempSelectedProducts: [], // 暂存的已选商品列表
      originalUnselectedProductList: [], // 存储所有商品的原始数据
      hasLoadedProducts: false, // 标记是否已加载过商品数据
    };
  },
  computed: {
    /**
     * 已选商品列表 - 支持搜索过滤
     */
    selectedProductList() {
      let list = this.tempSelectedProducts;

      // 按商品名称搜索
      if (this.selectedProductSearch.name) {
        list = list.filter((item) => item.name.toLowerCase().includes(this.selectedProductSearch.name.toLowerCase()));
      }

      // 按商品状态搜索
      if (this.selectedProductSearch.status !== '') {
        list = list.filter((item) => item.status.toString() === this.selectedProductSearch.status);
      }

      return list;
    },

    /**
     * 已选商品总数
     */
    selectedProductCount() {
      return this.tempSelectedProducts.length;
    },

    /**
     * 过滤后的未选商品列表 - 排除已添加的商品并支持搜索过滤
     */
    filteredUnselectedProductList() {
      let list = this.unselectedProductList;

      // 按商品名称搜索
      if (this.unselectedProductSearch.name) {
        list = list.filter((item) => item.name.toLowerCase().includes(this.unselectedProductSearch.name.toLowerCase()));
      }

      // 按商品状态搜索
      if (this.unselectedProductSearch.status !== '') {
        list = list.filter((item) => item.status.toString() === this.unselectedProductSearch.status);
      }

      return list;
    },
    filteredUnselectedProductTotal() {
      return this.filteredUnselectedProductList.length;
    },
  },
  methods: {
    /**
     * 获取活动详情信息
     */
    async getActivityInfo() {
      try {
        const response = await getActivityInfoApi(this.activityId);
        const activityInfo = response;

        // 填充表单数据
        this.formData = {
          activityName: activityInfo.name || '',
          backendName: activityInfo.backName || '',
          description: activityInfo.instruction || '',
          activityType: parseInt(activityInfo.type) || 1,
          newPriceValue: activityInfo.newPrice ? activityInfo.newPrice.toString() : '1.00',
          activityTime: [activityInfo.beginTime, activityInfo.overTime],
          activityTarget: parseInt(activityInfo.target) || 1,
          ifCoupon: activityInfo.ifCoupon === 1 || activityInfo.ifCoupon === true,
          limitType: activityInfo.astrictNumber > 0,
          limitQuantity: activityInfo.astrictNumber > 0 ? activityInfo.astrictNumber.toString() : '',
          buyLimit: activityInfo.astrictAmount ? activityInfo.astrictAmount.toString() : '0',
          productType: 2,
        };

        // 获取已选商品列表
        await this.getSelectedProducts();
      } catch (error) {
        console.error('获取活动详情失败:', error);
        this.$message.error('获取活动详情失败');
      }
    },

    /**
     * 获取已选商品列表
     */
    async getSelectedProducts() {
      this.selectedProductLoading = true;
      try {
        const params = {
          // page: this.selectedProductPagination.currentPage,
          // limit: this.selectedProductPagination.pageSize,
          limit: -1,
          activityId: this.activityId,
          status: this.selectedProductSearch.status || '',
          name: this.selectedProductSearch.name || '',
        };

        const response = await productQueryListApi(params);
        const productList = response.list || [];

        // 处理商品数据，添加编辑页面需要的字段
        this.tempSelectedProducts = productList.map((product) => ({
          ...product,
          originalPrice: product.otPrice || '',
          discount: this.formData.newPriceValue,
          activityPrice: product.activityPrice || parseFloat(this.formData.newPriceValue).toFixed(2),
          limitQuantity: this.formData.limitType ? this.formData.limitQuantity || '0' : '0',
          award: product.award || '',
        }));

        this.selectedProductPagination.total = response.count || 0;
      } catch (error) {
        console.error('获取已选商品列表失败:', error);
        this.$message.error('获取已选商品列表失败');
      } finally {
        this.selectedProductLoading = false;
      }
    },

    /**
     * 限购类型变化处理
     * @param {String} val - 限购类型值
     */
    handleLimitTypeChange(val) {
      if (!val) {
        this.formData.limitQuantity = '';
      }

      // 同步更新已选商品的限购数量
      this.tempSelectedProducts.forEach((product) => {
        product.limitQuantity = val ? this.formData.limitQuantity || '0' : '0';
      });
    },

    /**
     * 限购数量输入框获得焦点时处理
     */
    handleLimitQuantityFocus() {
      if (!this.formData.limitType) {
        this.formData.limitType = true;
      }
    },

    /**
     * 限购数量变化处理
     */
    handleLimitQuantityChange() {
      // 同步更新已选商品的限购数量
      this.tempSelectedProducts.forEach((product) => {
        product.limitQuantity = this.formData.limitType ? this.formData.limitQuantity || '0' : '0';
      });
    },

    /**
     * 折扣值变化处理
     */
    handlenewPriceValueChange() {
      // 更新已选商品的折扣值和活动价格
      this.tempSelectedProducts.forEach((product) => {
        const originalPrice = parseFloat(product.originalPrice) || 0;
        const newPrice = parseFloat(this.formData.newPriceValue) || 0;
        product.activityPrice = newPrice.toFixed(2);
      });
    },

    // 添加商品
    addProduct() {
      this.addProductDialogVisible = true;
      this.activeTab = 'selected';
      // 只在第一次打开时获取商品数据
      if (!this.hasLoadedProducts) {
        this.getAllProductList();
      } else {
        // 非首次打开，只更新过滤列表
        this.updateFilteredProductLists();
      }
    },

    /**
     * 搜索已选商品
     */
    searchSelectedProducts() {
      this.selectedProductPagination.currentPage = 1;
      // 不再调用 getSelectedProducts，实现本地过滤
    },

    // 已选商品表格选择变化
    handleSelectedProductSelectionChange(selection) {
      this.selectedProductIds = selection.map((item) => item.id);
    },

    // 删除单个商品
    deleteProduct(row, index) {
      this.$confirm('确认删除该商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const deletedProduct = this.tempSelectedProducts[index];

          // 从已选商品中移除
          this.tempSelectedProducts.splice(index, 1);

          // 将删除的商品重新添加到原始未选商品列表
          this.originalUnselectedProductList.push(deletedProduct);

          // 更新过滤后的商品列表
          this.updateFilteredProductLists();

          this.updateSelectedProductCount();
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    /**
     * 活动价变化处理
     * @param {Object} product - 商品对象
     * @param {String} value - 输入的活动价
     */
    handleActivityPriceChange(product, value) {
      // 验证输入的价格格式
      const price = parseFloat(value);
      if (isNaN(price) || price < 0) {
        this.$message.warning('请输入有效的价格');
        return;
      }

      // 检查活动价不能高于原价
      const originalPrice = parseFloat(product.originalPrice || product.otPrice || 0);
      if (price > originalPrice) {
        this.$message.warning('活动价不能高于商品原价');
        return;
      }

      // 更新商品的活动价格
      product.activityPrice = value;
    },

    /**
     * 奖励百分比变化处理
     * @param {Object} product - 商品对象
     * @param {String} value - 输入的奖励百分比
     */
    handleAwardChange(product, value) {
      // 验证输入的百分比格式
      const award = parseFloat(value);
      if (isNaN(award) || award < 0 || award > 100) {
        this.$message.warning('请输入0-100之间的有效百分比');
        return;
      }

      // 更新商品的奖励百分比
      product.award = value;
    },

    // 已选商品分页大小变化
    handleSelectedProductSizeChange(val) {
      this.selectedProductPagination.pageSize = val;
      this.selectedProductPagination.currentPage = 1;
      this.getSelectedProducts();
    },

    // 已选商品页码变化
    handleSelectedProductPageChange(val) {
      this.selectedProductPagination.currentPage = val;
      this.getSelectedProducts();
    },

    // 搜索未选商品
    searchUnselectedProducts() {
      this.unselectedProductPagination.currentPage = 1;
      this.updateFilteredProductLists();
    },

    // 未选商品表格选择变化
    handleUnselectedProductSelectionChange(selection) {
      this.unselectedProductIds = selection.map((item) => item.id);
    },

    /**
     * 添加商品到已选列表
     * @param {Object} product - 商品对象
     */
    addToSelected(product) {
      // 检查是否已存在
      const existingIndex = this.tempSelectedProducts.findIndex((item) => item.id === product.id);
      if (existingIndex !== -1) {
        this.$message.warning('该商品已添加');
        return;
      }

      // 从原始未选商品列表中移除
      const originalIndex = this.originalUnselectedProductList.findIndex((item) => item.id === product.id);
      if (originalIndex !== -1) {
        this.originalUnselectedProductList.splice(originalIndex, 1);
      }

      // 计算活动价格
      const originalPrice = parseFloat(product.otPrice) || 0;
      const newPrice = parseFloat(this.formData.newPriceValue) || 0;
      const activityPrice = newPrice.toFixed(2);

      // 添加商品并设置默认值
      const newProduct = {
        ...product,
        originalPrice: product.otPrice || '',
        discount: this.formData.newPriceValue,
        activityPrice: '',
        limitQuantity: this.formData.limitType ? this.formData.limitQuantity || '0' : '0',
        singleLimitQuantity: '',
        award: '',
      };

      this.tempSelectedProducts.push(newProduct);

      // 更新过滤后的商品列表
      this.updateFilteredProductLists();

      this.updateSelectedProductCount();
      this.$message.success('添加成功');
    },

    /**
     * 批量添加选中商品到已选列表
     */
    batchAddToSelected() {
      if (this.unselectedProductIds.length === 0) {
        this.$message.warning('请先选择要添加的商品');
        return;
      }

      let addedCount = 0;

      this.unselectedProductIds.forEach((productId) => {
        const product = this.unselectedProductList.find((item) => item.id === productId);
        if (product) {
          // 检查是否已存在
          const existingIndex = this.tempSelectedProducts.findIndex((item) => item.id === product.id);
          if (existingIndex === -1) {
            // 从原始未选商品列表中移除
            const originalIndex = this.originalUnselectedProductList.findIndex((item) => item.id === product.id);
            if (originalIndex !== -1) {
              this.originalUnselectedProductList.splice(originalIndex, 1);
            }

            // 计算活动价格
            const originalPrice = parseFloat(product.otPrice) || 0;
            const newPrice = parseFloat(this.formData.newPriceValue) || 0;
            const activityPrice = newPrice.toFixed(2);

            // 添加商品并设置默认值
            const newProduct = {
              ...product,
              originalPrice: product.otPrice || '',
              discount: this.formData.newPriceValue,
              activityPrice: '',
              limitQuantity: this.formData.limitType ? this.formData.limitQuantity || '0' : '0',
              singleLimitQuantity: '',
              award: '',
            };

            this.tempSelectedProducts.push(newProduct);
            addedCount++;
          }
        }
      });

      // 更新过滤后的商品列表
      this.updateFilteredProductLists();

      this.updateSelectedProductCount();
      this.unselectedProductIds = [];
      this.$message.success(`成功添加${addedCount}件商品`);
    },

    /**
     * 确认添加商品（关闭对话框）
     */
    confirmAddProducts() {
      this.updateSelectedProductCount();
      this.addProductDialogVisible = false;
      this.$message.success('商品设置成功');
    },

    /**
     * 更新已选商品数量
     */
    updateSelectedProductCount() {
      this.selectedProductPagination.total = this.tempSelectedProducts.length;
    },

    /**
     * 提交表单
     */
    async submitForm() {
      // 校验是否至少选择了一件商品
      if (this.tempSelectedProducts.length === 0) {
        this.$message.error('请至少选择一件商品');
        return;
      }

      // 校验所有选中商品是否都设置了活动价和奖励
      for (const product of this.tempSelectedProducts) {
        if (!product.activityPrice || product.activityPrice === '' || parseFloat(product.activityPrice) <= 0) {
          this.$message.error(`商品「${product.name}」未设置活动价或活动价无效`);
          return;
        }

        // 检查活动价不能高于原价
        const originalPrice = parseFloat(product.originalPrice || product.otPrice || 0);
        const activityPrice = parseFloat(product.activityPrice);
        if (activityPrice > originalPrice) {
          this.$message.error(`商品「${product.name}」的活动价不能高于原价`);
          return;
        }

        // 校验奖励字段
        if (!product.award || product.award === '' || parseFloat(product.award) < 0) {
          this.$message.error(`商品「${product.name}」未设置奖励或奖励无效`);
          return;
        }

        // 检查奖励百分比范围（假设0-100%）
        const award = parseFloat(product.award);
        if (award > 100) {
          this.$message.error(`商品「${product.name}」的奖励百分比不能超过100%`);
          return;
        }
      }

      // 自定义校验：购买限制金额和限购数量
      const buyLimit = parseFloat(this.formData.buyLimit) || 0;
      if (buyLimit < 0 || buyLimit > 999999.99) {
        this.$message.error('购买限制金额必须在0.00-999999.99元之间');
        return;
      }

      // 如果启用了限购，校验限购数量
      if (this.formData.limitType) {
        const limitQuantity = parseInt(this.formData.limitQuantity) || 0;
        if (limitQuantity < 1 || limitQuantity > 999) {
          this.$message.error('限购数量必须在1-999之间');
          return;
        }
      }

      // 表单验证
      const valid = await this.$refs.editForm.validate().catch(() => false);
      if (!valid) {
        return;
      }

      // 构造提交数据
      const submitData = {
        id: this.activityId,
        name: this.formData.activityName,
        backName: this.formData.backendName,
        instruction: this.formData.description,
        type: this.formData.activityType.toString(),
        newPrice: parseFloat(this.formData.newPriceValue),
        beginTime: this.formData.activityTime[0],
        overTime: this.formData.activityTime[1],
        target: this.formData.activityTarget.toString(),
        createBy: this.$store.getters.name,
      };

      try {
        this.submitLoading = true;
        await discountUpdateApi(submitData);
        // 无论商品数量是否为0，都调用批量添加接口
        const productData = {
          productIds: this.tempSelectedProducts.map((product) => product.id),
          activityId: this.activityId,
          merchantId: 1,
          ifCoupon: this.formData.ifCoupon,
          astrictNumber: this.formData.limitType ? parseInt(this.formData.limitQuantity) || 0 : 0,
          astrictAmount: parseFloat(this.formData.buyLimit) || 0,
          productType: '2', // 商户商品
          remark: '',
          productPrices: this.tempSelectedProducts.map((product) => ({
            productId: product.id,
            activityPrice: parseFloat(product.activityPrice),
            award: parseFloat(product.award) || 0,
          })),
        };
        await activityProductBatchAddApi(productData);
        this.$message.success('更新新人活动成功');
        this.$router.push('/marketing/newuser');
      } catch (error) {
        console.error('更新失败:', error);
        this.$message.error('更新失败，请重试');
      } finally {
        this.submitLoading = false;
      }
    },

    /**
     * 获取所有商品列表（一次性获取）
     */
    async getAllProductList() {
      this.unselectedProductLoading = true;
      try {
        const params = {
          limit: -1,
          activityId: -1, // -1表示没有关联活动的商品
        };

        const response = await productQueryListApi(params);
        this.originalUnselectedProductList = response.list || [];
        this.hasLoadedProducts = true; // 标记已加载

        // 初始化过滤后的商品列表
        this.updateFilteredProductLists();
      } catch (error) {
        console.error('获取商品列表失败:', error);
        this.$message.error('获取商品列表失败');
      } finally {
        this.unselectedProductLoading = false;
      }
    },

    // 批量删除商品
    batchDeleteProducts() {
      this.$confirm('确认删除选中的商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 获取要删除的商品
          const productsToDelete = this.tempSelectedProducts.filter((item) =>
            this.selectedProductIds.includes(item.id),
          );

          // 从已选商品中移除
          this.tempSelectedProducts = this.tempSelectedProducts.filter(
            (item) => !this.selectedProductIds.includes(item.id),
          );

          // 将删除的商品重新添加到原始未选商品列表
          productsToDelete.forEach((product) => {
            this.originalUnselectedProductList.push(product);
          });

          // 更新过滤后的商品列表
          this.updateFilteredProductLists();

          this.selectedProductIds = [];
          this.updateSelectedProductCount();
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    /**
     * 更新过滤后的商品列表（本地过滤）
     */
    updateFilteredProductLists() {
      // 获取已选商品的ID列表
      const selectedIds = this.tempSelectedProducts.map((item) => String(item.id));

      // 过滤未选商品列表（基于原始数据，已选商品已经从原始数据中移除）
      let filteredList = [...this.originalUnselectedProductList];

      // 按搜索条件进一步过滤
      if (this.unselectedProductSearch.name) {
        filteredList = filteredList.filter((item) =>
          item.name.toLowerCase().includes(this.unselectedProductSearch.name.toLowerCase()),
        );
      }

      if (this.unselectedProductSearch.status !== '') {
        filteredList = filteredList.filter((item) => item.status.toString() === this.unselectedProductSearch.status);
      }

      this.unselectedProductList = filteredList;
      this.unselectedProductPagination.total = filteredList.length;
    },
  },

  mounted() {
    // 从路由参数获取活动ID
    this.activityId = this.$route.params.id || this.$route.query.id;
    if (!this.activityId) {
      this.$message.error('缺少活动ID参数');
      this.$router.push('/marketing/newuser');
      return;
    }

    // 获取活动详情
    this.getActivityInfo();
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.app-breadcrumb {
  margin-bottom: 20px;
  font-size: 14px;
}

.content-wrapper {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
}

.section-header {
  background-color: #f5f7fa;
  padding: 12px 16px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #333;
}

.form-section {
  padding: 0 16px 20px 16px;
  margin-bottom: 20px;
}

.submit-wrapper {
  text-align: right;
  padding-top: 20px;
}

.add-product-btn {
  background-color: white;
  border: 1px solid #409eff;
  color: #409eff;
}

.add-product-btn:hover {
  background-color: white;
  border-color: #66b1ff;
  color: #66b1ff;
}

.add-product-btn:focus {
  background-color: white;
  border-color: #409eff;
  color: #409eff;
}

.search-section {
  padding: 15px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.footer-left {
  flex: 1;
}

.footer-right {
  flex: 1;
  text-align: right;
}

.delete-selected-btn,
.add-selected-btn {
  background-color: white;
  border: 1px solid #409eff;
  color: #409eff;
}

.delete-selected-btn:hover,
.add-selected-btn:hover {
  background-color: white;
  border-color: #66b1ff;
  color: #66b1ff;
}

.delete-selected-btn:focus,
.add-selected-btn:focus {
  background-color: white;
  border-color: #409eff;
  color: #409eff;
}

.delete-selected-btn:disabled,
.add-selected-btn:disabled {
  background-color: white;
  border-color: #dcdfe6;
  color: #c0c4cc;
}

/* 搜索表单样式 */
.search-form {
  padding: 0 0 8px 0;
  margin-bottom: 8px;
}

/* 调整标签页内容间距 */
::v-deep .el-tabs__content {
  padding: 8px 0;
}
</style>
