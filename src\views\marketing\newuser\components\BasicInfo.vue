<template>
  <div class="basic-content">
    <div class="info-row" v-for="(item, key) in displayItems" :key="key">
      <span class="label">{{ item.label }}：</span>
      <span class="value">{{ item.value }}</span>
    </div>
  </div>
</template>

<script>
import { getActivityInfoApi } from '@/api/discount';

export default {
  name: 'BasicInfo',
  props: {
    activityId: {
      type: [String, Number],
      required: true,
    },
    activityInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      detailData: {},
    };
  },
  computed: {
    displayItems() {
      const data = Object.keys(this.activityInfo).length > 0 ? this.activityInfo : this.detailData;
      return [
        { label: '活动编码', value: data.activityNo },
        { label: '活动名称', value: data.name },
        { label: '后台活动名称', value: data.backName },
        { label: '活动标签', value: data.activityLabel },
        { label: '活动描述', value: data.instruction },
        { label: '活动时间', value: `${data.beginTime} 至 ${data.overTime}` },
        { label: '活动对象', value: this.getTargetText(data.target) },
        { label: '新人价', value: data.newPrice ? `${data.newPrice}元` : '-' },
        { label: '是否可与优惠券叠加', value: data.ifCoupon ? '是' : '否' },
        { label: '购买限制', value: `限购数量：${data.astrictNumber}，每单购买的商品需要满：${data.astrictAmount} 元` },
      ];
    },
  },
  mounted() {
    if (Object.keys(this.activityInfo).length === 0) {
      this.getActivityInfo();
    }
  },
  watch: {
    activityId: {
      handler() {
        if (Object.keys(this.activityInfo).length === 0) {
          this.getActivityInfo();
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * 获取活动详情
     */
    async getActivityInfo() {
      if (!this.activityId) return;
      try {
        this.detailData = await getActivityInfoApi(this.activityId);
        this.$emit('activity-loaded', this.detailData);
      } catch (error) {
        console.error('获取活动详情失败:', error);
      }
    },

    /**
     * 获取活动对象文本
     * @param {String} target - 活动对象类型
     */
    getTargetText(target) {
      const targetMap = {
        0: '不限',
        1: '新人首次',
      };
      return targetMap[target] || '未知';
    },
  },
};
</script>

<style scoped>
.basic-content {
  .info-row {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;

    .label {
      width: 150px;
      color: #606266;
      font-size: 14px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      flex: 1;
    }
  }
}
</style>
