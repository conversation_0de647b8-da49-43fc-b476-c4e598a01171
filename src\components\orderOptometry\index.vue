<template>
  <div>
    <el-dialog title="验光单信息" :visible.sync="dialogVisible" :center="true" width="700px">
      <div class="rows">
        <el-row type="flex">
          <el-col :span="5">
            <div></div>
          </el-col>
          <el-col :span="4" class="title">
            <div>球镜片(S)</div>
          </el-col>
          <el-col :span="4" class="title">
            <div>柱镜片(C)</div>
          </el-col>
          <el-col :span="3" class="title">
            <div>轴位(A)</div>
          </el-col>
          <el-col :span="4" class="title">
            <div>瞳距(PD)</div>
          </el-col>
          <el-col :span="4" class="title">
            <div>瞳高(PH)</div>
          </el-col>
        </el-row>

        <el-row type="flex">
          <el-col :span="5" class="title">
            <div>右眼(R)</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.RS }}</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.RC }}</div>
          </el-col>
          <el-col :span="3">
            <div>{{ optometry.data.RA }}</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.RPD }}</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.RPH }}</div>
          </el-col>
        </el-row>

        <el-row type="flex">
          <el-col :span="5" class="title">
            <div>左眼(L)</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.LS }}</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.LC }}</div>
          </el-col>
          <el-col :span="3">
            <div>{{ optometry.data.LA }}</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.LPD }}</div>
          </el-col>
          <el-col :span="4">
            <div>{{ optometry.data.LPH }}</div>
          </el-col>
        </el-row>

        <el-row type="flex">
          <el-col :span="5" class="title">
            <div>下加光(ADD)</div>
          </el-col>
          <el-col :span="19">
            <div>{{ optometry.data.ADD }}</div>
          </el-col>
        </el-row>
        <el-row type="flex" v-if="optometry.url">
          <el-col :span="5" class="title">
            <div>验光单图片</div>
          </el-col>
          <el-col :span="19">
            <div class="demo-image__preview">
              <el-image style="width: 36px; height: 36px" :src="optometry.url" :preview-src-list="[optometry.url]" />
            </div>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="5" class="title">
            <div>验光人</div>
          </el-col>
          <el-col :span="19">
            <div>{{ optometry.name }}</div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'OrderOptometry',
  props: {
    optometry: {
      type: Object,
      default: () => {
        return {
          data: {
            RS: 0,
            RC: 0,
            RA: 0,
            LS: 0,
            LC: 0,
            LA: 0,
            RPD: 0,
            LPD: 0,
            RPH: 0,
            LPH: 0,
            ADD: 0,
          },
          url: '',
          name: '',
        };
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
};
</script>

<style lang="scss" scoped>
.rows {
  border: 1px solid #ebeef5;
  color: #606266;

  .el-row {
    &:first-child {
      .el-col {
        > div {
          &.el-row {
            &:first-child {
              font-size: 16px;
              font-weight: bold;
            }
          }
        }
      }
    }

    .el-col {
      border-bottom: 1px solid #ebeef5;
      border-left: 1px solid #ebeef5;
      min-height: 24px;
      line-height: 24px;
      text-align: center;

      > div {
        padding: 8px 10px;

        &.el-row {
          padding: 0;
        }
      }

      &:first-child {
        border-left: none;
      }

      &.title {
        font-weight: bold;
        font-size: 16px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &:last-child {
      .el-col {
        border-bottom: none;
      }
    }
  }
}
</style>
