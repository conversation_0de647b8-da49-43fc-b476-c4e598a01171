(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1ebfda05"],{"0f53":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入角色昵称",clearable:""},model:{value:e.listPram.roleName,callback:function(t){e.$set(e.listPram,"roleName",t)},expression:"listPram.roleName"}})],1),e._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:admin:role:list"],expression:"['merchant:admin:role:list']"}],attrs:{size:"mini",type:"primary"},nativeOn:{click:function(t){return e.handleGetRoleList(t)}}},[e._v("查询")])],1)],1),e._v(" "),a("el-form",{attrs:{inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:admin:role:save"],expression:"['merchant:admin:role:save']"}],attrs:{size:"small",type:"primary"},on:{click:function(t){return e.handlerOpenEdit(0)}}},[e._v("添加角色")])],1)],1),e._v(" "),a("el-table",{attrs:{data:e.listData.list,size:"mini","header-cell-style":{fontWeight:"bold",background:"#f8f8f9",color:"#515a6e",height:"40px"}}},[a("el-table-column",{attrs:{label:"角色编号",prop:"id",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{label:"角色昵称",prop:"roleName","min-width":"130"}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["merchant:admin:role:update:status"])?[a("el-switch",{staticStyle:{width:"40px"},attrs:{"active-value":!0,"inactive-value":!1},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",prop:"updateTime","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:admin:role:update","merchant:admin:role:info"],expression:"['merchant:admin:role:update', 'merchant:admin:role:info']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return e.handlerOpenEdit(1,t.row)}}},[e._v("编辑")]),e._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:admin:role:delete"],expression:"['merchant:admin:role:delete']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return e.handlerOpenDel(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),a("el-pagination",{attrs:{"current-page":e.listPram.page,"page-sizes":e.constants.page.limit,layout:e.constants.page.layout,total:e.listData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{visible:e.editDialogConfig.visible,title:0===e.editDialogConfig.isCreate?"创建身份":"编辑身份","destroy-on-close":"","close-on-click-modal":!1,width:"500px"},on:{"update:visible":function(t){return e.$set(e.editDialogConfig,"visible",t)}}},[e.editDialogConfig.visible?a("edit",{ref:"editForm",attrs:{"is-create":e.editDialogConfig.isCreate,"edit-data":e.editDialogConfig.editData},on:{hideEditDialog:e.hideEditDialog}}):e._e()],1)],1)},i=[],l=a("cc5e"),r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"pram",attrs:{model:e.pram,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"角色名称",prop:"roleName",rules:[{required:!0,message:"请填写角色名称",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"身份名称"},model:{value:e.pram.roleName,callback:function(t){e.$set(e.pram,"roleName",t)},expression:"pram.roleName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态"}},[a("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},model:{value:e.pram.status,callback:function(t){e.$set(e.pram,"status",t)},expression:"pram.status"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"菜单权限"}},[a("el-checkbox",{on:{change:function(t){return e.handleCheckedTreeExpand(t,"menu")}},model:{value:e.menuExpand,callback:function(t){e.menuExpand=t},expression:"menuExpand"}},[e._v("展开/折叠")]),e._v(" "),a("el-checkbox",{on:{change:function(t){return e.handleCheckedTreeConnect(t,"menu")}},model:{value:e.menuCheckStrictly,callback:function(t){e.menuCheckStrictly=t},expression:"menuCheckStrictly"}},[e._v("父子联动")]),e._v(" "),a("el-tree",{ref:"menu",staticClass:"tree-border",attrs:{data:e.menuOptions,"show-checkbox":"","node-key":"id","default-expand-all":e.expandAll,"check-strictly":!e.menuCheckStrictly,"empty-text":"加载中，请稍候",props:e.defaultProps}})],1),e._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:admin:role:update"],expression:"['merchant:admin:role:update']"}],attrs:{loading:e.loading,size:"small",type:"primary"},on:{click:function(t){return e.handlerSubmit("pram")}}},[e._v(e._s(0===e.isCreate?"确定":"更新"))]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:e.close}},[e._v("取消")])],1)],1)],1)},s=[],o=a("61f7"),c=a("a78e"),u=a.n(c),d={name:"roleEdit",props:{isCreate:{type:Number,required:0},editData:{type:Object,default:null}},data:function(){return{expandAll:!1,loading:!1,pram:{roleName:null,rules:"",status:null,id:null,merId:JSON.parse(u.a.get("JavaMerInfo")).id},menuExpand:!1,menuNodeAll:!1,menuOptions:[],menuCheckStrictly:!0,currentNodeId:[],defaultProps:{children:"childList",label:"name"},menuIds:[]}},mounted:function(){this.initEditData(),this.getCacheMenu()},methods:{close:function(){this.$emit("hideEditDialog")},initEditData:function(){var e=this;if(1===this.isCreate){var t=this.editData,a=t.roleName,n=t.status,i=t.id;t.merId;this.pram.roleName=a,this.pram.status=n,this.pram.id=i,this.pram.merId=JSON.parse(u.a.get("JavaMerInfo")).id;var r=this.$loading({lock:!0,text:"Loading"});l["c"](i).then((function(t){e.menuOptions=t.menuList,e.checkDisabled(e.menuOptions),r.close(),e.getTreeId(t.menuList),e.$nextTick((function(){e.menuIds.forEach((function(t,a){var n=e.$refs.menu.getNode(t);n.isLeaf&&e.$refs.menu.setChecked(n,!0)}))}))}))}},handlerSubmit:Object(o["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(e){var a=t.getMenuAllCheckedKeys().toString();t.pram.rules=a,0===t.isCreate?t.handlerSave():t.handlerEdit()}}))})),handlerSave:function(){var e=this;this.loading=!0,l["a"](this.pram).then((function(t){e.$message.success("创建身份成功"),e.$emit("hideEditDialog"),e.loading=!1})).catch((function(t){e.loading=!1}))},handlerEdit:function(){var e=this;this.loading=!0,l["f"](this.pram).then((function(t){e.$message.success("更新身份成功"),e.$emit("hideEditDialog"),e.loading=!1})).catch((function(t){e.loading=!1}))},rulesSelect:function(e){this.pram.rules=e},handleCheckedTreeExpand:function(e,t){if(this.expandAll=!!this.menuExpand,"menu"==t)for(var a=this.menuOptions,n=0;n<a.length;n++)this.$refs.menu.store.nodesMap[a[n].id].expanded=e},handleCheckedTreeNodeAll:function(e,t){"menu"==t&&this.$refs.menu.setCheckedNodes(e?this.menuOptions:[])},handleCheckedTreeConnect:function(e,t){"menu"==t&&(this.menuCheckStrictly=!!e)},getMenuAllCheckedKeys:function(){var e=this.$refs.menu.getCheckedKeys(),t=this.$refs.menu.getHalfCheckedKeys();return e.unshift.apply(e,t),e},getCacheMenu:function(){var e=this;if(0===this.isCreate){var t=this.$loading({lock:!0,text:"Loading"});l["e"]().then((function(a){e.menuOptions=a,e.checkDisabled(e.menuOptions),t.close()}))}},getTreeId:function(e){for(var t in e)e[t].checked&&this.menuIds.push(e[t].id),e[t].childList&&this.getTreeId(e[t].childList)},checkDisabled:function(e){var t=this;e.forEach((function(e){346!==e.id&&350!==e.id&&351!==e.id||(e.disabled=!0,e.childList.forEach((function(e){e.disabled=!0,t.$nextTick((function(){var a=t.$refs.menu.getNode(e.id);a.isLeaf&&t.$refs.menu.setChecked(a,!0)}))})))}))}}},m=d,h=a("2877"),f=Object(h["a"])(m,r,s,!1,null,"76dae7a4",null),p=f.exports,v=a("e350"),g={components:{edit:p},data:function(){return{constants:this.$constants,listData:{list:[]},listPram:{createTime:null,updateTime:null,level:null,page:1,limit:this.$constants.page.limit[0],roleName:"",rules:null,status:""},menuList:[],editDialogConfig:{visible:!1,isCreate:0,editData:{}}}},mounted:function(){this.handleGetRoleList()},methods:{checkPermi:v["a"],handlerOpenDel:function(e){var t=this;this.$modalSure("删除当前数据").then((function(){l["b"](e.id).then((function(e){t.$message.success("删除数据成功"),t.handleGetRoleList()}))}))},handleGetRoleList:function(){var e=this;l["d"](this.listPram).then((function(t){e.listData=t}))},handlerOpenEdit:function(e,t){this.editDialogConfig.editData=1===e?t:{},this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},hideEditDialog:function(){this.editDialogConfig.visible=!1,this.handleGetRoleList()},handleSizeChange:function(e){this.listPram.limit=e,this.handleGetRoleList(this.listPram)},handleCurrentChange:function(e){this.listPram.page=e,this.handleGetRoleList(this.listPram)},handleStatusChange:function(e){var t=this;l["g"](e).then((function(e){t.$message.success("更新状态成功"),t.handleGetRoleList()}))}}},b=g,C=Object(h["a"])(b,n,i,!1,null,"020a8c65",null);t["default"]=C.exports},cc5e:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return s})),a.d(t,"f",(function(){return o})),a.d(t,"g",(function(){return c})),a.d(t,"e",(function(){return u}));var n=a("b775");function i(e){var t={roleName:e.roleName,status:e.status,rules:e.rules,merId:e.merId};return Object(n["a"])({url:"/admin/merchant/role/save",method:"POST",data:t})}function l(e){return Object(n["a"])({url:"admin/merchant/role/delete/".concat(e),method:"post"})}function r(e){return Object(n["a"])({url:"/admin/merchant/role/info/".concat(e),method:"GET"})}function s(e){var t={page:e.page,limit:e.limit,roleName:e.roleName,status:e.status};return Object(n["a"])({url:"/admin/merchant/role/list",method:"get",params:t})}function o(e){var t={id:e.id,roleName:e.roleName,rules:e.rules,status:e.status,merId:e.merId};return Object(n["a"])({url:"/admin/merchant/role/update",method:"post",data:t})}function c(e){return Object(n["a"])({url:"/admin/merchant/role/updateStatus",method:"post",data:{id:e.id,status:e.status,merid:e.merid}})}function u(e){return Object(n["a"])({url:"/admin/merchant/menu/cache/tree",method:"get"})}}}]);