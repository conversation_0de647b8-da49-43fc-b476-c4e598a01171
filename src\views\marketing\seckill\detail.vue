<template>
  <div class="app-container">
    <div class="content-wrapper">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 基本内容 -->
        <el-tab-pane label="基本内容" name="basic">
          <basic-info :activity-id="activityId" :activity-data="activityData" />
        </el-tab-pane>

        <!-- 活动商品 -->
        <el-tab-pane label="活动商品" name="products">
          <product-list :activity-id="activityId" :seckill-price="activityData.seckillPrice" />
        </el-tab-pane>

        <!-- 参与商户 -->
        <el-tab-pane label="参与商户" name="merchants">
          <merchant-list
            :merchant-list="merchantList"
            :loading="merchantLoading"
            :pagination="merchantPagination"
            :merchant-store-count="merchantStoreCount"
            :all-stores="allStores"
            :expanded-rows="expandedRows"
            @search="handleMerchantSearch"
            @toggle-expand="toggleMerchantExpand"
            @size-change="handleMerchantSizeChange"
            @page-change="handleMerchantPageChange"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { getActivityInfoApi, getActivityMerchantListApi } from '@/api/discount';
import BasicInfo from './components/BasicInfo.vue';
import ProductList from './components/ProductList.vue';
import MerchantList from './components/MerchantList.vue';

export default {
  name: 'DiscountDetail',
  components: {
    BasicInfo,
    ProductList,
    MerchantList,
  },
  data() {
    return {
      activeTab: 'basic',
      activityId: null,
      activityData: {}, // 新增活动详情数据
      merchantList: [],
      merchantLoading: false,
      merchantPagination: {
        page: 1,
        limit: 20,
        total: 0,
      },
      merchantStoreCount: 0,
      expandedRows: [],
      allStores: [],
      filteredMerchantList: [],
      merchantSearchForm: {
        merchantName: '',
        storeName: '',
      },
    };
  },
  mounted() {
    this.activityId = this.$route.params.id;
    this.getActivityInfo(); // 新增获取活动详情
    this.getMerchantList();
  },
  methods: {
    /**
     * 获取活动详情
     */
    async getActivityInfo() {
      if (!this.activityId) return;
      try {
        this.activityData = await getActivityInfoApi(this.activityId);
      } catch (error) {
        console.error('获取活动详情失败:', error);
        this.$message.error('获取活动详情失败');
      }
    },

    /**
     * 获取参与商户列表
     */
    /**
     * 获取参与商户列表
     */
    async getMerchantList() {
      this.merchantLoading = true;
      try {
        const params = {
          activityId: this.activityId,
          page: this.merchantPagination.page,
          limit: this.merchantPagination.limit,
          ...this.merchantSearchForm,
        };

        // 由于request.js拦截器只返回data部分，直接使用返回的数据
        const data = await getActivityMerchantListApi(params);

        // 处理商户数据
        this.merchantList = data.list.map((item) => ({
          id: item.merchantId,
          merchantId: item.merchantId,
          merchantName: item.merchantName,
          stores: item.stores || [],
        }));

        // 处理门店数据，用于展开显示
        this.allStores = [];
        data.list.forEach((merchant) => {
          if (merchant.stores && merchant.stores.length > 0) {
            merchant.stores.forEach((store) => {
              this.allStores.push({
                id: store.storeId,
                merchantId: merchant.merchantId,
                name: store.storeName,
              });
            });
          }
        });

        // 更新分页信息
        this.merchantPagination.page = data.page;
        // this.merchantPagination.limit = data.limit;
        this.merchantPagination.total = data.total;

        // 计算门店总数
        this.merchantStoreCount = this.allStores.length;

        console.log('获取参与商户列表成功:', data);
      } catch (error) {
        console.error('获取参与商户列表失败:', error);
        this.$message.error('获取参与商户列表失败');
      } finally {
        this.merchantLoading = false;
      }
    },

    /**
     * 商户搜索过滤
     * @param {Object} form - 搜索表单数据
     */
    handleMerchantSearch(form) {
      this.merchantSearchForm = form;
      this.merchantPagination.page = 1; // 重置到第一页
      this.getMerchantList();
    },

    /**
     * 切换商户展开状态
     * @param {Object} row - 商户行数据
     */
    toggleMerchantExpand(row) {
      const index = this.expandedRows.indexOf(row.id);
      if (index > -1) {
        this.expandedRows.splice(index, 1);
      } else {
        this.expandedRows.push(row.id);
      }
    },

    /**
     * 检查行是否展开
     * @param {Number} rowId - 行ID
     * @returns {Boolean} 是否展开
     */
    isRowExpanded(rowId) {
      return this.expandedRows.includes(rowId);
    },

    /**
     * 根据商户ID获取门店列表
     * @param {Number} merchantId - 商户ID
     * @returns {Array} 门店列表
     */
    getStoresByMerchantId(merchantId) {
      return this.allStores.filter((store) => store.merchantId === merchantId);
    },

    /**
     * 商户分页 - 每页数量变化
     * @param {Number} val - 新的每页显示数量
     */
    handleMerchantSizeChange(val) {
      this.merchantPagination.limit = val;
      this.getMerchantList();
    },

    /**
     * 商户分页 - 页码变化
     * @param {Number} val - 新的页码
     */
    handleMerchantPageChange(val) {
      this.merchantPagination.page = val;
      this.getMerchantList();
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.app-breadcrumb {
  margin-bottom: 20px;
}

.content-wrapper {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-tabs {
  ::v-deep .el-tabs__header {
    margin-bottom: 20px;
  }

  ::v-deep .el-tabs__item {
    font-size: 14px;
    font-weight: 500;
  }
}

.basic-content {
  .info-row {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;

    .label {
      width: 150px;
      color: #606266;
      font-size: 14px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      flex: 1;
    }
  }
}

.merchants-content {
  .merchant-header {
    margin-bottom: 20px;

    .search-section {
      display: flex;
      align-self: flex-start;
    }

    .merchant-stats {
      margin-top: 10px;
      .stats-text {
        font-size: 14px;
        color: #0080ff;
      }
    }
  }
}

.expanded-stores {
  background: #f8f9fa;
  padding: 15px;
  margin: 10px 0;
  border-radius: 4px;

  .store-header {
    display: flex;
    margin-bottom: 10px;
    font-weight: bold;
    color: #303133;

    .store-label {
      flex: 1;
      padding: 8px 12px;
      background: #e6f7ff;
      border: 1px solid #91d5ff;

      &:first-child {
        border-right: none;
      }
    }
  }

  .store-item {
    display: flex;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }

    .store-id,
    .store-name {
      flex: 1;
      padding: 8px 12px;
      background: #fff;
      border: 1px solid #d9d9d9;
      color: #303133;

      &:first-child {
        border-right: none;
      }
    }
  }
}

.block {
  margin-top: 20px;
  text-align: right;
}

.merchants-content {
  .expanded-stores {
    background-color: #f5f5f5;
    padding: 10px;
    margin: 0;

    .store-header {
      display: flex;
      font-weight: bold;
      margin-bottom: 8px;

      .store-column {
        flex: 1;
        padding: 5px 10px;
        text-align: left;

        &:first-child {
          margin-right: 5px;
          max-width: 100px;
        }

        &:last-child {
          min-width: 200px;
        }
      }
    }

    .store-row {
      display: flex;
      margin-bottom: 5px;

      .store-column {
        flex: 1;
        padding: 5px 10px;
        text-align: left;

        &:first-child {
          margin-right: 5px;
          max-width: 100px;
        }

        &:last-child {
          min-width: 200px;
        }
      }
    }
  }

  .stats-text {
    color: #409eff;
  }

  .stats-number {
    color: #409eff;
  }
}
</style>
