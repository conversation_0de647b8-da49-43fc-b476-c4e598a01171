// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import * as store from '@/api/product';
import * as logistics from '@/api/logistics';

/**
 * 过滤平台端的特殊类型
 * @param {Array} adminProductClassify 平台端所有类型
 * @returns 所有的特殊类型（镜架、镜片）
 */
function filterClassify(adminProductClassify) {
  let lensArr = [];
  let eyeglassArr = [];
  if (Array.isArray(adminProductClassify)) {
    adminProductClassify.forEach((item) => {
      if (item.name == '眼镜' && Array.isArray(item.childList)) {
        item.childList.forEach((item1) => {
          if (item1.name == '光学镜片') {
            lensArr = lensArr.concat(item1.childList);
          }
        });
      }

      if (item.name == '眼镜' && Array.isArray(item.childList)) {
        item.childList.forEach((item1) => {
          if (item1.name == '光学眼镜架') {
            eyeglassArr = eyeglassArr.concat(item1.childList);
          }
        });
      }
    });
  }
  return {
    eyeglass: eyeglassArr,
    lens: lensArr,
  };
}

const adminProductClassify = JSON.parse(localStorage.getItem('adminProductClassify'));
const filteredClassify = filterClassify(adminProductClassify);

const state = {
  adminProductClassify: adminProductClassify || [] /** 平台商品分类 **/,
  merProductClassify: JSON.parse(localStorage.getItem('merProductClassify')) || [] /** 商户商品分类 **/,
  productBrand: [] /** 商品品牌 **/,
  shippingTemplates: JSON.parse(localStorage.getItem('shippingTemplates')) || [] /** 运费模板 **/,
  adminLensClassify: filteredClassify.lens,
  adminEyeglassClassify: filteredClassify.eyeglass,
};

const mutations = {
  SET_AdminProductClassify: (state, adminProductClassify) => {
    state.adminProductClassify = adminProductClassify;
    if (!adminProductClassify.length) localStorage.removeItem('adminProductClassify');
  },

  SET_AdminLensClassify: (state, lensClassify) => {
    state.adminLensClassify = lensClassify;
  },

  SET_AdminEyeglassClassify: (state, eyeglassClassify) => {
    state.adminEyeglassClassify = eyeglassClassify;
  },

  SET_MerProductClassify: (state, merProductClassify) => {
    state.merProductClassify = merProductClassify;
    if (!merProductClassify.length) localStorage.removeItem('merProductClassify');
  },
  SET_ProductBrand: (state, productBrand) => {
    state.productBrand = productBrand;
    if (!productBrand.length) localStorage.removeItem('productBrand');
  },
  SET_ShippingTemplates: (state, shippingTemplates) => {
    state.shippingTemplates = shippingTemplates;
    if (!shippingTemplates.length) localStorage.removeItem('shippingTemplates');
  },
};

const actions = {
  /** 平台商品分类 **/
  getAdminProductClassify({ commit, dispatch }) {
    return new Promise((resolve, reject) => {
      store
        .categoryApi()
        .then(async (res) => {
          let nodes = changeNodes(res);
          commit('SET_AdminProductClassify', nodes);
          const filteredNodes = filterClassify(nodes);

          commit('SET_AdminLensClassify', filteredNodes.lens);
          commit('SET_AdminEyeglassClassify', filteredNodes.eyeglass);
          localStorage.setItem('adminProductClassify', JSON.stringify(nodes));
          resolve(res);
        })
        .catch(reject);
    });
  },

  /** 商户商品分类 **/
  getMerProductClassify({ commit }) {
    return new Promise((resolve, reject) => {
      store
        .storeCategoryAllApi()
        .then(async (res) => {
          commit('SET_MerProductClassify', changeNodes(res));
          localStorage.setItem('merProductClassify', JSON.stringify(changeNodes(res)));
          resolve(res);
        })
        .catch(reject);
    });
  },

  /** 商品品牌 **/
  getMerProductBrand({ commit }) {
    return new Promise((resolve, reject) => {
      store
        .brandAllApi()
        .then(async (res) => {
          commit('SET_ProductBrand', res);
          resolve(res);
        })
        .catch(reject);
    });
  },

  /** 运费模板 **/
  getShippingTemplates({ commit }) {
    return new Promise((resolve, reject) => {
      logistics
        .shippingTemplatesList({
          keywords: '',
          page: 1,
          limit: 9999,
        })
        .then(async (res) => {
          commit('SET_ShippingTemplates', res.list);
          localStorage.setItem('shippingTemplates', JSON.stringify(res.list));
          resolve(res);
        })
        .catch(reject);
    });
  },
};

/** tree去除 childList=[] 的结构**/
const changeNodes = function (data) {
  if (data.length > 0) {
    for (var i = 0; i < data.length; i++) {
      if (!data[i].childList || data[i].childList.length < 1) {
        data[i].childList = undefined;
      } else {
        changeNodes(data[i].childList);
      }
    }
  }
  return data;
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  changeNodes,
};
