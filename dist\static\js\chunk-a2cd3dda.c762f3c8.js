(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a2cd3dda"],{"400c":function(e,t,i){"use strict";i("b2c9")},"93ce":function(e,t,i){},b2c9:function(e,t,i){},dcdc4:function(e,t,i){"use strict";i("93ce")},f91b:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"container"},[i("el-form",{ref:"form",attrs:{inline:"",model:e.form}},[i("el-form-item",{attrs:{label:"模板名称："}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入模板名称",size:"small",clearable:""},model:{value:e.form.keywords,callback:function(t){e.$set(e.form,"keywords",t)},expression:"form.keywords"}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:shipping:templates:list"],expression:"['merchant:shipping:templates:list']"}],attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.handleSearch},slot:"append"})],1)],1)],1)],1),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:shipping:templates:save"],expression:"['merchant:shipping:templates:save']"}],attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handleSubmit()}}},[e._v("添加运费模板")])],1),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData.list,size:"mini","header-cell-style":{fontWeight:"bold"}}},[i("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"60"}}),e._v(" "),i("el-table-column",{attrs:{label:"模板名称","min-width":"180",prop:"name"}}),e._v(" "),i("el-table-column",{attrs:{"min-width":"100",label:"计费方式",prop:"type"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("p",[e._v(e._s(e._f("typeFilter")(r.type)))])]}}])}),e._v(" "),i("el-table-column",{attrs:{"min-width":"100",label:"包邮方式",prop:"appoint"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("p",[e._v(e._s(e._f("statusFilter")(r.appoint)))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"排序","min-width":"100",prop:"sort"}}),e._v(" "),i("el-table-column",{attrs:{label:"添加时间","min-width":"150",prop:"createTime"}}),e._v(" "),i("el-table-column",{attrs:{prop:"address",fixed:"right",width:"120",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:shipping:templates:update","merchant:shipping:templates:info"],expression:"['merchant:shipping:templates:update', 'merchant:shipping:templates:info']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.bindEdit(t.row)}}},[e._v("修改")]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:shipping:templates:delete"],expression:"['merchant:shipping:templates:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.bindDelete(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),i("div",{staticClass:"block-pagination"},[i("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":e.tableData.limit,"current-page":e.tableData.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"current-change":e.pageChange,"size-change":e.handleSizeChange}})],1)],1),e._v(" "),i("CreatTemplates",{ref:"addTemplates",on:{getList:e.getList}})],1)},n=[],a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.dialogVisible?i("el-drawer",{attrs:{title:"运费模板",visible:e.dialogVisible,size:"1000px",direction:"rtl","before-close":e.handleClose,"append-to-body":!0,"modal-append-to-body":!1,wrapperClosable:!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("div",{staticClass:"demo-drawer__content"},[e.dialogVisible?i("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,"label-width":"100px",size:"mini",rules:e.rules}},[i("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[i("el-input",{staticClass:"withs",attrs:{placeholder:"请输入模板名称"},model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"包邮方式",prop:"appoint"}},[i("el-radio-group",{on:{change:e.changeAppoint},model:{value:e.ruleForm.appoint,callback:function(t){e.$set(e.ruleForm,"appoint",t)},expression:"ruleForm.appoint"}},[i("el-radio",{attrs:{label:0}},[e._v("全国包邮")]),e._v(" "),i("el-radio",{attrs:{label:1}},[e._v("部分包邮")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("自定义")])],1)],1),e._v(" "),Number(e.ruleForm.appoint)>0?[i("el-form-item",{attrs:{label:"计费方式",prop:"type"}},[i("el-radio-group",{on:{change:function(t){return e.changeRadio(e.ruleForm.type)}},model:{value:e.ruleForm.type,callback:function(t){e.$set(e.ruleForm,"type",t)},expression:"ruleForm.type"}},[i("el-radio",{attrs:{label:1}},[e._v("按件数")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("按重量")]),e._v(" "),i("el-radio",{attrs:{label:3}},[e._v("按体积")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"运费",prop:"region"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"tempBox",staticStyle:{width:"100%"},attrs:{data:e.ruleForm.region,border:"",fit:"","highlight-current-row":"",size:"mini"}},[i("el-table-column",{attrs:{align:"center",label:"送达到","min-width":"260",prop:"city_ids"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.$index&&2===e.ruleForm.appoint?i("span",[e._v("默认运费")]):i("el-cascader",{ref:"cascader",staticStyle:{width:"98%"},attrs:{options:e.cityList,rules:e.rules.city_ids,props:e.props,filterable:""},on:{change:e.changeRegion},model:{value:t.row.city_ids,callback:function(i){e.$set(t.row,"city_ids",i)},expression:"scope.row.city_ids"}})]}}],null,!1,4171889844)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:e.columns.title,prop:"first"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.first,prop:"region."+t.$index+".first"}},[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:t.row.first,callback:function(i){e.$set(t.row,"first",i)},expression:"scope.row.first"}})],1)]}}],null,!1,3562015974)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"运费（元）",prop:"firstPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.firstPrice,prop:"region."+t.$index+".firstPrice"}},[i("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:t.row.firstPrice,callback:function(i){e.$set(t.row,"firstPrice",i)},expression:"scope.row.firstPrice"}})],1)]}}],null,!1,3623685529)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:e.columns.title2,prop:"renewal"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.renewal,prop:"region."+t.$index+".renewal"}},[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:t.row.renewal,callback:function(i){e.$set(t.row,"renewal",i)},expression:"scope.row.renewal"}})],1)]}}],null,!1,3469255770)}),e._v(" "),i("el-table-column",{attrs:{"class-name":"status-col",align:"center",label:"续费（元）","min-width":"120",prop:"renewalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.renewalPrice,prop:"region."+t.$index+".renewalPrice"}},[i("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:t.row.renewalPrice,callback:function(i){e.$set(t.row,"renewalPrice",i)},expression:"scope.row.renewalPrice"}})],1)]}}],null,!1,2943116773)}),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===e.ruleForm.appoint||1!==e.ruleForm.appoint&&t.$index>0?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return e.confirmEdit(e.ruleForm.region,t.$index)}}},[e._v("\n                  删除\n                ")]):e._e()]}}],null,!1,4187321847)})],1)],1),e._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.addRegion(e.ruleForm.region)}}},[e._v("\n            添加区域\n          ")])],1),e._v(" "),2===e.ruleForm.appoint?i("el-form-item",{attrs:{label:"包邮区域"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.ruleForm.free,border:"",fit:"","highlight-current-row":"",size:"mini"}},[i("el-table-column",{attrs:{align:"center",label:"选择区域","min-width":"220"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("el-cascader",{ref:"cascader",staticStyle:{width:"95%"},attrs:{options:e.cityList,props:e.props,clearable:""},model:{value:r.city_ids,callback:function(t){e.$set(r,"city_ids",t)},expression:"row.city_ids"}})]}}],null,!1,1273008282)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"180px",align:"center",label:e.columns.title3},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:r.number,callback:function(t){e.$set(r,"number",t)},expression:"row.number"}})]}}],null,!1,4105714930)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"包邮金额（元）"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("el-input-number",{attrs:{"controls-position":"right"},model:{value:r.price,callback:function(t){e.$set(r,"price",t)},expression:"row.price"}})]}}],null,!1,2942908482)}),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"操作","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return e.confirmEdit(e.ruleForm.free,t.$index)}}},[e._v("\n                  删除\n                ")])]}}],null,!1,1898196105)})],1)],1):e._e(),e._v(" "),2===e.ruleForm.appoint?i("el-form-item",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.addFree(e.ruleForm.free)}}},[e._v("\n            添加指定包邮区域\n          ")])],1):e._e()]:e._e(),e._v(" "),i("el-form-item",{attrs:{label:"排序"}},[i("el-input",{staticClass:"withs",attrs:{placeholder:"请输入排序"},model:{value:e.ruleForm.sort,callback:function(t){e.$set(e.ruleForm,"sort",t)},expression:"ruleForm.sort"}})],1)],2):e._e()],1),e._v(" "),i("div",{staticClass:"demo-drawer__footer from-foot-btn btn-shadow drawer_fix"},[i("el-button",{on:{click:function(t){return e.handleClose("ruleForm")}}},[e._v("取 消")]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["merchant:shipping:templates:update"],expression:"['merchant:shipping:templates:update']"}],attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.onsubmit("ruleForm")}}},[e._v("确 定")])],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"})]):e._e()},l=[],s=i("2f2c"),o=i("5c96"),c=i("61f7");i("fca7");function u(e){return f(e)||m(e)||d(e)||p()}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"===typeof e)return g(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?g(e,t):void 0}}function m(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function f(e){if(Array.isArray(e))return g(e)}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}var h={name:"",type:1,appoint:0,sort:0,region:[{first:1,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[],cityId:0}],undelivery:0,free:[],undelives:{}},b="重量（kg）",y="体积（m³）",v=[{title:"首件",title2:"续件",title3:"包邮件数"},{title:"首件".concat(b),title2:"续件".concat(b),title3:"包邮".concat(b)},{title:"首件".concat(y),title2:"续件".concat(y),title3:"包邮".concat(y)}],_={name:"CreatTemplates",components:{},data:function(){return{loading:!1,rules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],free:[{type:"array",required:!0,message:"请至少添加一个区域",trigger:"change"}],appoint:[{required:!0,message:"请选择包邮方式",trigger:"change"}],undelivery:[{required:!0,message:"请选择是否指定区域不配送",trigger:"change"}],type:[{required:!0,message:"请选择计费方式",trigger:"change"}],region:[{required:!0,message:"请选择区域运费",trigger:"change"}],city_ids:[{type:"array",required:!0,message:"请至少选择一个区域",trigger:"change"}],first:[{required:!0,message:"请输入",trigger:"blur"}],renewal:[{required:!0,message:"请输入",trigger:"blur"}],firstPrice:[{required:!0,message:"请输入运费",trigger:"blur"}],renewalPrice:[{required:!0,message:"请输入续费",trigger:"blur"}]},nodeKey:"city_ids",props:{children:"child",label:"regionName",value:"regionId",multiple:!0},dialogVisible:!1,ruleForm:Object.assign({},h),listLoading:!1,cityList:[],columns:{title:"首件",title2:"续件",title3:"包邮件数"},tempId:0,regionNew:[{first:1,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[],cityId:0}],type:0}},mounted:function(){var e=localStorage.getItem("cityList")?JSON.parse(localStorage.getItem("cityList")):[];this.cityList=this.changeNodes(e)},methods:{changeAppoint:function(){u(this.ruleForm.region)},changType:function(e){this.type=e},confirmEdit:function(e,t){e.splice(t,1)},popoverHide:function(){},handleClose:function(){var e=this;this.dialogVisible=!1,setTimeout((function(){e.ruleForm={name:"",type:1,appoint:0,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[],cityId:0}],undelivery:0,free:[],undelives:{}}}),1e3),this.columns=Object.assign({},v[0])},changeNodes:function(e){if(e.length>0)for(var t=0;t<e.length;t++)!e[t].child||e[t].child.length<1?e[t].child=void 0:this.changeNodes(e[t].child);return e},changeRegion:function(e){},judgeDisabled:function(e,t){var i=this;e.map((function(e,r){e.disabled=e.regionId===t[r],e.child&&e.child.length&&i.judgeDisabled(e.child)}))},changeRadio:function(e){this.columns=Object.assign({},v[e-1])},addRegion:function(e){e.push(Object.assign({},{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[],cityId:""}))},addFree:function(e){e.push(Object.assign({},{number:1,price:1,city_ids:[],cityId:""}))},getInfo:function(e,t){var i=this;this.tempId=e;var r=o["Loading"].service({fullscreen:!0});s["h"]({id:e}).then((function(e){i.dialogVisible=!0;var t=e;0===t.appoint&&(t.type=1),Number(t.appoint)>0&&(t.regionList&&t.regionList.length>0&&t.regionList.forEach((function(e,t){e.title=JSON.parse(e.title),e.city_ids=e.title})),t.freeList&&t.freeList.length>0&&t.freeList.forEach((function(e,t){e.title=JSON.parse(e.title),e.city_ids=e.title}))),i.ruleForm=Object.assign(i.ruleForm,{name:t.name,type:t.type,appoint:t.appoint,sort:t.sort,region:t.regionList||[],free:t.freeList||[]}),i.regionNew=u(i.ruleForm.region),i.columns=Object.assign({},v[i.ruleForm.type-1]),i.$nextTick((function(){r.close()}))})).catch((function(e){i.$message.error(e.message),i.$nextTick((function(){r.close()}))}))},getCityList:function(){var e=this;s["a"]().then((function(t){localStorage.setItem("cityList",JSON.stringify(t));var i=JSON.parse(localStorage.getItem("cityList"));e.cityList=i,e.changeNodes(i)})).catch((function(t){e.$message.error(t.message)}))},change:function(e){return e.map((function(e){var t=[];e.city_ids.map((function(e){e.splice(0,1),t.push(e[0])})),e.city_ids=t})),e},changeOne:function(e){var t=[];return e.map((function(e){e.splice(0,1),t.push(e[0])})),t},onsubmit:Object(c["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.loading=!0;var i={appoint:t.ruleForm.appoint,name:t.ruleForm.name,sort:t.ruleForm.sort,type:t.ruleForm.type,shippingTemplatesRegionRequestList:[],shippingTemplatesFreeRequestList:[],id:t.tempId};t.ruleForm.region.forEach((function(e,t){if(e.title=e.city_ids.length>0?JSON.stringify(e.city_ids):JSON.stringify([[0,0]]),"[[0,0]]"===e.title)e.cityId="0";else{for(var i=0;i<e.city_ids.length;i++)e.city_ids[i].splice(0,2);e.cityId=e.city_ids.length>0?e.city_ids.join(","):"0"}})),i.shippingTemplatesRegionRequestList=t.ruleForm.region,i.shippingTemplatesRegionRequestList.forEach((function(e,i){t.$delete(e,"city_ids")})),t.ruleForm.free.forEach((function(e,t){if(e.title=e.city_ids.length>0?JSON.stringify(e.city_ids):JSON.stringify([[0,0]]),"[[0,0]]"===e.title)e.cityId="0";else{for(var i=0;i<e.city_ids.length;i++)e.city_ids[i].splice(0,2);e.cityId=e.city_ids.length>0?e.city_ids.join(","):"0"}})),i.shippingTemplatesFreeRequestList=t.ruleForm.free,i.shippingTemplatesFreeRequestList.forEach((function(e,i){t.$delete(e,"city_ids")})),2===t.ruleForm.appoint&&t.ruleForm.region.map((function(e,i){t.ruleForm.region[0].title="[[0,0]]",t.ruleForm.region[0].cityId="0"})),0===t.ruleForm.appoint&&(t.$delete(i,"shippingTemplatesRegionRequestList"),t.$delete(i,"shippingTemplatesFreeRequestList"),t.ruleForm.type=0),0===t.type?s["e"](i).then((function(e){t.$message.success("操作成功"),t.$emit("getList"),t.loading=!1,t.$store.commit("product/SET_ShippingTemplates",[]),t.handleClose()})).catch((function(){t.loading=!1})):s["g"](i).then((function(e){t.$message.success("操作成功"),t.$emit("getList"),t.handleClose(),t.loading=!1})).catch((function(){t.loading=!1}))}))})),clear:function(){this.ruleForm.name="",this.ruleForm.sort=0}}},w=_,F=(i("dcdc4"),i("2877")),x=Object(F["a"])(w,a,l,!1,null,"66c6305d",null),S=x.exports,k={name:"ShippingTemplates",filters:{statusFilter:function(e){var t={0:"全国包邮",1:"部分包邮",2:"自定义"};return t[e]},typeFilter:function(e){var t={0:"无",1:"按件数",2:"按重量",3:"按体积"};return t[e]}},components:{CreatTemplates:S},data:function(){return{isShow:!1,dialogVisible:!1,form:{keywords:""},tableData:"",page:1,limit:20,loading:!1}},created:function(){this.getDataList()},methods:{handleSubmit:function(){this.$refs.addTemplates.dialogVisible=!0,localStorage.getItem("cityList")||this.$refs.addTemplates.getCityList(),this.$refs.addTemplates.changType(0)},handleSearch:function(){this.page=1,this.getDataList()},pageChange:function(e){this.page=e,this.getDataList()},handleSizeChange:function(e){this.limit=e,this.getDataList()},getDataList:function(){var e=this;this.loading=!0,s["f"]({keywords:encodeURIComponent(this.form.keywords),page:this.page,limit:this.limit}).then((function(t){e.loading=!1,e.tableData=t}))},bindEdit:function(e){localStorage.getItem("cityList")||this.$refs.addTemplates.getCityList(),this.$refs.addTemplates.getInfo(e.id,e.appoint),this.$refs.addTemplates.changType(1)},bindDelete:function(e){var t=this;this.$modalSure().then((function(){s["d"]({id:e.id}).then((function(e){t.$message.success("删除成功"),t.$store.commit("product/SET_ShippingTemplates",[]),t.getDataList()}))}))},getList:function(){this.getDataList()}}},L=k,$=(i("400c"),Object(F["a"])(L,r,n,!1,null,"7ae25890",null));t["default"]=$.exports}}]);