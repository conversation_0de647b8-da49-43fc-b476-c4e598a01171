stages:
  - build
  - deploy


variables:
  MYAPP: "ooseek-web-mer"
  DOCKER_URL: "registry.cn-qingdao.aliyuncs.com"
  DOCKER_USER: "<EMAIL>"
  DOCKER_PW: "Dk111112"

before_script:
  - echo "$(pwd)"

toDevBuild:
  image: node:8-alpine
  stage: build
  script:
    #- npm install -g cnpm --registry=https://registry.npm.taobao.org
    - npm install
    - npm run build:prod
  artifacts:
    expire_in: 5 min
    paths:
      - ./dist
  tags:
    - java
  only:
    - /^v.*$/
  except:
    - branches


toDockerStable:
  stage: deploy
  script:
    - docker build -t "$DOCKER_URL"/jlm/"$MYAPP":"$CI_COMMIT_TAG" .
    - docker login "$DOCKER_URL" -p "$DOCKER_PW" -u "$DOCKER_USER"
    - docker push "$DOCKER_URL"/jlm/"$MYAPP":"$CI_COMMIT_TAG"
    - docker rmi --force `docker images | grep "$MYAPP" | awk '{print $3}'`
  tags:
    - java
  only:
    - /^v.*$/
  except:
    - branches
