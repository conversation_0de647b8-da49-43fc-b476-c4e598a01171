import request from '@/utils/request';

/**
 * 门店分页列表
 * @param {Object} params - 查询参数
 */
export function storesListApi(params) {
  return request({
    url: 'admin/merchant/stores/list',
    method: 'get',
    params,
  });
}

/**
 * 获取门店详情
 * @param {Number} id - 门店ID
 */
export function storesDetailApi(id) {
  return request({
    url: `admin/merchant/stores/detail/${id}`,
    method: 'get',
  });
}

/**
 * 添加门店
 * @param {Object} data - 门店数据
 */
export function storesAddApi(data) {
  return request({
    url: 'admin/merchant/stores/add',
    method: 'post',
    data,
  });
}

/**
 * 编辑门店
 * @param {Object} data - 门店数据
 */
export function storesUpdateApi(data) {
  return request({
    url: 'admin/merchant/stores/update',
    method: 'post',
    data,
  });
}

/**
 * 删除门店
 * @param {Number} id - 门店ID
 */
export function storesDeleteApi(id) {
  return request({
    url: `admin/merchant/stores/delete/${id}`,
    method: 'post',
  });
}

/**
 * 修改门店配送方式
 * @param {Object} data - 配送方式数据
 */
export function updateDeliveryApi(data) {
  return request({
    url: 'admin/merchant/stores/updateDelivery',
    method: 'post',
    data,
  });
}
