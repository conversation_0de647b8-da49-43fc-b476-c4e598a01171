<template>
  <div class="delivery-page">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="仅自提" name="2"></el-tab-pane>
      <el-tab-pane label="仅配送" name="1"></el-tab-pane>
      <el-tab-pane label="仅快递" name="3"></el-tab-pane>
    </el-tabs>

    <el-form :inline="true" :model="searchForm" class="search-form" size="small">
      <el-form-item label="商品搜索：">
        <el-input v-model="searchForm.productName" placeholder="请输入商品名称" clearable />
      </el-form-item>
      <el-button type="primary" @click="handleSearch">查询</el-button>
    </el-form>
    <div style="margin-bottom: 16px">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="primary" @click="handleImport">导入</el-button>
      <el-button type="primary" @click="handleExport">导出</el-button>
    </div>

    <el-table :data="tableData" style="width: 100%; margin-top: 20px" size="small" v-loading="loading" class="mt20">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column label="商品图" width="80">
        <template slot-scope="scope">
          <el-image
            style="width: 36px; height: 36px"
            :src="scope.row.productImage"
            :preview-src-list="[scope.row.productImage]"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="productName"
        label="商品名称"
        min-width="200"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="block">
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        :current-page="currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog
      title="添加商品"
      :visible.sync="addDialogVisible"
      width="500px"
      @close="resetAddForm"
      class="center-dialog-title"
    >
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="0">
        <el-form-item prop="productId">
          <el-input v-model="addForm.productId" placeholder="请输入商品ID" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer center-footer">
        <el-button type="primary" @click="submitAddForm">确认添加</el-button>
      </div>
    </el-dialog>

    <!-- 批量导入弹窗 -->
    <el-dialog title="批量导入" :visible.sync="importDialogVisible" width="700px" class="center-dialog-title">
      <div
        style="
          display: flex;
          justify-content: center;
          margin: 40px 0;
          gap: 32px;
          flex-direction: column;
          align-items: center;
        "
      >
        <el-upload
          class="upload-demo"
          :show-file-list="false"
          :before-upload="beforeImportUpload"
          :http-request="handleImportUpload"
          accept=".xls,.xlsx"
        >
          <el-button
            style="
              width: 320px;
              height: 80px;
              font-size: 28px;
              border: 2px solid #bfcbd9;
              background: #fff;
              color: #409eff;
            "
            >点击上传Excel文件</el-button
          >
        </el-upload>
        <div style="color: #999; font-size: 16px; margin-top: 12px">仅支持Excel文件（.xls, .xlsx）</div>
      </div>
      <div slot="footer" style="text-align: right">
        <el-button @click="importDialogVisible = false" type="primary" plain style="width: 120px; font-size: 28px"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProductDeliveryList,
  addProductDelivery,
  deleteProductDeliveryById,
  batchImportProductDelivery,
  exportProductDelivery,
} from '@/api/productdelivery';

export default {
  name: 'ProductDeliveryList',
  data() {
    return {
      activeTab: '2',
      searchForm: {
        productName: '',
      },
      tableData: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      loading: false,
      addDialogVisible: false,
      addForm: {
        productId: '',
      },
      addFormRules: {
        productId: [
          { required: true, message: '请输入商品ID', trigger: 'blur' },
          { pattern: /^[0-9]+$/, message: '只能输入数字', trigger: 'blur' },
        ],
      },
      importDialogVisible: false,
      importLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      const params = {
        productName: this.searchForm.productName,
        page: this.currentPage,
        limit: this.pageSize,
        type: this.activeTab, // 假设后端支持type区分自提/配送/快递
      };
      getProductDeliveryList(params)
        .then((res) => {
          this.tableData = res.list || [];
          this.total = res.total || 0;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handleAdd() {
      this.addDialogVisible = true;
    },
    handleImport() {
      this.importDialogVisible = true;
    },
    handleDelete(row) {
      this.$confirm('确认要删除该商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 调用删除接口
          deleteProductDeliveryById(row.id)
            .then(() => {
              this.$message.success('删除成功');
              this.getList();
            })
            .catch(() => {
              this.$message.error('删除失败');
            });
        })
        .catch(() => {});
    },
    handleTabClick(tab) {
      this.activeTab = tab.name;
      this.currentPage = 1;
      this.getList();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.getList();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.getList();
    },
    resetAddForm() {
      this.$refs.addFormRef && this.$refs.addFormRef.resetFields();
    },
    submitAddForm() {
      this.$refs.addFormRef.validate((valid) => {
        if (!valid) return;
        const data = {
          productId: this.addForm.productId,
          type: this.activeTab,
        };
        addProductDelivery(data)
          .then(() => {
            this.$message.success('添加成功');
            this.addDialogVisible = false;
            this.getList();
          })
          .catch(() => {});
      });
    },
    /**
     * 上传前校验
     */
    beforeImportUpload(file) {
      const isExcel =
        file.type === 'application/vnd.ms-excel' ||
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isExcel) {
        this.$message.error('只能上传Excel文件');
      }
      return isExcel;
    },
    /**
     * 处理Excel上传
     */
    async handleImportUpload(param) {
      this.importLoading = true;
      const formData = new FormData();
      formData.append('file', param.file);
      formData.append('type', this.activeTab);
      try {
        const res = await batchImportProductDelivery(formData);
        // 直接处理后端返回的 message，因为文件上传成功后，后端可能返回JSON
        if (res && res.message) {
          this.$message.success(res.message);
        } else {
          this.$message.success('批量导入成功');
        }
        this.importDialogVisible = false;
        this.getList();
      } catch (e) {
        this.$message.error(e.message || '批量导入失败');
      } finally {
        this.importLoading = false;
      }
    },
    // 导出订单列表
    handleExport() {
      // 防止重复点击
      if (this.exportLoading) {
        return;
      }

      this.$confirm('确认要导出商品列表吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 设置loading状态
          this.exportLoading = true;
          this.$modal.loading('正在导出商品列表，请稍候...');

          // 调用后端导出接口
          const params = {
            type: this.activeTab,
          };
          // 引入SettingMer
          import('@/utils/settingMer')
            .then(({ default: SettingMer }) => {
              exportProductDelivery(params)
                .then((res) => {
                  console.log('返回文件下载地址', res);
                  if (res) {
                    // 拼接主请求路径，使用正确的静态文件访问前缀
                    const downloadUrl = SettingMer.httpUrl + '/' + res;
                    window.open(downloadUrl);
                    this.$message.success('导出成功，文件开始下载');
                  } else {
                    this.$message.error('导出失败，未获取到下载链接');
                  }
                })
                .catch((e) => {
                  console.log(e);
                  this.$message.error('导出失败');
                })
                .finally(() => {
                  // 关闭loading状态
                  this.exportLoading = false;
                  this.$modal.closeLoading();
                });
            })
            .catch((e) => {
              console.log(e);
              this.$message.error('导出失败');
              this.exportLoading = false;
              this.$modal.closeLoading();
            });
        })
        .catch(() => {
          this.$message.info('已取消导出');
        });
    },
  },
};
</script>

<style scoped lang="scss">
.delivery-page {
  background: #fff;
  padding: 24px;
}
.search-form {
  margin: 20px 0 0 0;
}
.mt20 {
  margin-top: 20px;
}
.block {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}
::v-deep .el-tabs__header {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e7ed;
}
.center-dialog-title ::v-deep .el-dialog__header {
  display: flex;
  justify-content: center;
}
.center-footer {
  display: flex;
  justify-content: center;
}
</style>
