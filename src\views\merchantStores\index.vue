<template>
  <div class="divBox">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <el-form :inline="true" :model="searchForm" @submit.native.prevent>
          <el-form-item label="选择时间：">
            <div class="time-filter">
              <el-radio-group v-model="searchForm.timeRange" @change="handleTimeChange">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="today">今天</el-radio-button>
                <el-radio-button label="yesterday">昨天</el-radio-button>
                <el-radio-button label="last7days">最近7天</el-radio-button>
                <el-radio-button label="last30days">最近30天</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">本年</el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleDateRangeChange"
              />
            </div>
          </el-form-item>
          <el-form-item label="门店地区：">
            <el-cascader
              :options="cityTree"
              :show-all-levels="true"
              :props="cityProps"
              v-model="searchForm.district"
              class="selWidth"
              @change="handleInputChange"
              clearable
            >
            </el-cascader>
          </el-form-item>
          <el-form-item label="门店名称：">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入门店名称"
              clearable
              class="selWidth"
              @input="handleInputChange"
              @clear="handleInputChange"
            />
          </el-form-item>
        </el-form>
      </div>

      <el-tabs v-model="storeStatusTab" @tab-click="handleStoreStatusChange">
        <el-tab-pane :label="`在运营门店 (${activeStoresCount})`" name="active"></el-tab-pane>
        <el-tab-pane :label="`已关闭门店 (${closedStoresCount})`" name="closed"></el-tab-pane>
      </el-tabs>

      <el-button type="primary" class="export-btn" @click="handleAddStore"> 新增门店 </el-button>
      <el-button v-hasPermi="['platform:stores:export']" type="primary" class="export-btn" @click="handleExport">
        导出门店列表
      </el-button>

      <el-table v-loading="listLoading" :data="tableData" style="width: 100%" size="small" class="mt20">
        <el-table-column prop="id" label="ID" min-width="50" />
        <el-table-column prop="name" label="门店名称" min-width="120" />
        <el-table-column prop="merchantName" label="所属代理商" min-width="120" />
        <el-table-column label="所属地区" min-width="120">
          <template slot-scope="scope"> {{ scope.row.province }}{{ scope.row.city }}{{ scope.row.district }} </template>
        </el-table-column>
        <el-table-column prop="addressDetail" label="门店地址" min-width="200" />
        <el-table-column label="创建时间" min-width="150">
          <template slot-scope="scope">
            {{ scope.row.createTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['platform:stores:detail']"
              type="text"
              size="small"
              @click="handleDetail(scope.row)"
            >
              查看
            </el-button>
            <el-button v-hasPermi="['platform:stores:update']" type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              v-hasPermi="['platform:stores:delete']"
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button type="text" size="small" @click="handleDeliverySetting(scope.row)"> 配送方式 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[10, 20, 30, 40]"
          :page-size="searchForm.limit"
          :current-page="searchForm.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 门店弹窗组件 -->
    <store-dialog
      ref="storeDialog"
      :dialog-visible.sync="dialogVisible"
      :store-id="currentStoreId"
      :is-view="isView"
      :merchant-options="merchantOptions"
      @success="getList"
    />

    <el-dialog title="配送方式设置" :visible.sync="deliveryDialogVisible" width="400px">
      <el-form :model="deliveryForm" label-width="120px">
        <el-form-item label="开启自提">
          <el-switch v-model="deliveryForm.selfPickup"></el-switch>
        </el-form-item>
        <el-form-item label="开启配送">
          <el-switch v-model="deliveryForm.delivery"></el-switch>
        </el-form-item>
        <el-form-item label="开启快递">
          <el-switch v-model="deliveryForm.express"></el-switch>
        </el-form-item>
        <el-form-item label="开始公里数">
          <el-input
            v-model.number="deliveryForm.kmStart"
            placeholder="请输入开始公里数"
            type="number"
            :min="0"
            @input="onKmStartInput"
          ></el-input>
        </el-form-item>
        <el-form-item label="结束公里数">
          <el-input
            v-model.number="deliveryForm.kmEnd"
            placeholder="请输入结束公里数"
            type="number"
            :min="0"
            @input="onKmEndInput"
          ></el-input>
        </el-form-item>
        <el-form-item label="配送费">
          <el-input
            v-model="deliveryForm.amount"
            placeholder="请输入配送费"
            type="number"
            :min="0"
            step="0.01"
            @input="onAmountInput"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deliveryDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleDeliveryDialogConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { storesListApi, storesDeleteApi, storesDetailApi, updateDeliveryApi } from '@/api/merchantStores';
import StoreDialog from './storeDialog.vue';
import _ from 'lodash';
import { cityListTree } from '@/api/logistics';
import { export_json_to_excel } from '@/vendor/Export2Excel';

export default {
  name: 'merchantStoresList',
  filters: {
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      const second = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
  },
  components: {
    StoreDialog,
  },
  data() {
    return {
      searchForm: {
        page: 1,
        limit: 10,
        merId: '',
        name: '',
        type: '',
        openStatus: '',
        timeRange: 'all',
        merchantName: '',
        region: '',
        startTime: '',
        endTime: '',
        district: [],
      },
      dateRange: [],
      listLoading: false,
      tableData: [],
      total: 0,
      typeOptions: [
        { value: 1, label: '直营' },
        { value: 2, label: '旗舰' },
        { value: 3, label: '专卖' },
        { value: 4, label: '普通' },
      ],
      statusOptions: [
        { value: 1, label: '营业中' },
        { value: 0, label: '已关闭' },
      ],
      merchantOptions: [],
      dialogVisible: false,
      currentStoreId: '',
      isView: false,
      activeTab: 'stores',
      storeStatusTab: 'active',
      activeStoresCount: 0,
      closedStoresCount: 0,
      cityTree: [],
      cityProps: {
        children: 'child',
        label: 'regionName',
        value: 'regionName',
      },
      deliveryDialogVisible: false,
      deliveryForm: {
        selfPickup: false,
        delivery: false,
        express: false,
        kmStart: '',
        kmEnd: '',
        amount: '',
      },
      currentDeliveryStore: null,
    };
  },
  created() {
    this.getList();
    // 创建防抖函数
    this.debouncedGetList = _.debounce(this.getList, 500);
  },
  mounted() {
    let cityList = localStorage.getItem('cityList');
    if (cityList) {
      cityList = JSON.parse(cityList);
      this.cityTree = cityList;
    } else {
      this.getCityList();
    }
  },
  methods: {
    /**
     * 获取城市列表
     */
    getCityList() {
      cityListTree()
        .then((tree) => {
          let cityTree = this.changeNodes(tree);
          localStorage.setItem('cityList', JSON.stringify(cityTree));
          this.cityTree = cityTree;
        })
        .catch((err) => {
          this.$message.error(res.message);
        });
    },
    changeNodes(data) {
      if (data.length > 0) {
        for (var i = 0; i < data.length; i++) {
          if (!data[i].child || data[i].child.length < 1) {
            data[i].child = undefined;
          } else {
            this.changeNodes(data[i].child);
          }
        }
      }
      return data;
    },
    // 获取门店列表
    getList() {
      console.log('searchForm', this.searchForm);
      if (this.searchForm.district && this.searchForm.district.length > 0) {
        this.searchForm.city = this.searchForm.district[this.searchForm.district.length - 2];
        this.searchForm.district = this.searchForm.district[this.searchForm.district.length - 1];
      }
      this.listLoading = true;
      // 根据状态标签页设置查询条件
      if (this.storeStatusTab === 'active') {
        this.searchForm.openStatus = 1;
      } else if (this.storeStatusTab === 'closed') {
        this.searchForm.openStatus = 0;
      }

      storesListApi(this.searchForm)
        .then((res) => {
          console.log('门店列表响应', res);
          this.tableData = res.list;
          this.total = res.total;
          this.activeStoresCount = res.activeStoresCount || 0;
          this.closedStoresCount = res.closedStoresCount || 0;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    // 查询
    searchList() {
      this.searchForm.page = 1;
      this.getList();
    },
    // 重置
    resetSearch() {
      this.dateRange = [];
      this.searchForm = {
        page: 1,
        limit: 10,
        merId: '',
        name: '',
        type: '',
        openStatus: this.storeStatusTab === 'active' ? 1 : 0,
        timeRange: 'all',
        merchantName: '',
        region: '',
        startTime: '',
        endTime: '',
        district: [],
      };
      this.getList();
    },
    // 每页数量改变
    handleSizeChange(val) {
      this.searchForm.limit = val;
      this.getList();
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.searchForm.page = val;
      this.getList();
    },
    // 新增门店
    handleAddStore() {
      this.currentStoreId = '';
      this.isView = false;
      this.dialogVisible = true;
    },
    // 导出门店列表
    handleExport() {
      this.$confirm('确认要导出门店列表吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 设置表头
          const tHeader = ['ID', '门店名称', '所属代理商', '所属地区', '门店地址', '创建时间'];
          // 处理数据
          const data = this.tableData.map((item) => {
            // 格式化时间
            const formatDateTime = (dateTimeStr) => {
              if (!dateTimeStr) return '';
              const date = new Date(dateTimeStr);
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hour = String(date.getHours()).padStart(2, '0');
              const minute = String(date.getMinutes()).padStart(2, '0');
              const second = String(date.getSeconds()).padStart(2, '0');
              return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            };

            return [
              item.id,
              item.name,
              item.merchantName,
              `${item.province}${item.city}${item.district}`,
              item.addressDetail,
              formatDateTime(item.createTime),
            ];
          });
          // 导出文件名
          const filename = `门店列表_${new Date().getTime()}`;
          // 导出数据
          export_json_to_excel({
            header: tHeader,
            data: data,
            filename: filename,
            autoWidth: true,
            bookType: 'xlsx',
          });
        })
        .catch(() => {
          this.$message.info('已取消导出');
        });
    },
    // 查看门店详情
    handleDetail(row) {
      this.loading = true;
      storesDetailApi(row.id)
        .then((res) => {
          console.log('门店详情响应', res);
          if (res) {
            this.currentStoreId = row.id;
            this.isView = true;
            this.dialogVisible = true;
            this.$nextTick(() => {
              if (this.$refs.storeDialog) {
                this.$refs.storeDialog.setStoreDetail(res);
              }
            });
          } else {
            this.$message.error('获取门店详情数据为空');
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$message.error('获取门店详情失败');
        });
    },
    // 编辑门店
    handleEdit(row) {
      this.loading = true;
      storesDetailApi(row.id)
        .then((res) => {
          console.log('编辑门店详情响应', res);
          if (res) {
            this.currentStoreId = row.id;
            this.isView = false;
            this.dialogVisible = true;
            this.$nextTick(() => {
              if (this.$refs.storeDialog) {
                this.$refs.storeDialog.setStoreDetail(res);
              }
            });
          } else {
            this.$message.error('获取门店详情数据为空');
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$message.error('获取门店详情失败');
        });
    },
    // 删除门店
    handleDelete(row) {
      this.$confirm('确定要删除该门店吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          storesDeleteApi(row.id).then((res) => {
            this.$message.success('删除成功');
            this.getList();
          });
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },
    // 时间范围选择改变
    handleTimeChange() {
      // 根据选择的时间范围设置日期
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      switch (this.searchForm.timeRange) {
        case 'all':
          this.dateRange = [];
          this.searchForm.startTime = '';
          this.searchForm.endTime = '';
          break;
        case 'today':
          this.dateRange = [today, today];
          this.searchForm.startTime = this.formatDate(today);
          this.searchForm.endTime = this.formatDate(today);
          break;
        case 'yesterday':
          this.dateRange = [yesterday, yesterday];
          this.searchForm.startTime = this.formatDate(yesterday);
          this.searchForm.endTime = this.formatDate(yesterday);
          break;
        case 'last7days':
          const last7days = new Date(today);
          last7days.setDate(last7days.getDate() - 6);
          this.dateRange = [last7days, today];
          this.searchForm.startTime = this.formatDate(last7days);
          this.searchForm.endTime = this.formatDate(today);
          break;
        case 'last30days':
          const last30days = new Date(today);
          last30days.setDate(last30days.getDate() - 29);
          this.dateRange = [last30days, today];
          this.searchForm.startTime = this.formatDate(last30days);
          this.searchForm.endTime = this.formatDate(today);
          break;
        case 'month':
          const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          this.dateRange = [firstDayOfMonth, today];
          this.searchForm.startTime = this.formatDate(firstDayOfMonth);
          this.searchForm.endTime = this.formatDate(today);
          break;
        case 'year':
          const firstDayOfYear = new Date(now.getFullYear(), 0, 1);
          this.dateRange = [firstDayOfYear, today];
          this.searchForm.startTime = this.formatDate(firstDayOfYear);
          this.searchForm.endTime = this.formatDate(today);
          break;
      }

      this.searchForm.page = 1;
      this.getList();
    },
    // 日期范围选择改变
    handleDateRangeChange(val) {
      if (val) {
        this.searchForm.startTime = this.formatDate(val[0]);
        this.searchForm.endTime = this.formatDate(val[1]);
        this.searchForm.timeRange = ''; // 自定义日期时清除预设选项
      } else {
        this.searchForm.startTime = '';
        this.searchForm.endTime = '';
        this.searchForm.timeRange = 'all';
      }
      this.searchForm.page = 1;
      this.getList();
    },
    // 门店状态标签页切换
    handleStoreStatusChange() {
      this.searchForm.page = 1;
      this.getList();
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    // 输入框变化处理
    handleInputChange() {
      this.searchForm.page = 1;
      // 使用防抖函数
      this.debouncedGetList();
    },
    async handleDeliverySetting(row) {
      // 先查详情接口，获取数据库中的配送方式和配送费
      try {
        const res = await storesDetailApi(row.id);
        console.log('门店详情响应', res);
        // 假设res.data为后端返回的门店详情对象
        const data = res || {};
        // 处理配送方式
        let selfPickup = false,
          delivery = false,
          express = false;
        if (Array.isArray(data.storesDeliveryList)) {
          data.storesDeliveryList.forEach((item) => {
            if (item.type === 1) express = true;
            if (item.type === 2) selfPickup = true;
            if (item.type === 3) delivery = true;
          });
        }
        this.currentDeliveryStore = { ...row, id: data.id };
        this.deliveryForm = {
          selfPickup,
          delivery,
          express,
          kmStart: data.storesShippingFees.kmStart || '',
          kmEnd: data.storesShippingFees.kmEnd || '',
          amount: data.storesShippingFees.amount || '',
        };
        this.deliveryDialogVisible = true;
      } catch (e) {
        this.$message.error('获取门店配送信息失败');
      }
    },
    handleDeliveryDialogConfirm() {
      // 校验三个开关至少有一个为true
      if (!this.deliveryForm.selfPickup && !this.deliveryForm.delivery && !this.deliveryForm.express) {
        this.$message.error('自提、配送、快递至少开启一个！');
        return;
      }
      // 组装storesDeliveryList
      const storesDeliveryList = [];
      const storeId = this.currentDeliveryStore.id;
      if (this.deliveryForm.express) {
        storesDeliveryList.push({ storeId, type: 1 });
      }
      if (this.deliveryForm.selfPickup) {
        storesDeliveryList.push({ storeId, type: 2 });
      }
      if (this.deliveryForm.delivery) {
        storesDeliveryList.push({ storeId, type: 3 });
      }
      // 组装请求参数
      const params = {
        storeId,
        kmStart: this.deliveryForm.kmStart,
        kmEnd: this.deliveryForm.kmEnd,
        amount: this.deliveryForm.amount,
        storesDeliveryList,
      };
      // 调用接口提交params
      updateDeliveryApi(params)
        .then((res) => {
          this.$message.success('配送方式设置已保存');
          this.deliveryDialogVisible = false;
          this.getList();
        })
        .catch(() => {
          this.$message.error('配送方式设置失败');
        });
    },
    onKmStartInput(val) {
      // 只允许输入正整数
      let v = String(val).replace(/[^\d]/g, '');
      this.deliveryForm.kmStart = v ? parseInt(v, 10) : '';
    },
    onKmEndInput(val) {
      // 只允许输入正整数
      let v = String(val).replace(/[^\d]/g, '');
      this.deliveryForm.kmEnd = v ? parseInt(v, 10) : '';
    },
    onAmountInput(val) {
      // 只允许输入数字和两位小数
      let v = String(val)
        .replace(/[^\d.]/g, '')
        .replace(/^(\d+)(\.\d{0,2})?.*$/, '$1$2');
      this.deliveryForm.amount = v;
    },
  },
};
</script>

<style scoped lang="scss">
.selWidth {
  width: 300px;
}

.time-filter {
  display: flex;
  align-items: center;

  .el-radio-group {
    margin-right: 10px;
  }
}

.mt20 {
  margin-top: 20px;
}

.block {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 导出按钮样式
.export-btn {
  border-radius: 4px;
  border: 1px solid #0084ff;
  background-color: #fff;
  color: #0084ff;
  padding: 9px 15px;
  font-size: 14px;
  margin-top: 10px;
}

// 覆盖el-tabs默认边框
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

// 为tabs头部添加底部边框线
::v-deep .el-tabs__header {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e7ed;
}

// 去掉卡片头部下边框和底部内边距
::v-deep .el-card__header {
  border-bottom: none;
  padding-bottom: 0;
}

// 设置卡片内容区域上边距为0
::v-deep .el-card__body {
  padding-top: 0;
}
</style>
