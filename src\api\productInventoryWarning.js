import request from '@/utils/request';

// 获取商品库存预警分页列表
export function getInventoryWarningList(params) {
  return request({
    url: '/admin/merchant/product/inventorywarning/list',
    method: 'get',
    params,
  });
}

// 获取需要提醒的预警商品列表
export function getNeedRemindList() {
  return request({
    url: '/admin/merchant/product/inventorywarning/need/remind',
    method: 'get',
  });
}

// 更新库存预警状态
export function updateInventoryWarningStatus(id, status) {
  return request({
    url: '/admin/merchant/product/inventorywarning/update/status',
    method: 'post',
    params: { id, status },
  });
}

// 更新提醒状态
export function updateRemindStatus(id, isRemind) {
  return request({
    url: '/admin/merchant/product/inventorywarning/update/remind',
    method: 'post',
    params: { id, isRemind },
  });
}

// 检查并更新商品库存预警状态
export function checkAndUpdateWarningStatus(productId, currentStock) {
  return request({
    url: '/admin/merchant/product/inventorywarning/check',
    method: 'post',
    params: { productId, currentStock },
  });
}
