import request from '@/utils/request';

// 获取商户分类轮播图分页列表
export function getCategoryCarouselList(params) {
  return request({
    url: '/admin/merchant/categoryCarousel/list',
    method: 'get',
    params,
  });
}

// 新增商户分类轮播图
export function addCategoryCarousel(data) {
  return request({
    url: '/admin/merchant/categoryCarousel/add',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 编辑商户分类轮播图
export function updateCategoryCarousel(data) {
  return request({
    url: '/admin/merchant/categoryCarousel/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 删除商户分类轮播图
export function deleteCategoryCarousel(id) {
  return request({
    url: `/admin/merchant/categoryCarousel/delete/${id}`,
    method: 'post',
  });
}
