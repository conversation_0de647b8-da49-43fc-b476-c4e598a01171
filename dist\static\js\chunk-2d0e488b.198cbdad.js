(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e488b"],{9141:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("upload-picture",{attrs:{pictureType:t.pictureType}})],1)],1)},c=[],i={name:"index",data:function(){return{pictureType:"maintain"}}},r=i,u=a("2877"),s=Object(u["a"])(r,n,c,!1,null,"3d0f7c66",null);e["default"]=s.exports}}]);